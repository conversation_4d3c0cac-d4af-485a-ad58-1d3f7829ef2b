using QFramework;
using TFGShare.Protocol;

namespace GameServer.Battle.State
{
    /// <summary>
    /// 比赛前准备状态
    /// </summary>
    public class PreMatchState : BattleStateBase
    {
        public PreMatchState(BattleStateMachine stateMachine) : base(stateMachine) { }
        
        public override void Enter()
        {
            base.Enter();
            
            // 初始化战斗数据
            BattleData.CurrentRound = 0;
            BattleData.Player1Wins = 0;
            BattleData.Player2Wins = 0;
            
            // 检查是否有玩家断线，如果有则等待重连
            if (BattleModel.IsBattleWaitingForReconnection(BattleId))
            {
                LogKit.I($"[PreMatchState] 检测到断线玩家，等待重连完成: BattleId={BattleId}");
                return;
            }
            
            // 等待客户端资源加载完成
            // 不再直接进入下一个状态，而是等待客户端资源准备完成消息
            LogKit.I($"[PreMatchState] 等待客户端资源加载: BattleId={BattleId}");
        }
        
        /// <summary>
        /// 处理客户端资源准备完成
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        public void HandlePlayerResourceReady(string playerId)
        {
            // 标记玩家资源准备完成
            if (StateMachine.MarkPlayerResourceReady(playerId,true))
            {
                // 所有玩家都准备好了，进入比赛开始状态
                LogKit.I($"[PreMatchState] 所有玩家资源准备完成，进入比赛开始状态: BattleId={BattleId}");
                StateMachine.ChangeBattleState(MatchState.MatchStart);
            }
        }
        
        /// <summary>
        /// 处理所有玩家重连完成，继续PreMatch流程
        /// </summary>
        public void ContinueAfterReconnection()
        {
            LogKit.I($"[PreMatchState] 所有玩家重连完成，继续PreMatch流程: BattleId={BattleId}");
            
            // 检查所有玩家是否已准备好资源
            if (StateMachine.AreAllPlayersResourceReady())
            {
                LogKit.I($"[PreMatchState] 重连后检查：所有玩家资源已准备完成，进入比赛开始状态: BattleId={BattleId}");
                StateMachine.ChangeBattleState(MatchState.MatchStart);
            }
            else
            {
                LogKit.I($"[PreMatchState] 重连后检查：等待玩家资源加载完成: BattleId={BattleId}");
            }
        }
    }
} 
