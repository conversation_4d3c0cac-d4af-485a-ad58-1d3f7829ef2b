using QFramework;
using GameServer.Battle.Model;
using GameServer.Battle.Event;
using GameServer.Battle.State;
using GameServer.Battle.System;
using TFGShare.Protocol;

namespace GameServer.Battle.Command
{
    /// <summary>
    /// 玩家断线命令 - 符合QFramework架构的无状态命令
    /// </summary>
    public class PlayerDisconnectedCommand : AbstractCommand
    {
        private readonly PlayerDisconnectedEvent _disconnectedEvent;
        
        public PlayerDisconnectedCommand(PlayerDisconnectedEvent disconnectedEvent)
        {
            this._disconnectedEvent = disconnectedEvent;
        }
        
        protected override void OnExecute()
        {
            var battleModel = this.GetModel<IBattleModel>();
            var battleSystem = this.GetSystem<BattleSystem>();
            
            // 获取战斗状态机
            var stateMachine = battleSystem.GetBattleStateMachine(_disconnectedEvent.BattleId);
            if (stateMachine == null)
            {
                LogKit.W($"[HandlePlayerDisconnectedCommand] 找不到战斗状态机: {_disconnectedEvent.BattleId}");
                return;
            }
            
            var battleData = stateMachine.BattleData;
            var currentState = battleData.CurrentState;
            
            if (_disconnectedEvent.IsOverGame)
            {
                LogKit.E($"[HandlePlayerDisconnectedCommand] 服务器玩家断线，结束比赛: {_disconnectedEvent.BattleId}");
                
                stateMachine.ChangeBattleState(MatchState.MatchEnd);
                return;
            }
            
            // 根据当前状态决定如何处理断线
            switch (currentState)
            {
                case MatchState.PreMatch:
                    HandleDisconnectionInPreMatch(battleModel, stateMachine);
                    break;
                case MatchState.RoundStart:
                    HandleDisconnectionInRoundStart(battleModel, stateMachine);
                    break;
                case MatchState.Running:
                    HandleDisconnectionInRunning(battleModel, stateMachine);
                    break;
                case MatchState.IntermissionFinish:
                    HandleDisconnectionInIntermissionFinish(battleModel, stateMachine);
                    break;
            }
        }

        /// <summary>
        /// 处理PreMatch状态下的断线
        /// </summary>
        private void HandleDisconnectionInPreMatch(IBattleModel battleModel, BattleStateMachine stateMachine)
        {
            string battleId = _disconnectedEvent.BattleId;
            stateMachine.MarkPlayerResourceReady(_disconnectedEvent.PlayerId, false);
            // 发送等待重连状态变更事件
            var disconnectedPlayers = battleModel.GetDisconnectedParticipants(battleId);
            this.SendEvent(new WaitingForReconnectionStateChangedEvent(
                battleId, 
                true, 
                disconnectedPlayers.ToArray()
            ));
        }

        /// <summary>
        /// 处理RoundStart状态下的断线
        /// </summary>
        private void HandleDisconnectionInRoundStart(IBattleModel battleModel, BattleStateMachine stateMachine)
        {
            string battleId = _disconnectedEvent.BattleId;
            stateMachine.MarkPlayerRoundStartEffectFinished(_disconnectedEvent.PlayerId,
                battleModel.GetBattle(battleId).CurrentRound, false);
        }

        /// <summary>
        /// 处理Running状态下的断线
        /// </summary>
        private void HandleDisconnectionInRunning(IBattleModel battleModel, BattleStateMachine stateMachine)
        {
            string battleId = _disconnectedEvent.BattleId;
            
            LogKit.I($"[HandlePlayerDisconnectedCommand] Running状态下玩家断线，切换到Timeout状态: {battleId}");
            // 切换到Timeout状态
            stateMachine.ChangeBattleState(MatchState.TimeOut);
            this.SendEvent(new BattlePauseEvent(battleId, true, stateMachine.BattleData.RoundTimer));
        }

        /// <summary>
        /// 处理IntermissionFinish状态下的断线
        /// </summary>
        private void HandleDisconnectionInIntermissionFinish(IBattleModel battleModel, BattleStateMachine stateMachine)
        {
            string battleId = _disconnectedEvent.BattleId;
            stateMachine.MarkPlayerRoundReportFinished(_disconnectedEvent.PlayerId,
                battleModel.GetBattle(battleId).CurrentRound, false);
            // 发送等待重连状态变更事件
            var disconnectedPlayers = battleModel.GetDisconnectedParticipants(battleId);
            this.SendEvent(new WaitingForReconnectionStateChangedEvent(
                battleId, 
                true, 
                disconnectedPlayers.ToArray()
            ));
        }
    }
} 
