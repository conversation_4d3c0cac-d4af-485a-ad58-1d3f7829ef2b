
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;
using Newtonsoft.Json.Linq;



namespace ServerConfig.config
{

public sealed partial class TestTable : Luban.BeanBase
{
    public TestTable(JToken _buf) 
    {
        JObject _obj = _buf as JObject;
        Id = (int)_obj.GetValue("id");
        Desc = (string)_obj.GetValue("desc");
        Count = (int)_obj.GetValue("count");
        XxxTest = (int)_obj.GetValue("xxx_test");
    }

    public static TestTable DeserializeTestTable(JToken _buf)
    {
        return new config.TestTable(_buf);
    }

    /// <summary>
    /// id
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 描述
    /// </summary>
    public readonly string Desc;
    /// <summary>
    /// 个数
    /// </summary>
    public readonly int Count;
    /// <summary>
    /// 测试
    /// </summary>
    public readonly int XxxTest;


    public const int __ID__ = 1821859472;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(ServerTables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "desc:" + Desc + ","
        + "count:" + Count + ","
        + "xxxTest:" + XxxTest + ","
        + "}";
    }
}
}

