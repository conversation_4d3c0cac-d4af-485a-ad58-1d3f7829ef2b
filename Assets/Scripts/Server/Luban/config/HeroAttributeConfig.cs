
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;
using Newtonsoft.Json.Linq;



namespace ServerConfig.config
{

public sealed partial class HeroAttributeConfig : Luban.BeanBase
{
    public HeroAttributeConfig(JToken _buf) 
    {
        JObject _obj = _buf as JObject;
        AttributeID = (int)_obj.GetValue("Attribute_ID");
        AttributeType = (string)_obj.GetValue("Attribute_Type");
        ParameterType = (string)_obj.GetValue("Parameter_Type");
        Count = (float)_obj.GetValue("Count");
    }

    public static HeroAttributeConfig DeserializeHeroAttributeConfig(JToken _buf)
    {
        return new config.HeroAttributeConfig(_buf);
    }

    /// <summary>
    /// 属性Id
    /// </summary>
    public readonly int AttributeID;
    /// <summary>
    /// 属性名称
    /// </summary>
    public readonly string AttributeType;
    /// <summary>
    /// 类型
    /// </summary>
    public readonly string ParameterType;
    /// <summary>
    /// 初始数值
    /// </summary>
    public readonly float Count;


    public const int __ID__ = 1235551448;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(ServerTables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "AttributeID:" + AttributeID + ","
        + "AttributeType:" + AttributeType + ","
        + "ParameterType:" + ParameterType + ","
        + "Count:" + Count + ","
        + "}";
    }
}
}

