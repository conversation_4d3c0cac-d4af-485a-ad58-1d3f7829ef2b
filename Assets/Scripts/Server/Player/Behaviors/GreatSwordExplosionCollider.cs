using System;
using System.Collections.Generic;
using GameServer.Character;
using GameServer.Battle.Event;
using GameServer.System;
using GameShare.Character;
using QFramework;
using TFGShare.Protocol;
using UnitSystem.Unit;
using UnityEngine;

namespace GameServer.Player.Behaviors
{
    /// <summary>
    /// 大剑爆炸碰撞体 - 处理爆炸范围伤害检测
    /// </summary>
    public class GreatSwordExplosionCollider
    {
        private string _greatSwordId;
        private Vector3 _explosionPosition;
        private GreatSwordConfig _config;
        private string _battleId;
        private CombatPlayer _owner;
        
        private float _explosionRadius = 3f; // 爆炸范围半径
        private bool _hasExploded = false;
        
        /// <summary>
        /// 初始化爆炸碰撞体
        /// </summary>
        public void Initialize(string greatSwordId, Vector3 explosionPosition, GreatSwordConfig config, string battleId, CombatPlayer owner)
        {
            _greatSwordId = greatSwordId;
            _explosionPosition = explosionPosition;
            _config = config;
            _battleId = battleId;
            _owner = owner;
            
            LogKit.I($"[GreatSwordExplosionCollider] 爆炸初始化: {_greatSwordId}, 位置: {explosionPosition}");

            TriggerExplosion();
        }
        
        /// <summary>
        /// 触发爆炸伤害检测
        /// </summary>
        private void TriggerExplosion()
        {
            if (_hasExploded) return;
            
            _hasExploded = true;
            
            LogKit.I($"[GreatSwordExplosionCollider] 开始爆炸检测: {_greatSwordId}, 范围: {_explosionRadius}");
            
            // 使用Physics.OverlapCapsule进行范围检测
            Collider[] hitColliders = Physics.OverlapCapsule(_explosionPosition,
                _explosionPosition + Vector3.up * _config.colliderSize.y, _config.colliderSize.x);
            
            List<string> hitTargets = new List<string>();
            List<Vector3> hitPositions = new List<Vector3>();
            
            foreach (var collider in hitColliders)
            {
                var characterCollider = collider.GetComponent<CharacterCollider>();
                if (characterCollider == null) continue;
                
                // 检查是否是可攻击目标
                if (characterCollider.ColliderType != CharacterColliderType.HitBox) continue;
                
                // 避免攻击自己
                if (characterCollider.OwnerPlayerId == _owner.PlayerId) continue;
                
                // 避免重复攻击同一目标
                if (hitTargets.Contains(characterCollider.OwnerPlayerId)) continue;
                
                // 计算击中位置和方向
                Vector3 hitPosition = collider.ClosestPoint(_explosionPosition);
                Vector3 direction = (collider.transform.position - _explosionPosition).normalized;
                
                // 处理伤害
                ProcessExplosionHit(characterCollider, hitPosition, direction);
                
                // 记录击中信息用于广播
                hitTargets.Add(characterCollider.OwnerPlayerId);
                hitPositions.Add(hitPosition);
                
                LogKit.I($"[GreatSwordExplosionCollider] 爆炸击中目标: {characterCollider.OwnerPlayerId}");
            }
            
            // 广播爆炸结果
            BroadcastExplosion(hitTargets, hitPositions);
            
            // 销毁爆炸对象
            // Destroy(gameObject, 0.1f);
        }
        
        /// <summary>
        /// 处理爆炸伤害
        /// </summary>
        private void ProcessExplosionHit(CharacterCollider target, Vector3 hitPosition, Vector3 direction)
        {
            int finalDamage = Mathf.RoundToInt(_config.damage);
            
            // 创建伤害事件
            var damageEvent = new SkillDamageEvent(
                _owner.PlayerId,
                target.OwnerPlayerId,
                finalDamage,
                hitPosition,
                direction,
                HitType.Heavy, // 爆炸造成重击
                target.GameBodyPart,
                GameBodyPart.FireWeapon
            );
            
            // 发送伤害事件到伤害系统
            LaunchMainArch.Interface.SendEvent(damageEvent);
        }
        
        /// <summary>
        /// 广播爆炸结果
        /// </summary>
        private void BroadcastExplosion(List<string> hitTargets, List<Vector3> hitPositions)
        {
            // 广播爆炸消息
            var explosionPacket = new S2CGreatSwordExplosion
            {
                GreatSwordId = _greatSwordId,
                OwnerId = _owner.PlayerId,
                ExplosionPosition = _explosionPosition,
                ExplosionRadius = _explosionRadius,
                HitTargets = hitTargets.ToArray(),
                HitPositions = hitPositions.ToArray()
            };
            
            LaunchMainArch.Interface.GetSystem<RoomSystem>()?.BroadcastToBattleRoom(_battleId, explosionPacket);
            
            LogKit.I($"[GreatSwordExplosionCollider] 广播爆炸结果: 击中目标数={hitTargets.Count}");
        }
    }
}
