using GameServer.Battle.Event;
using UnityEngine;
using UnitSystem.Unit;
using GameServer.Player.Model;
using QFramework;
using NotImplementedException = System.NotImplementedException;

namespace GameServer.Player.Behaviors
{
    /// <summary>
    /// 被动技能抽象基类
    /// 被动技能特点：
    /// 1. 无冷却时间
    /// 2. 在初始化时自动开始运行
    /// 3. 不通过Execute方法执行主要逻辑
    /// 4. 主要逻辑在Update方法中持续执行
    /// 5. 始终处于可用状态
    /// </summary>
    public abstract class PassiveBehavior : PlayerBehavior, ICanGetSystem
    {
        protected PassiveBehavior(string behaviorName, string playerId, CombatPlayer combatPlayer) 
            : base(behaviorName, playerId, combatPlayer)
        {
        }

        /// <summary>
        /// 被动技能无冷却时间
        /// </summary>
        protected override sealed float GetCooldownTime()
        {
            return 0f;
        }

        /// <summary>
        /// 被动技能始终可用
        /// </summary>
        protected override sealed bool ValidateConditions(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            return true;
        }

        /// <summary>
        /// 被动技能不通过Execute执行主要逻辑
        /// 子类如有特殊需求可重写
        /// </summary>
        protected override sealed void Execute(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            // 被动技能无需主动执行逻辑
            OnPassiveExecute(startPos, startDir, targetPos);
        }

        /// <summary>
        /// 被动技能的Execute回调，子类可选择重写
        /// </summary>
        protected virtual void OnPassiveExecute(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            // 默认为空，子类可根据需要重写
        }

        /// <summary>
        /// 被动技能初始化时自动开始运行
        /// </summary>
        protected override void OnInitialize()
        {
            base.OnInitialize();
            
            // 被动技能自动开始运行
            IsRunning = true;
            StartAutoUpdate();
            // 调用子类的被动初始化逻辑
            OnPassiveInitialize();
        }


        /// <summary>
        /// 被动技能专用的初始化方法，子类重写此方法
        /// </summary>
        protected abstract void OnPassiveInitialize();

        /// <summary>
        /// 被动技能的主要逻辑更新，子类必须实现
        /// </summary>
        protected override abstract void OnUpdate(float deltaTime);
        

        /// <summary>
        /// 重写Dispose方法，确保被动技能能正确释放
        /// </summary>
        public override void Dispose()
        {
            if (IsRunning)
            {
                Stop();
            }
            OnPassiveDispose();
            base.Dispose();
        }
        
        

        /// <summary>
        /// 被动技能释放时的回调，子类可选择重写
        /// </summary>
        protected virtual void OnPassiveDispose()
        {
            // 默认为空，子类可根据需要重写
        }
    }
} 
