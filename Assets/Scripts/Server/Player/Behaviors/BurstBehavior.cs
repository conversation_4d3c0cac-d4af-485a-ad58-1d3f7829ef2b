using System;
using UnityEngine;
using UnitSystem.Unit;
using GameServer.Player.Model;
using TFG.Shared;
using QFramework;
using QFrameworkIntegration;
using TFGShare.Protocol;
using TFGShared.Const;
using GameServer;
using GameServer.Battle.Model;
using GameServer.Player.Statuses;
using GameServer.System;
using GameShare.Networking.Model;
using UnitSystem.Unit.Attribute;

namespace GameServer.Player.Behaviors
{
    /// <summary>
    /// 爆气技能实现
    /// 严格按照BurstSkill的原始逻辑实现
    /// 
    /// TODO: 未来将持有BurstBehaviorConfig类，包含爆气技能的所有配置数据：
    /// - 冷却时间、进入爆气所需怒气、持续消耗率、自然恢复速度等
    /// </summary>
    public class BurstBehavior : PlayerBehavior
    {
        private float m_EnergyTimer = 0f;
        private float m_burstTimer = 0f;

        public BurstBehavior(string playerId, CombatPlayer combatPlayer) 
            : base("BurstBehavior", playerId, combatPlayer)
        {
        }

        protected override float GetCooldownTime()
        {
            return 0.0f; // 爆气技能冷却时间很短
        }

        public override bool CanExecute(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            if (!IsCooldownComplete()) return false;
            return ValidateConditions(startPos, startDir, targetPos);
        }

        protected override bool ValidateConditions(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            float startEnergyValue = CombatPlayer.GetStartEnergyValue();
            return !CombatPlayer.IsBursting && CanConsume(startEnergyValue);
        }

        protected override void Execute(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            TryEnterBurstMode();
            // LaunchMainArch.Interface.SendCommand(
            //     new AttachStatusCommand(
            //         CombatPlayer.PlayerId,
            //         CombatPlayer.PlayerId,
            //         new BroadcastEffectStatus(
            //             CombatPlayer.PlayerId,
            //             BroadcastStatusConst.BroadcastBurstEffectStatus),
            //         null));
            
            var battleId = LaunchMainArch.Interface.GetModel<IBattleModel>().GetPlayerBattle(CombatPlayer.PlayerId).BattleId;
            var packet = new S2CSpawnEffects()
            {
                PlayerId = CombatPlayer.PlayerId,
                StatusName = "BroadcastBurstEffectStatus",
                BuffId = "BroadcastBurstEffectStatus-id",
                SkillName = "BasicCombat",
                LifeType = CombatPlayerStatusLifeType.RealTime
            };
            LaunchMainArch.Interface.GetSystem<RoomSystem>().BroadcastToBattleRoom(battleId, packet);
        }
        protected override void OnInitialize()
        {
            base.OnInitialize();
            ResetEnergy();
            StartAutoUpdate();
            
            IsRunning = true;
        }

        protected override void OnDispose()
        {
            IsRunning = false;
            base.OnDispose();
        }

        public override void Stop()
        {
            if (IsRunning)
            {
                //IsRunning = false;
                OnStop();
            }
        }

        protected override void OnStop()
        {
            ExitBurstMode(true);
            Debug.Log("爆气结束 OnStop");
            // LaunchMainArch.Interface.SendCommand(
            //     new UnAttachStatusCommand(
            //         CombatPlayer.PlayerId,
            //         CombatPlayer.PlayerId,
            //         BroadcastStatusConst.BroadcastBurstEffectStatus,
            //         null));
            
            // 获取战斗ID用于房间广播
            var battleData = LaunchMainArch.Interface.GetModel<IBattleModel>().GetPlayerBattle(CombatPlayer.PlayerId);
            var packet = new S2CDespawnEffects()
            {
                PlayerId = CombatPlayer.PlayerId,
                StatusName = "BroadcastBurstEffectStatus",
                BuffId = "BroadcastBurstEffectStatus-id",
                SkillName = "BasicCombat",
                LifeType = CombatPlayerStatusLifeType.RealTime // 使用状态的实际LifeType
            };
            // 如果在战斗中，向战斗房间广播
            LaunchMainArch.Interface.GetSystem<RoomSystem>().BroadcastToBattleRoom(battleData.BattleId, packet);
        }
        public override void Update(float deltaTime)
        {
            if (PlayerId.IsNullOrEmpty())
            {
                return;
            }
            
            var data = LaunchMainArch.Interface.GetModel<IBattleModel>().GetPlayerBattle(PlayerId);
            if (data != null && data.CurrentState == MatchState.Running)
            {
                UpdateEnergy();
            }
        }
        
        public void ResetEnergy()
        {
            CombatPlayer.IsBursting = false;
            m_EnergyTimer = 0f;
            m_burstTimer = 0f;
        }

        #region 怒气更新逻辑

        private void UpdateEnergy()
        {
            AutoGainEnergy();
            Bursting();
        }
        private void AutoGainEnergy()
        {
            if (CombatPlayer.IsBursting)
            {
                return;
            }
            
            if (CombatPlayer.GetCurrentEnergy() < CombatPlayer.GetMaxEnergy())
            {
                m_EnergyTimer += Time.deltaTime;
                float updateFrequency = CombatPlayer.GetUpdateEnergyFrequency();
                if (m_EnergyTimer >= updateFrequency)
                {
                    var gain = CombatPlayer.GetNaturalGainPerSecond() * updateFrequency;
                    LaunchMainArch.Interface.SendCommand(new GainEnergyCommand(CombatPlayer.PlayerId, gain, "AutoRegen"));
                    m_EnergyTimer = 0f;
                }
            }
        }
        private void Bursting()
        {
            if (CombatPlayer.IsBursting)
            {
                m_burstTimer += Time.deltaTime;
                float updateFrequency = CombatPlayer.GetUpdateEnergyFrequency();
                if (m_burstTimer >= updateFrequency)
                {
                    float cost = CombatPlayer.GetEnergyNaturalCostPerSecond() * updateFrequency;
                    m_burstTimer = 0f;
                    Debug.Log("Bursting " + CombatPlayer.GetCurrentEnergy());
                    var tryConsumeEnergy = LaunchMainArch.Interface.SendCommand(
                        new ConsumeEnergyCommand(CombatPlayer.PlayerId, cost, "BurstMaintain"));
                    
                    if (!tryConsumeEnergy)
                    {
                        ExitBurstMode();
                    }
                }
            }
        }

        #endregion

        #region 怒气值管理
        public bool CanConsume(float amount)
        {
            return CombatPlayer.HasEnoughEnergy(amount);
        }

        #endregion

        #region 爆气系统
        public bool TryEnterBurstMode()
        {
            float startEnergyValue = CombatPlayer.GetStartEnergyValue();
            if (!CombatPlayer.IsBursting && CanConsume(startEnergyValue))
            {
                LaunchMainArch.Interface.SendCommand(
                    new ConsumeEnergyCommand(CombatPlayer.PlayerId, startEnergyValue, "BurstEnter"));
                CombatPlayer.IsBursting = true;
                Debug.Log("进入爆气状态");
                return true;
            }
            return false;
        }
        public void ExitBurstMode(bool isExitByPlayer = false)
        {
            if (CombatPlayer.IsBursting)
            {
                CombatPlayer.IsBursting = false;
                Debug.Log("退出爆气状态");
                
                LaunchMainArch.Interface.GetModel<INetworkModel>().BroadcastPacket(new S2CStopSkillResult()
                {
                    PlayerId = PlayerId,
                    BehaviorName = BehaviorName,
                    IsSuccess = true,
                    IsStopByPlayer = isExitByPlayer
                });

                m_burstTimer = 0f;
            }
        }

        #endregion
    }
} 
 
