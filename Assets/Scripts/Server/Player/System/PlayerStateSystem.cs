using QFramework;

namespace GameServer.Player.System
{
    /// <summary>
    /// 玩家状态系统实现类
    /// </summary>
    public class PlayerStateSystem : AbstractSystem, IPlayerStateSystem
    {
        private PlayerState _currentState = PlayerState.Offline;
        private bool _isOnline;
        
        protected override void OnInit()
        {
            _currentState = PlayerState.Offline;
            _isOnline = false;
        }

        public PlayerState GetCurrentState()
        {
            return _currentState;
        }

        public void UpdateState(PlayerState newState)
        {
            if (_currentState == newState) return;
            
            var oldState = _currentState;
            _currentState = newState;
            
            // 发送状态变更事件
            this.SendEvent(new PlayerStateChangedEvent
            {
                OldState = oldState,
                NewState = newState
            });
        }

        public bool IsOnline()
        {
            return _isOnline;
        }

        public void SetOnline(bool isOnline)
        {
            if (_isOnline == isOnline) return;
            
            _isOnline = isOnline;
            UpdateState(isOnline ? PlayerState.Online : PlayerState.Offline);
        }
    }
    
    /// <summary>
    /// 玩家状态变更事件
    /// </summary>
    public class PlayerStateChangedEvent
    {
        public PlayerState OldState { get; set; }
        public PlayerState NewState { get; set; }
    }
} 