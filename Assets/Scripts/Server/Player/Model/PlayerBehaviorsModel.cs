using System.Collections.Generic;
using System.Linq;
using QFramework;

namespace GameServer.Player.Model
{
    /// <summary>
    /// 玩家技能模型实现
    /// </summary>
    public class PlayerBehaviorsModel : AbstractModel, IPlayerBehaviorsModel
    {
        private readonly Dictionary<string, PlayerBehavior> _behaviors = new();
        private readonly Dictionary<string, PlayerBehavior> _runningBehaviors = new();
        private readonly Dictionary<string, List<PlayerBehavior>> _categoryCache = new();

        public IReadOnlyDictionary<string, PlayerBehavior> AllBehaviors => _behaviors;
        public IReadOnlyDictionary<string, PlayerBehavior> RunningBehaviors => _runningBehaviors;
        public IReadOnlyDictionary<string, List<PlayerBehavior>> CategoryCache => _categoryCache;

        protected override void OnInit()
        {
        }

        public void AddBehavior(PlayerBehavior behavior)
        {
            if (_behaviors.ContainsKey(behavior.BehaviorName))
                return;

            _behaviors[behavior.BehaviorName] = behavior;
        }

        public bool RemoveBehavior(string behaviorName)
        {
            if (_behaviors.TryGetValue(behaviorName, out var behavior))
            {
                _behaviors.Remove(behaviorName);
                _runningBehaviors.Remove(behaviorName);
                behavior.Dispose();
                return true;
            }
            return false;
        }

        public PlayerBehavior GetBehavior(string behaviorName)
        {
            _behaviors.TryGetValue(behaviorName, out var behavior);
            return behavior;
        }

        public List<PlayerBehavior> GetBehaviorsWithCategory(string category)
        {
            _categoryCache.TryGetValue(category, out var behaviors);
            return behaviors ?? new List<PlayerBehavior>();
        }

        public void AddRunningBehavior(PlayerBehavior behavior)
        {
            _runningBehaviors[behavior.BehaviorName] = behavior;
        }

        public bool RemoveRunningBehavior(string behaviorName)
        {
            return _runningBehaviors.Remove(behaviorName);
        }

        public void ClearRunningBehaviors()
        {
            _runningBehaviors.Clear();
        }

        public bool HasRunningBehavior()
        {
            return _runningBehaviors.Count > 0;
        }
        
    }
} 
