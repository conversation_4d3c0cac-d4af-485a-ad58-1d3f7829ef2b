using System;
using UnityEngine;
using UnitSystem.Unit;
using QFramework;

namespace GameServer.Player.Model
{

    /// 设计说明：
    /// 1. 每个具体技能将持有自己独有的Config类（如BlockBehaviorConfig、FireballBehaviorConfig等）
    public abstract class  PlayerBehavior : ICanRegisterEvent
    {
        /// <summary>
        /// 技能名称
        /// </summary>
        public string BehaviorName { get; protected set; }
        
        /// <summary>
        /// 玩家ID
        /// </summary>
        public string PlayerId { get; protected set; }
        
        /// <summary>
        /// 冷却时间
        /// TODO: 未来将从各技能的独有Config中获取，而不是通过GetCooldownTime()方法
        /// </summary>
        public float CooldownTime { get; protected set; }
        
        /// <summary>
        /// 上次使用时间戳
        /// </summary>
        public float LastUseTime { get; protected set; }
        
        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning { get; protected set; }
        
        /// <summary>
        /// ActionKit自动更新注册器
        /// </summary>
        private IUnRegister _updateRegister;
        
        /// <summary>
        /// 关联的战斗玩家
        /// </summary>
        protected CombatPlayer CombatPlayer { get; private set; }

        protected PlayerBehavior(string behaviorName, string playerId, CombatPlayer combatPlayer)
        {
            BehaviorName = behaviorName;
            PlayerId = playerId;
            CombatPlayer = combatPlayer;
            CooldownTime = GetCooldownTime();
        }
        
        protected abstract float GetCooldownTime();

        public virtual bool CanExecute(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            if (IsRunning) return false;
            if (!IsCooldownComplete()) return false;
            return ValidateConditions(startPos, startDir, targetPos);
        }

        protected virtual bool ValidateConditions(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            return true;
        }

        public bool TryExecute(Vector3 startPos, Vector3 startDir, Vector3 targetPos)
        {
            if (!CanExecute(startPos, startDir, targetPos))
                return false;

            IsRunning = true;
            LastUseTime = Time.time;
            
            OnExecuteStart(startPos, startDir, targetPos);
            Execute(startPos, startDir, targetPos);
            
            return true;
        }

        protected abstract void Execute(Vector3 startPos, Vector3 startDir, Vector3 targetPos);

        protected virtual void OnExecuteStart(Vector3 startPos, Vector3 startDir, Vector3 targetPos) 
        { 
        }

        public virtual void OnExecuteComplete()
        {
            IsRunning = false;
            StopAutoUpdate();
            OnExecuteEnd();
        }

        protected virtual void OnExecuteEnd() { }

        public virtual void Stop()
        {
            if (IsRunning)
            {
                IsRunning = false;
                StopAutoUpdate();
                OnStop();
            }
        }

        protected virtual void OnStop() { }

        public bool IsCooldownComplete()
        {
            return Time.time >= LastUseTime + CooldownTime;
        }

        public float GetRemainingCooldown()
        {
            float elapsed = Time.time - LastUseTime;
            return Mathf.Max(0, CooldownTime - elapsed);
        }

        public virtual void Initialize()
        {
            OnInitialize();
        }

        protected virtual void OnInitialize() { }

        public virtual void Update(float deltaTime)
        {
            if (!IsRunning) return;
            
            OnUpdate(deltaTime);
        }

        protected virtual void OnUpdate(float deltaTime) { }

        protected void StartAutoUpdate()
        {
            if (_updateRegister != null) return;

            _updateRegister = ActionKit.OnUpdate.Register(() =>
            {
                if (!IsRunning) return;
                Update(Time.deltaTime);
            });
        }

        protected void StopAutoUpdate()
        {
            _updateRegister?.UnRegister();
            _updateRegister = null;
        }

        public virtual void Dispose()
        {
            StopAutoUpdate();
            OnDispose();
        }

        protected virtual void OnDispose() { }
        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
} 
