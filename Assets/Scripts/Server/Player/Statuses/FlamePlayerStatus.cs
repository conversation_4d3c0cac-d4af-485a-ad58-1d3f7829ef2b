using GameServer.Player.Model;
using GameServer.System;
using TFG.Shared;
using TFGShare.Protocol;
using UnitSystem.Unit;
using UnityEngine;

namespace GameServer.Player.Statuses
{
    /// <summary>
    /// 火焰状态示例
    /// 展示如何使用新的PlayerStatus系统
    /// </summary>
    public class FlamePlayerStatus : PlayerStatus
    {
        public FlamePlayerStatus(string playerId) 
            : base(playerId, "FlameStatus")
        {
        }

        protected override void SetupStatus()
        {
            AddTag("Debuff");
            AddTag("DamageOverTime");
        }

        protected override void OnStart()
        {
            Debug.Log($"火焰状态开始 - 玩家: {PlayerId}, 持续时间: {Duration}");
            // 在这里可以添加状态开始时的逻辑，比如给玩家加血等
            var numericModel = CombatPlayer?.NumericModel;
            if (numericModel != null)
            {
                int currentHp = (int)numericModel.GetAsInt(TFG.Shared.NumericType.Hp);
                numericModel.SetValue(TFG.Shared.NumericType.Hp, currentHp + 10);
            }
        }

        protected override void OnUpdate(float deltaTime)
        {
            // 每帧更新的逻辑，比如持续伤害
            // Debug.Log($"火焰状态更新 - 剩余时间: {RemainingTime}");
        }

        protected override void OnEnd()
        {
            Debug.Log($"火焰状态结束 - 玩家: {PlayerId}");
            // 状态结束时的逻辑，比如减血
            var numericModel = CombatPlayer?.NumericModel;
            if (numericModel != null)
            {
                int currentHp = (int)numericModel.GetAsInt(TFG.Shared.NumericType.Hp);
                numericModel.SetValue(TFG.Shared.NumericType.Hp, Mathf.Max(1, currentHp - 10));
            }
        }

        protected override void OnLifeReset(float newDuration)
        {
            Debug.Log($"火焰状态刷新 - 新持续时间: {newDuration}");
        }
    }
} 
