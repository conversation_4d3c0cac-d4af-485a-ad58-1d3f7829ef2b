using System;
using QFramework;
using QFrameworkIntegration;
using UnityEngine;

namespace GameServer.Character
{
    /// <summary>
    /// 角色碰撞系统外观接口 - 为外部提供统一的访问入口
    /// </summary>
    public static class CharacterCollisionFacade
    {
        /// <summary>
        /// 初始化碰撞系统架构
        /// </summary>
        public static void Initialize()
        {
            // 确保架构已初始化
            LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
        }

        /// <summary>
        /// 创建角色碰撞体
        /// </summary>
        /// <param name="ownerPlayerId">角色ID</param>
        /// <param name="character">角色Animator</param>
        /// <returns>碰撞体管理器</returns>
        public static void CreateColliders(string ownerPlayerId, Animator character)
        {
            // 使用新架构的系统创建碰撞体
            var collisionSystem = LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
            collisionSystem.CreateColliders(ownerPlayerId, character);
        }

        /// <summary>
        /// 销毁角色碰撞体
        /// </summary>
        /// <param name="playerId">角色ID</param>
        public static void DestroyColliders(string playerId)
        {
            var collisionSystem = LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
            collisionSystem.DestroyColliders(playerId);
        }

        /// <summary>
        /// 设置角色碰撞体激活状态
        /// </summary>
        /// <param name="playerId">角色ID</param>
        /// <param name="active">是否激活</param>
        public static void SetPlayerActive(string playerId, bool active)
        {
            var collisionSystem = LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
            collisionSystem.SetPlayerActive(playerId, active);
        }

        /// <summary>
        /// 检查角色是否在阻挡
        /// </summary>
        /// <param name="playerId">角色ID</param>
        /// <returns>是否在阻挡</returns>
        public static bool IsPlayerBlocking(string playerId)
        {
            var collisionSystem = LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
            return collisionSystem.IsPlayerBlocking(playerId);
        }

        /// <summary>
        /// 检查角色双手是否都在阻挡
        /// </summary>
        /// <param name="playerId">角色ID</param>
        /// <returns>双手是否都在阻挡</returns>
        public static bool GetBothHandsBlocking(string playerId)
        {
            var collisionSystem = LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
            return collisionSystem.GetBothHandsBlocking(playerId);
        }

        /// <summary>
        /// 重置角色碰撞体
        /// </summary>
        /// <param name="playerId">角色ID</param>
        public static void ResetPlayerCollider(string playerId)
        {
            var collisionSystem = LaunchMainArch.Interface.GetSystem<ICharacterCollisionSystem>();
            collisionSystem.ResetPlayerCollider(playerId);
        }
    }
} 
