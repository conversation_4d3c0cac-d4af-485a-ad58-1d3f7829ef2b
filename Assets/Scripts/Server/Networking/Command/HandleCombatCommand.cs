using GameServer.Battle.Model;
using GameServer.System;
using GameShare.Networking.Command;

using LiteNetLib;
using QFramework;
using QFrameworkIntegration;
using TFGShare.Protocol;

namespace GameServer.Networking.Command
{
    public class HandleTriggerSkillCommand : NetworkMsgCommandBase<C2STriggerSkillRequest>
    {
        public HandleTriggerSkillCommand(NetPeer peer, C2STriggerSkillRequest packet) : base(peer, packet) { }

        protected override void OnExecute()
        {
            var request = _packet as C2STriggerSkillRequest;
            if (!this.GetModel<IBattleModel>().IsPlayerInBattle(request.PlayerId))
            {
                LogKit.W("HandleTriggerSkillCommand: Player is not in battle");
                return;
            }
            var result = this.GetSystem<CombatPlayersSystem>().TriggerSkill(request);
            
            // 广播给房间内所有玩家
            var battleId = this.GetModel<IBattleModel>().GetPlayerBattle(request.PlayerId).BattleId;
            this.GetSystem<RoomSystem>().BroadcastToBattleRoom(battleId, result);
        }
    }

    public class HandleStopSkillCommand : NetworkMsgCommandBase<C2SStopSkillRequest>
    {
        public HandleStopSkillCommand(NetPeer peer, C2SStopSkillRequest packet) : base(peer, packet) { }

        protected override void OnExecute()
        {
            var request = _packet as C2SStopSkillRequest;
            var result = LaunchMainArch.Interface.GetSystem<CombatPlayersSystem>().StopSkill(request);
            if (!this.GetModel<IBattleModel>().IsPlayerInBattle(request.PlayerId))
            {
                LogKit.W("HandleTriggerSkillCommand: Player is not in battle");
                return;
            }
            // 广播给房间内所有玩家
            //this.GetModel<INetworkModel>().BroadcastPacket(result);
            var battleId = this.GetModel<IBattleModel>().GetPlayerBattle(request.PlayerId).BattleId;
            this.GetSystem<RoomSystem>().BroadcastToBattleRoom(battleId, result);
        }
    }

    /// <summary>
    /// 处理客户端引导特效更新请求
    /// </summary>
    public class HandleGuidanceUpdateCommand : NetworkMsgCommandBase<C2SGuidanceUpdateRequest>
    {
        public HandleGuidanceUpdateCommand(NetPeer peer, C2SGuidanceUpdateRequest packet) : base(peer, packet) { }

        protected override void OnExecute()
        {
            var request = _packet as C2SGuidanceUpdateRequest;
            
            // 数据验证
            if (string.IsNullOrEmpty(request.PlayerId) || string.IsNullOrEmpty(request.SkillId))
            {
                LogKit.W("[HandleGuidanceUpdateCommand] 引导更新请求缺少必要参数");
                return;
            }

            // 检查玩家是否在战斗中
            if (!this.GetModel<IBattleModel>().IsPlayerInBattle(request.PlayerId))
            {
                LogKit.W("[HandleGuidanceUpdateCommand] 玩家不在战斗中");
                return;
            }

            LogKit.I($"[HandleGuidanceUpdateCommand] 处理引导更新请求: PlayerId={request.PlayerId}, " +
                    $"SkillId={request.SkillId}, ParameterValue={request.ParameterValue:F2}");

            // 创建广播消息
            var broadcast = new S2CGuidanceUpdateBroadcast
            {
                PlayerId = request.PlayerId,
                SkillId = request.SkillId,
                ParameterValue = request.ParameterValue
            };

            // 广播给房间内除了发送者之外的其他玩家
            var battleId = this.GetModel<IBattleModel>().GetPlayerBattle(request.PlayerId).BattleId;
            this.GetSystem<RoomSystem>().BroadcastToBattleRoom(battleId, broadcast, new[] { request.PlayerId });
        }
    }
} 
