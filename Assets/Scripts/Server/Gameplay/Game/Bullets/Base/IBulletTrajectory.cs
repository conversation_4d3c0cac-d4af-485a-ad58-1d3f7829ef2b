using GameServer.ServerConfigs;
using UnityEngine;

namespace UnitSystem.Bullets
{
    /// <summary>
    /// 子弹轨迹接口
    /// 负责计算子弹的轨迹和移动
    /// </summary>
    public interface IBulletTrajectory
    {
        /// <summary>
        /// 初始化轨迹
        /// </summary>
        void Initialize(BulletBase bullet, BulletConfig config);
        
        /// <summary>
        /// 计算下一个位置
        /// </summary>
        Vector3 CalculateNextPosition(Vector3 currentPosition, float deltaTime);
        
        /// <summary>
        /// 获取当前移动方向
        /// </summary>
        Vector3 GetDirection(Vector3 currentPosition);
        
        /// <summary>
        /// 轨迹是否已结束
        /// </summary>
        bool IsCompleted { get; }
    }
} 
