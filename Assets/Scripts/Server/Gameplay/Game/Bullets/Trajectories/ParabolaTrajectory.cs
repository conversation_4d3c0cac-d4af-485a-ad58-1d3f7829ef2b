using GameServer.ServerConfigs;
using UnityEngine;

namespace UnitSystem.Bullets
{
    /// <summary>
    /// 抛物线轨迹实现
    /// </summary>
    public class ParabolaTrajectory : IBulletTrajectory
    {
        private Vector3 _startPoint;
        private Vector3 _targetPoint;
        private Vector3 _velocity;
        private float _gravity = 9.8f;
        private float _elapsedTime = 0f;
        private float _totalTime = 1f;
        private Vector3 _currentDirection;
        private Vector3 _previousPosition;
        
        public bool IsCompleted { get; private set; }
        
        public void Initialize(BulletBase bullet, BulletConfig config)
        {
            _startPoint = bullet.CreateData.startPoint;
            _targetPoint = bullet.CreateData.targetPoint;
            _currentDirection = bullet.CurrentDirection;
            _previousPosition = _startPoint;
            IsCompleted = false;
            
            // 计算初速度
            Vector3 toTarget = _targetPoint - _startPoint;
            float distance = new Vector2(toTarget.x, toTarget.z).magnitude;
            
            // 设定最大高度
            float maxHeight = Mathf.Max(distance * 0.25f, 1f); // 抛物线高度至少为1，或者距离的1/4
            
            // 计算理想飞行时间
            _totalTime = distance / config.speed;
            
            // 基于距离和抛物线公式计算初始速度
            _velocity = CalculateVelocity(_startPoint, _targetPoint, maxHeight, _totalTime);
            _elapsedTime = 0f;
        }
        
        public Vector3 CalculateNextPosition(Vector3 currentPosition, float deltaTime)
        {
            _elapsedTime += deltaTime;
            
            if (_elapsedTime >= _totalTime)
            {
                IsCompleted = true;
                return _targetPoint;
            }
            
            // 计算当前位置
            Vector3 newPosition = CalculateParabolaPosition(_startPoint, _velocity, _gravity, _elapsedTime);
            
            // 更新方向
            if (Vector3.Distance(newPosition, _previousPosition) > 0.01f)
            {
                _currentDirection = (newPosition - _previousPosition).normalized;
                _previousPosition = newPosition;
            }
            
            return newPosition;
        }
        
        public Vector3 GetDirection(Vector3 currentPosition)
        {
            return _currentDirection;
        }
        
        private Vector3 CalculateVelocity(Vector3 start, Vector3 end, float maxHeight, float time)
        {
            // 计算水平速度
            Vector3 toTarget = end - start;
            Vector3 horizontalVelocity = new Vector3(toTarget.x, 0, toTarget.z) / time;
            
            // 计算垂直方向上的速度
            float verticalVelocity = (4 * maxHeight) / time;
            
            return horizontalVelocity + Vector3.up * verticalVelocity;
        }
        
        private Vector3 CalculateParabolaPosition(Vector3 start, Vector3 initialVelocity, float gravity, float time)
        {
            Vector3 position = start + initialVelocity * time;
            
            // 应用重力
            position.y = start.y + initialVelocity.y * time - 0.5f * gravity * time * time;
            
            return position;
        }
    }
} 
