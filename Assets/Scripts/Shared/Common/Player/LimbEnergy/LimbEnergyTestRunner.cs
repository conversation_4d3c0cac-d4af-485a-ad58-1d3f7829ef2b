using System;
using UnityEngine;
using UnityEngine.UI;
using TFGShare.Protocol;     // GameBodyPart 枚举

namespace GameShare.Common.Player
{

    public class LimbEnergyTestRunner : MonoBehaviour
    {
        public Animator animator;
        public GameBodyPart testBodyPart = GameBodyPart.RightHand;

        public enum OpponentVelocityMode
        {
            Manual,
            MirrorSelf
        }

        [Header("对手速度设置")] public OpponentVelocityMode opponentVelocityMode = OpponentVelocityMode.Manual;
        public Vector3 opponentVelocity = Vector3.zero; // 手动设置
        public bool printToConsole = true; // 是否输出到控制台
        public bool printToFile = false; // 是否写入文件
        [Header("能量计算日志开关")] public bool logZeroDamageWarning = false; // 控制是否打印"伤害为0"日志

        [Header("能量参数调试")] [Tooltip("角色体重（kg）")]
        public float BodyMass = 70f;

        [Tooltip("手部质量百分比")] public float HandMassPercent = 0.01f;
        [Tooltip("下臂质量百分比")] public float LowerArmMassPercent = 0.025f;
        [Tooltip("上臂质量百分比")] public float UpperArmMassPercent = 0.035f;
        [Tooltip("躯干质量百分比")] public float TorsoMassPercent = 0.09f;
        [Tooltip("对手速度系数")] public float OpponentSpeedFactor = 1.0f;
        [Tooltip("伤害转化系数")] public float DamageConvertFactor = 0.3f;
        [Tooltip("最小伤害阈值")] public float MinDamage = 20f;
        [Tooltip("最大伤害阈值")] public float MaxDamage = 200f;
        [Tooltip("能量滤波系数（0-1，越大越平滑）")] public float PunchEnergyFilter = 0.5f;
        [Tooltip("速度阈值")] public float MaxSpeed = 1000f;

        [Header("自动骨骼运动参数")] public bool autoSwing = true;
        public float swingAngle = 60f; // 摆动最大角度
        public float swingSpeed = 1.5f; // 摆动速度（Hz）

        [Header("参与摆动的骨骼（右侧）")] public bool swingHand = true;
        public bool swingLowerArm = true;
        public bool swingUpperArm = true;
        public bool swingShoulder = true;
        public bool swingFoot = true;
        public bool swingLowerLeg = true;
        public bool swingUpperLeg = true;
        public bool swingHip = false;

        [Header("参与摆动的骨骼（左侧）")] public bool swingLeftHand = true;
        public bool swingLeftLowerArm = true;
        public bool swingLeftUpperArm = true;
        public bool swingLeftShoulder = true;
        public bool swingLeftFoot = true;
        public bool swingLeftLowerLeg = true;
        public bool swingLeftUpperLeg = true;
        public bool swingLeftHip = false;

        public GameBodyPart BodyPart = GameBodyPart.LeftHand;

        private Transform handBone, lowerArmBone, upperArmBone, shoulderBone;
        private Transform footBone, lowerLegBone, upperLegBone, hipBone;
        private Transform leftHandBone, leftLowerArmBone, leftUpperArmBone, leftShoulderBone;
        private Transform leftFootBone, leftLowerLegBone, leftUpperLegBone, leftHipBone;
        private Quaternion handInit, lowerArmInit, upperArmInit, shoulderInit;
        private Quaternion footInit, lowerLegInit, upperLegInit, hipInit;
        private Quaternion leftHandInit, leftLowerArmInit, leftUpperArmInit, leftShoulderInit;
        private Quaternion leftFootInit, leftLowerLegInit, leftUpperLegInit, leftHipInit;

        // 新增：UI文本
        private Text uiText;

        private float smoothHandEnergy, smoothHandDamage;

        private PlayerLimbEnergy playerLimbEnergy;
        
        void Start()
        {
            if (animator == null) animator = GetComponent<Animator>();
            if (animator != null) animator.enabled = false; // 自动禁用Animator，防止动画还原骨骼
            
            playerLimbEnergy = gameObject.AddComponent<PlayerLimbEnergy>();
            playerLimbEnergy.Init(animator, new PlayerLimbEnergyConfig()
            {
                BodyMass = BodyMass,
                HandMassPercent = HandMassPercent,
                LowerArmMassPercent = LowerArmMassPercent,
                UpperArmMassPercent = UpperArmMassPercent,
                TorsoMassPercent = TorsoMassPercent,
                OpponentSpeedFactor = OpponentSpeedFactor,
                DamageConvertFactor = DamageConvertFactor,
                MinDamage = MinDamage,
                MaxDamage = MaxDamage,
                PunchEnergyFilter = PunchEnergyFilter,
                MaxSpeed = MaxSpeed
            });
            
#if UNITY_EDITOR
            // 可选：每帧都打印详细日志
            LimbEnergyCalculator.LogEveryFrame = printToFile;
            LimbEnergyCalculator.LogZeroDamageWarning = logZeroDamageWarning;
#endif
            CacheBonesAndInitRotations();
            CreateOrFindUIText();
        }

        void Awake()
        {
            SyncLimbEnergyParams();
        }

        void OnValidate()
        {
            SyncLimbEnergyParams();
        }

        private void SyncLimbEnergyParams()
        {
            LimbEnergyCalculator.BodyMass = BodyMass;
            LimbEnergyCalculator.HandMassPercent = HandMassPercent;
            LimbEnergyCalculator.LowerArmMassPercent = LowerArmMassPercent;
            LimbEnergyCalculator.UpperArmMassPercent = UpperArmMassPercent;
            LimbEnergyCalculator.TorsoMassPercent = TorsoMassPercent;
            LimbEnergyCalculator.OpponentSpeedFactor = OpponentSpeedFactor;
            LimbEnergyCalculator.DamageConvertFactor = DamageConvertFactor;
            LimbEnergyCalculator.MinDamage = MinDamage;
            LimbEnergyCalculator.MaxDamage = MaxDamage;
            LimbEnergyCalculator.PunchEnergyFilter = PunchEnergyFilter;
            LimbEnergyCalculator.MaxSpeed = MaxSpeed;
        }

        void Update()
        {
            if (!Application.isPlaying) return;
            if (playerLimbEnergy == null) return;
            
            if (autoSwing)
            {
                float angle = Mathf.Sin(Time.time * swingSpeed * Mathf.PI * 2) * swingAngle;
                // 右侧手臂
                if (swingHand && handBone != null) handBone.localRotation = handInit * Quaternion.Euler(angle, 0, 0);
                if (swingLowerArm && lowerArmBone != null) lowerArmBone.localRotation = lowerArmInit * Quaternion.Euler(angle, 0, 0);
                if (swingUpperArm && upperArmBone != null) upperArmBone.localRotation = upperArmInit * Quaternion.Euler(angle, 0, 0);
                if (swingShoulder && shoulderBone != null) shoulderBone.localRotation = shoulderInit * Quaternion.Euler(angle, 0, 0);
                // 右侧腿
                if (swingFoot && footBone != null) footBone.localRotation = footInit * Quaternion.Euler(angle, 0, 0);
                if (swingLowerLeg && lowerLegBone != null) lowerLegBone.localRotation = lowerLegInit * Quaternion.Euler(angle, 0, 0);
                if (swingUpperLeg && upperLegBone != null) upperLegBone.localRotation = upperLegInit * Quaternion.Euler(angle, 0, 0);
                if (swingHip && hipBone != null) hipBone.localRotation = hipInit * Quaternion.Euler(angle, 0, 0);
                // 左侧手臂
                if (swingLeftHand && leftHandBone != null) leftHandBone.localRotation = leftHandInit * Quaternion.Euler(angle, 0, 0);
                if (swingLeftLowerArm && leftLowerArmBone != null) leftLowerArmBone.localRotation = leftLowerArmInit * Quaternion.Euler(angle, 0, 0);
                if (swingLeftUpperArm && leftUpperArmBone != null) leftUpperArmBone.localRotation = leftUpperArmInit * Quaternion.Euler(angle, 0, 0);
                if (swingLeftShoulder && leftShoulderBone != null) leftShoulderBone.localRotation = leftShoulderInit * Quaternion.Euler(angle, 0, 0);
                // 左侧腿
                if (swingLeftFoot && leftFootBone != null) leftFootBone.localRotation = leftFootInit * Quaternion.Euler(angle, 0, 0);
                if (swingLeftLowerLeg && leftLowerLegBone != null) leftLowerLegBone.localRotation = leftLowerLegInit * Quaternion.Euler(angle, 0, 0);
                if (swingLeftUpperLeg && leftUpperLegBone != null) leftUpperLegBone.localRotation = leftUpperLegInit * Quaternion.Euler(angle, 0, 0);
                if (swingLeftHip && leftHipBone != null) leftHipBone.localRotation = leftHipInit * Quaternion.Euler(angle, 0, 0);
            }


            var handEnergy = opponentVelocity == Vector3.zero ? playerLimbEnergy.GetEnergyDamage(BodyPart) : playerLimbEnergy.GetEnergyDamage(BodyPart, opponentVelocity);
            
            if (printToConsole)
            {
                Debug.Log($"[LimbTest] {BodyPart} 能量伤害: {handEnergy:F4} 对手速度: {opponentVelocity.magnitude:F2}m/s");
            }

            // 更新UI文本
            if (uiText != null)
            {
                uiText.text = $"{BodyPart} 能量伤害: {handEnergy:F4} 对手速度: {opponentVelocity.magnitude:F2}m/s";
            }
        }
        
        // 缓存所有相关骨骼和初始旋转
        private void CacheBonesAndInitRotations()
        {
            if (animator == null) return;
            // 右侧
            handBone = animator.GetBoneTransform(HumanBodyBones.RightHand);
            lowerArmBone = animator.GetBoneTransform(HumanBodyBones.RightLowerArm);
            upperArmBone = animator.GetBoneTransform(HumanBodyBones.RightUpperArm);
            shoulderBone = animator.GetBoneTransform(HumanBodyBones.RightShoulder);
            handInit = handBone ? handBone.localRotation : Quaternion.identity;
            lowerArmInit = lowerArmBone ? lowerArmBone.localRotation : Quaternion.identity;
            upperArmInit = upperArmBone ? upperArmBone.localRotation : Quaternion.identity;
            shoulderInit = shoulderBone ? shoulderBone.localRotation : Quaternion.identity;
            footBone = animator.GetBoneTransform(HumanBodyBones.RightFoot);
            lowerLegBone = animator.GetBoneTransform(HumanBodyBones.RightLowerLeg);
            upperLegBone = animator.GetBoneTransform(HumanBodyBones.RightUpperLeg);
            hipBone = animator.GetBoneTransform(HumanBodyBones.Hips);
            footInit = footBone ? footBone.localRotation : Quaternion.identity;
            lowerLegInit = lowerLegBone ? lowerLegBone.localRotation : Quaternion.identity;
            upperLegInit = upperLegBone ? upperLegBone.localRotation : Quaternion.identity;
            hipInit = hipBone ? hipBone.localRotation : Quaternion.identity;
            // 左侧
            leftHandBone = animator.GetBoneTransform(HumanBodyBones.LeftHand);
            leftLowerArmBone = animator.GetBoneTransform(HumanBodyBones.LeftLowerArm);
            leftUpperArmBone = animator.GetBoneTransform(HumanBodyBones.LeftUpperArm);
            leftShoulderBone = animator.GetBoneTransform(HumanBodyBones.LeftShoulder);
            leftHandInit = leftHandBone ? leftHandBone.localRotation : Quaternion.identity;
            leftLowerArmInit = leftLowerArmBone ? leftLowerArmBone.localRotation : Quaternion.identity;
            leftUpperArmInit = leftUpperArmBone ? leftUpperArmBone.localRotation : Quaternion.identity;
            leftShoulderInit = leftShoulderBone ? leftShoulderBone.localRotation : Quaternion.identity;
            leftFootBone = animator.GetBoneTransform(HumanBodyBones.LeftFoot);
            leftLowerLegBone = animator.GetBoneTransform(HumanBodyBones.LeftLowerLeg);
            leftUpperLegBone = animator.GetBoneTransform(HumanBodyBones.LeftUpperLeg);
            leftHipBone = animator.GetBoneTransform(HumanBodyBones.Hips); // 髋部为同一骨骼
            leftFootInit = leftFootBone ? leftFootBone.localRotation : Quaternion.identity;
            leftLowerLegInit = leftLowerLegBone ? leftLowerLegBone.localRotation : Quaternion.identity;
            leftUpperLegInit = leftUpperLegBone ? leftUpperLegBone.localRotation : Quaternion.identity;
            leftHipInit = leftHipBone ? leftHipBone.localRotation : Quaternion.identity;
        }

        // 新增：自动创建或查找UI文本
        private void CreateOrFindUIText()
        {
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObj = new GameObject("LimbTestCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();
            }

            uiText = canvas.GetComponentInChildren<Text>();
            if (uiText == null)
            {
                GameObject textObj = new GameObject("LimbTestText");
                textObj.transform.SetParent(canvas.transform);
                uiText = textObj.AddComponent<Text>();
                uiText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
                uiText.fontSize = 32;
                uiText.alignment = TextAnchor.UpperLeft;
                uiText.color = Color.yellow;
                uiText.rectTransform.anchorMin = new Vector2(0, 1);
                uiText.rectTransform.anchorMax = new Vector2(0, 1);
                uiText.rectTransform.pivot = new Vector2(0, 1);
                uiText.rectTransform.anchoredPosition = new Vector2(20, -20);
            }
        }
    }
}
