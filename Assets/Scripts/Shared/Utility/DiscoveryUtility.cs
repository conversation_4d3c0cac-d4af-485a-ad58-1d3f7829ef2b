/*
 * 文件名称：DiscoveryUtility.cs
 * 功能描述：服务器发现工具类，用于扫描和发现局域网内的游戏服务器
 */

using System;
using System.Collections.Generic;
using GameMain.Scripts;
using QFramework;
using TFGShare.Networking;
using TFGShare.Protocol;

namespace GameShare.Utility
{
    /// <summary>
    /// 服务器发现工具类，提供UDP扫描功能以发现局域网内的游戏服务器
    /// 实现IUtility接口以支持QFramework框架的工具管理
    /// </summary>
    public class DiscoveryUtility : IUtility
    {
        /// <summary>
        /// UDP扫描器实例
        /// </summary>
        protected UdpScanner _udpScanner;

        /// <summary>
        /// 初始化服务器发现工具
        /// </summary>
        /// <param name="param">初始化参数列表</param>
        /// <param name="callback">发现服务器后的回调函数</param>
        public virtual void Init(List<object> param, Action<List<BroadcastServerInfo>> callback)
        {
            if (_udpScanner != null)
            {
                _udpScanner?.Dispose();
            }

            _udpScanner = new UdpScanner(GameConfig.ScannerPort, GameConfig.ScannerDelayMs, GameConfig.ScannerSendDelayMs, HandleScannerLog);
            ActionKit.OnUpdate.Register(Update);
        }

        /// <summary>
        /// 停止服务器发现功能
        /// </summary>
        public bool Stop()
        {
            if (_udpScanner == null)
            {
                return false;
            }
            ActionKit.OnUpdate.UnRegister(Update);

            _udpScanner?.Dispose();
            _udpScanner = null;
            return true;
        }

        /// <summary>
        /// 更新UDP扫描器状态
        /// </summary>
        private void Update()
        {
            _udpScanner?.Update();
        }

        /// <summary>
        /// 处理UDP扫描器的日志信息
        /// </summary>
        /// <param name="logLev">日志级别</param>
        /// <param name="msg">日志消息</param>
        /// <param name="ex">异常信息（如果有）</param>
        private void HandleScannerLog(UdpScanner.LogLevel logLev, string msg, Exception ex)
        {
            switch (logLev)
            {
                case UdpScanner.LogLevel.Info:
                    LogKit.I($"[{GetType().FullName}::HandleScannerLog] {msg}");
                    break;
                case UdpScanner.LogLevel.Error:
                    LogKit.E($"[{GetType().FullName}::HandleScannerLog] {msg}");
                    break;
                case UdpScanner.LogLevel.Exception:
                    LogKit.E($"[{GetType().FullName}::HandleScannerLog] {ex}");
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(logLev), logLev, null);
            }
        }
    }
}
