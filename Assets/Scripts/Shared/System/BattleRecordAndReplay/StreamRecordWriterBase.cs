using System;
using System.IO;
using QFramework;

namespace GameShare.System.BattleRecordAndReplay
{
    /// <summary>
    /// 流式记录写入器抽象基类
    /// 封装通用的状态管理、异常处理、资源管理和线程安全功能
    /// </summary>
    public abstract class StreamRecordWriterBase : IStreamRecordWriter
    {
        // 通用状态字段
        protected readonly string _recordFilePath;
        protected readonly object _writeLock = new object();
        protected int _recordCount = 0;
        protected float _lastTimestamp = 0f;
        protected bool _isInitialized = false;
        protected bool _isDisposed = false;

        // 公共属性
        public int RecordCount => _recordCount;
        public float LastTimestamp => _lastTimestamp;
        public bool IsInitialized => _isInitialized;

        protected StreamRecordWriterBase(string recordFilePath)
        {
            _recordFilePath = recordFilePath ?? throw new ArgumentNullException(nameof(recordFilePath));
        }

        // 通用的安全执行包装方法
        protected bool SafeExecute(Func<bool> operation, string operationName)
        {
            try
            {
                return operation();
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::{operationName}] 操作失败: {ex.Message}");
                return false;
            }
        }

        protected T SafeExecute<T>(Func<T> operation, string operationName, T defaultValue = default(T))
        {
            try
            {
                return operation();
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::{operationName}] 操作失败: {ex.Message}");
                return defaultValue;
            }
        }

        // 通用的目录创建方法
        protected bool EnsureDirectoryExists()
        {
            try
            {
                var recordDir = Path.GetDirectoryName(_recordFilePath);
                if (!Directory.Exists(recordDir))
                {
                    Directory.CreateDirectory(recordDir);
                }
                return true;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}] 创建目录失败: {ex.Message}");
                return false;
            }
        }

        // 抽象方法 - 具体实现由子类提供
        protected abstract bool InitializeCore(BattleReplayData metadata);
        protected abstract bool WriteRecordCore(BattleRecordEntry record);
        protected abstract bool FinishCore(BattleReplayData metadata);
        protected abstract void DisposeCore();

        // IStreamRecordWriter接口实现
        public virtual bool Initialize(BattleReplayData metadata)
        {
            if (_isInitialized) return true;
            if (_isDisposed)
            {
                LogKit.E($"[{GetType().FullName}] 已释放的写入器无法重新初始化");
                return false;
            }

            lock (_writeLock)
            {
                return SafeExecute(() =>
                {
                    if (!EnsureDirectoryExists()) return false;
                    
                    var result = InitializeCore(metadata);
                    if (result)
                    {
                        _isInitialized = true;
                        LogKit.I($"[{GetType().FullName}] 初始化成功: {_recordFilePath}");
                    }
                    return result;
                }, "Initialize");
            }
        }

        public virtual bool WriteRecord(BattleRecordEntry record)
        {
            if (!_isInitialized || _isDisposed) return false;
            if (record == null) return false;

            lock (_writeLock)
            {
                return SafeExecute(() =>
                {
                    var result = WriteRecordCore(record);
                    if (result)
                    {
                        _recordCount++;
                        _lastTimestamp = record.Timestamp;
                    }
                    return result;
                }, "WriteRecord");
            }
        }

        public virtual bool Finish(BattleReplayData metadata)
        {
            if (!_isInitialized || _isDisposed) return false;

            lock (_writeLock)
            {
                var result = SafeExecute(() =>
                {
                    // 更新元数据
                    metadata.TotalRecordCount = _recordCount;
                    metadata.FinishRecording(_lastTimestamp, _recordCount);
                    
                    var finishResult = FinishCore(metadata);
                    if (finishResult)
                    {
                        LogKit.I($"[{GetType().FullName}] 写入完成，总记录数: {_recordCount}，时长: {_lastTimestamp:F2}秒");
                    }
                    return finishResult;
                }, "Finish");

                // 完成后自动释放资源
                Dispose();
                return result;
            }
        }

        public virtual void Dispose()
        {
            if (_isDisposed) return;
            
            lock (_writeLock)
            {
                _isDisposed = true;
                SafeExecute(() =>
                {
                    DisposeCore();
                    return true;
                }, "Dispose");
            }
        }
    }
} 
