using QFramework;
using UnityEngine;

namespace GameServer.ServerConfigs
{
    /// <summary>
    /// FireHeroSkill3大招阶段配置
    /// 用于配置大招各阶段的持续时间、高度控制和动画参数
    /// </summary>
    [CreateAssetMenu(fileName = "FireHeroSkill3PhaseConfig", menuName = "Game/FireHeroSkill3PhaseConfig", order = 2)]
    public class FireHeroSkill3PhaseConfig : BaseScriptableObjectConfig
    {
        [Header("阶段持续时间配置")]
        
        [Tooltip("上升阶段持续时间（秒）")]
        [Range(0, 10)]
        public float RisingDuration = 2.0f;
        
        [Tooltip("悬停阶段持续时间（秒）")]
        [Range(0, 10)]
        public float HoveringDuration = 1.5f;
        
        [Tooltip("射击阶段持续时间（秒）")]
        [Range(0, 20)]
        public float ShootingDuration = 5.0f;
        
        [Tooltip("准备降落阶段持续时间（秒）")]
        [Range(0, 5)]
        public float PreparingLandingDuration = 1.0f;
        
        [Tooltip("下降阶段持续时间（秒）")]
        [Range(0, 10)]
        public float FallingDuration = 1.5f;
        
        [Tooltip("落地阶段持续时间（秒）")]
        [Range(0, 5)]
        public float LandingDuration = 0.5f;
        
        [Header("高度控制配置")]
        [Tooltip("最大飞行高度（米）")]
        [Range(5, 50)]
        public float MaxHeight = 15f;
        
        [Tooltip("上升阶段专用曲线")]
        public AnimationCurve RisingCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        
        [Tooltip("下降阶段专用曲线")]
        public AnimationCurve FallingCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        
        [Tooltip("高度变化平滑度")]
        [Range(0.1f, 5f)]
        public float HeightSmoothness = 1f;
        
        /// <summary>
        /// 初始化配置
        /// </summary>
        public override void Initialize()
        {
            // 验证阶段持续时间
            ValidatePhaseDurations();
            
            // 验证高度控制参数
            ValidateHeightParameters();
            
            // 初始化默认曲线
            InitializeDefaultCurves();
            
            LogKit.I($"[FireHeroSkill3PhaseConfig] 阶段配置初始化完成 - 总持续时间: {GetTotalDuration()}s, 最大高度: {MaxHeight}m");
        }
        
        /// <summary>
        /// 验证阶段持续时间
        /// </summary>
        private void ValidatePhaseDurations()
        {
            if (RisingDuration <= 0)
            {
                LogKit.W($"[FireHeroSkill3PhaseConfig] 上升阶段持续时间无效: {RisingDuration}，使用默认值");
                RisingDuration = 2.0f;
            }
            
            if (ShootingDuration <= 0)
            {
                LogKit.W($"[FireHeroSkill3PhaseConfig] 射击阶段持续时间无效: {ShootingDuration}，使用默认值");
                ShootingDuration = 5.0f;
            }
            
            if (FallingDuration <= 0)
            {
                LogKit.W($"[FireHeroSkill3PhaseConfig] 下降阶段持续时间无效: {FallingDuration}，使用默认值");
                FallingDuration = 1.5f;
            }
        }
        
        /// <summary>
        /// 验证高度控制参数
        /// </summary>
        private void ValidateHeightParameters()
        {
            if (MaxHeight <= 0)
            {
                LogKit.W($"[FireHeroSkill3PhaseConfig] 最大高度无效: {MaxHeight}，使用默认值");
                MaxHeight = 15f;
            }
            
            if (HeightSmoothness <= 0)
            {
                LogKit.W($"[FireHeroSkill3PhaseConfig] 高度平滑度无效: {HeightSmoothness}，使用默认值");
                HeightSmoothness = 1f;
            }
        }
        
        /// <summary>
        /// 初始化默认曲线
        /// </summary>
        private void InitializeDefaultCurves()
        {
            if (RisingCurve == null || RisingCurve.keys.Length == 0)
            {
                RisingCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
            }
            
            if (FallingCurve == null || FallingCurve.keys.Length == 0)
            {
                FallingCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
            }
        }
        
        /// <summary>
        /// 获取总持续时间
        /// </summary>
        /// <returns>所有阶段的总持续时间</returns>
        public float GetTotalDuration()
        {
            return RisingDuration + HoveringDuration +
                   ShootingDuration + PreparingLandingDuration + 
                   FallingDuration + LandingDuration;
        }
        
        /// <summary>
        /// 根据阶段枚举获取对应的持续时间
        /// </summary>
        /// <param name="phase">阶段枚举（按字符串匹配）</param>
        /// <returns>阶段持续时间</returns>
        public float GetPhaseDuration(string phaseName)
        {
            return phaseName switch
            {
                "Rising" => RisingDuration,
                "Hovering" => HoveringDuration,
                "Shooting" => ShootingDuration,
                "PreparingLanding" => PreparingLandingDuration,
                "Falling" => FallingDuration,
                "Landing" => LandingDuration,
                _ => 1f
            };
        }
        
        /// <summary>
        /// 获取指定阶段的高度曲线
        /// </summary>
        /// <param name="phaseName">阶段名称</param>
        /// <returns>对应的动画曲线</returns>
        public AnimationCurve GetPhaseHeightCurve(string phaseName)
        {
            return phaseName switch
            {
                "Rising" => RisingCurve,
                "Falling" => FallingCurve
            };
        }
    }
} 
