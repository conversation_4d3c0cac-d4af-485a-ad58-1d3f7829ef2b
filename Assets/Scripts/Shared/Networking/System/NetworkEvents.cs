using System.Net;
using LiteNetLib;

namespace GameShare.Networking.System
{
    public class OnPeerConnectedEvent
    {
        public NetPeer Peer { get; private set; }
        public OnPeerConnectedEvent(NetPeer peer)
        {
            Peer = peer;
        }
    }

    public class OnPeerDisconnectedEvent
    {
        public NetPeer Peer { get; private set; }
        public OnPeerDisconnectedEvent(NetPeer peer)
        {
            Peer = peer;
        }
    }

    public class OnNetworkErrorEvent
    {
        public IPEndPoint Peer { get; private set; }
        public OnNetworkErrorEvent(IPEndPoint peer)
        {
            Peer = peer;
        }
    }

}
