using UnityEngine;
using QFramework;
using LiteNetLib;
using GameShare.Networking.Command;
using TFGShare.Protocol;
using Client.Model.Battle;

namespace Client.Command.Battle
{
    /// <summary>
    /// 处理玩家血量更新网络消息命令
    /// </summary>
    public class HandlePlayerHealthUpdateCommand : NetworkMsgCommandBase<S2CPlayerHealthUpdate>
    {
        public HandlePlayerHealthUpdateCommand(NetPeer peer, S2CPlayerHealthUpdate packet) : base(peer, packet) { }

        protected override void OnExecute()
        {
            // 数据验证
            if (_packet == null)
            {
                LogKit.E("[HandlePlayerHealthUpdateCommand] 收到空的玩家血量更新消息");
                return;
            }

            if (string.IsNullOrEmpty(_packet.BattleId) || string.IsNullOrEmpty(_packet.PlayerId))
            {
                LogKit.E("[HandlePlayerHealthUpdateCommand] 玩家血量更新消息缺少必要参数");
                return;
            }

            // 获取玩家血量更新数据
            string battleId = _packet.BattleId;
            string playerId = _packet.PlayerId;
            int currentHealth = _packet.CurrentHealth;
            int maxHealth = _packet.MaxHealth;

            LogKit.I($"[HandlePlayerHealthUpdateCommand] 玩家血量更新: BattleId={battleId}, PlayerId={playerId}, 当前血量={currentHealth}/{maxHealth}");

            // 更新战斗模型数据
            var battleModel = this.GetModel<IClientBattleModel>();
            if (!battleModel.BattleExists(battleId))
            {
                LogKit.W($"[HandlePlayerHealthUpdateCommand] 找不到战斗{battleId}，可能是消息乱序");
            }
            else
            {
                battleModel.UpdatePlayerHealth(battleId, playerId, currentHealth);
            }

            // 发送事件通知相关系统
            this.SendEvent(new PlayerHealthUpdateEvent(
                battleId,
                playerId,
                currentHealth
            ));
        }
    }

    /// <summary>
    /// 玩家血量更新事件
    /// </summary>
    public class PlayerHealthUpdateEvent
    {
        public string BattleId { get; }
        public string PlayerId { get; }
        public int Health { get; }

        public PlayerHealthUpdateEvent(string battleId, string playerId, int health)
        {
            BattleId = battleId;
            PlayerId = playerId;
            Health = health;
        }
    }
} 
