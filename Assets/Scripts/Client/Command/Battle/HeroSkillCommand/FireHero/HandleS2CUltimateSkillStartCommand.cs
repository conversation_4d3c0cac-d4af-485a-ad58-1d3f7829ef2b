using System;
using Client.Event.BattleEvent;
using GameClient;
using GameClient.Model;
using GameServer.ServerConfigs;
using QFramework;
using TFGShare.Protocol;
using GameShare.Networking.Command;
using LiteNetLib;
using UnityEngine;

namespace Client.Command.Battle.HeroSkillCommand
{
    /// <summary>
    /// 处理大招开始命令 - 触发天气变化特效
    /// </summary>
    public class HandleS2CUltimateSkillStartCommand : NetworkMsgCommandBase<S2CUltimateSkillStart>
    {
        public HandleS2CUltimateSkillStartCommand(NetPeer peer, S2CUltimateSkillStart packet) 
            : base(peer, packet) { }

        protected override void OnExecute()
        {
            StartWeatherEffect(_packet);

            // 可以在这里添加其他大招开始的客户端逻辑
            // 例如：UI提示、相机震动、音效播放等
        }

        /// <summary>
        /// 开始天气变化特效 (占位符实现)
        /// </summary>
        private void StartWeatherEffect(S2CUltimateSkillStart packet)
        {
            var soConfigModel = LaunchMainArch.Interface.GetModel<ClientSOConfigModel>();
            
            var effectConfig = soConfigModel.GetScriptableObject<FireHeroSkill3EffectConfig>("FireHeroSkill3EffectConfig");
            
            var effectId = Guid.NewGuid().ToString();
            this.SendEvent(new StaticEffectEvent(
                packet.PlayerId,
                effectConfig.SkyEffectSkyFlash, // 特效名称
                packet.Pos,
                effectId,
                EffectAction.Spawn,
                effectConfig.CommonEffectLifeTime, // 使用大招持续时间
                false, // 不跟随玩家
                Vector3.forward,
                Vector3.one
            ));
            
            effectId = Guid.NewGuid().ToString();
            this.SendEvent(new StaticEffectEvent(
                packet.PlayerId,
                effectConfig.SkyEffect, // 特效名称
                packet.Pos,
                effectId,
                EffectAction.Spawn,
                effectConfig.CommonEffectLifeTime,
                false, // 不跟随玩家
                Vector3.forward,
                Vector3.one
            ));
        }
    }
} 
