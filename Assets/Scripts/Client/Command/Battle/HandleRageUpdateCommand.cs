using GameShare.Networking.Command;
using LiteNetLib;
using QFramework;
using TFGShare.Protocol;

namespace Client.Command.Battle
{
    /// <summary>
    /// 处理怒气更新网络消息命令
    /// </summary>
    public class HandleEnergyUpdateCommand : NetworkMsgCommandBase<S2CEnergyUpdate>
    {
        public HandleEnergyUpdateCommand(NetPeer peer, S2CEnergyUpdate packet) : base(peer, packet) { }

        protected override void OnExecute()
        {
            // 数据验证
            if (_packet == null)
            {
                LogKit.E("[HandleEnergyUpdateCommand] 收到空的怒气更新消息");
                return;
            }

            if (string.IsNullOrEmpty(_packet.PlayerId))
            {
                LogKit.E("[HandleEnergyUpdateCommand] 怒气更新消息缺少必要参数");
                return;
            }

            //LogKit.I($"[HandleEnergyUpdateCommand] 处理怒气更新网络消息: PlayerId={_packet.PlayerId}, EnergyValue={_packet.EnergyValue}");

            // 发出事件通知表现层系统
            this.SendEvent(new EnergyUpdateEvent(
                _packet.PlayerId,
                _packet.EnergyValue
            ));
        }
    }

    /// <summary>
    /// 怒气更新事件 - 通知表现层系统更新怒气显示
    /// </summary>
    public class EnergyUpdateEvent
    {
        public string PlayerId { get; set; }
        public float EnergyValue { get; set; }

        public EnergyUpdateEvent(string playerId, float energyValue)
        {
            PlayerId = playerId;
            EnergyValue = energyValue;
        }
    }
}
