using Cysharp.Threading.Tasks;
using GameClient.System;
using QFramework;

namespace GameClient.Command.Room
{
    public class ChangeReadyInRoomStateCommand : AbstractCommand
    {
        public readonly string RoomId;
        public readonly string PlayerId;
        public readonly bool ToReady;

        public ChangeReadyInRoomStateCommand(string roomId,string playerId,bool toReady)
        {
            this.RoomId = roomId;
            this.PlayerId = playerId;
            this.ToReady = toReady;
        }
        protected override void OnExecute()
        {
            base.OnExecute();
            ReadyInRoom().Forget();
        }

        private async UniTask ReadyInRoom()
        {
            await this.GetSystem<RoomSystem>().ChangeReadyInRoomState(this.RoomId, this.PlayerId, this.ToReady);
        }
    }
}
