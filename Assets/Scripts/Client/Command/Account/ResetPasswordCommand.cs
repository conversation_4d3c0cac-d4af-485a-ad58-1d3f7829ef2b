using System;
using System.Threading;
using Client.Event.UserEvent;
using Client.Model;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using QFramework;
using TFG.UI;
using UnityEngine;

namespace GameClient.Command
{
    public class ResetPasswordEmailCommand : ResetPasswordCommand
    {
        protected override string Account => LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>().GetEmailAccount();
        public override string NewPassword => LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>().CachePassword;
        protected override void AfterResetSuccess()
        {
            // 获取账户偏好设置模型
            var preferencesModel = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            var accountFlowModel = LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>();
            preferencesModel.UpdateEmailPreferences(AccountFlowType.ResetPassword,accountFlowModel.CurrentEmail,accountFlowModel.CachePassword);
            preferencesModel.UpdateEmailPreferences(AccountFlowType.Login,accountFlowModel.CurrentEmail,accountFlowModel.CachePassword);
            accountFlowModel.ClearVerification(Account,AccountFlowType.ResetPassword);
        }
    }

    public class ResetPasswordPhoneCommand : ResetPasswordCommand
    {
        protected override string Account => LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>().GetPhoneAccount();
        public override string NewPassword => LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>().CachePassword;

        protected override void AfterResetSuccess()
        {
            // 获取账户偏好设置模型
            var preferencesModel = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            var accountFlowModel = LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>();
            preferencesModel.UpdatePhonePreferences(AccountFlowType.ResetPassword, accountFlowModel.CurrentPhone,
                accountFlowModel.CurrentCountryCode, accountFlowModel.CurrentCountryCodeIndex,
                CheckType.Password);;
            preferencesModel.UpdatePhonePreferences(AccountFlowType.Login, accountFlowModel.CurrentPhone,
                accountFlowModel.CurrentCountryCode, accountFlowModel.CurrentCountryCodeIndex,
                CheckType.Password);;
            accountFlowModel.ClearVerification(Account,AccountFlowType.ResetPassword);
        }
    }

    public abstract class ResetPasswordCommand : AbstractCommand
    {
        protected abstract string Account { get;}
        public abstract string NewPassword { get; }
        protected string VerifyCodeToken {
            get
            {
                var flowModel = LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>();
                flowModel.Account2VerifyTokenMap.TryGetValue(Account, out var token);
                token = token ?? "";
                return token;
            }
        }
        protected string VerifyCode => LaunchMainArch.Interface.GetModel<ClientAccountFlowModel>().CacheVerifyCode;


        protected abstract void AfterResetSuccess();

        protected override void OnExecute()
        {
            base.OnExecute();
            OnResetPassword().Forget();
        }

        private async UniTask OnResetPassword()
        {
            var tcs = new CancellationTokenSource();
            var LocalizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string loading = LocalizationSystem.GetLocalizationContent("Connecting");
            string errormsg = LocalizationSystem.GetLocalizationContent("Error");
            string ok = LocalizationSystem.GetLocalizationContent("OK");
            string requestError = LocalizationSystem.GetLocalizationContent("RequestError");
            string networkError = LocalizationSystem.GetLocalizationContent("NetworkError");
            var loadingDialog = UIDialogHelper.ShowNormalTips("", loading, 1f, tcs.Token);
            try
            {
                var request = new ResetPasswordRequest(VerifyCodeToken, NewPassword, VerifyCode);
                LogKit.I($"[{GetType().FullName}::OnResetPassword] {request.ToJson()}");
                var response = await HttpHelper.Create().WithJsonBody(request.ToJson())
                    .PostAsync<BackendApiResponse<ResetSuccessData>>(ApiHelper.QA_GetResetPasswordApi);
                tcs.Cancel();
                await loadingDialog;
                //Http错误
                if (!response.IsSuccess)
                {
                    this.SendEvent(new ResetPasswordFailedEvent());
                    LogKit.E(
                        $"[{GetType().FullName}::OnGetVerificationCode] HttpReqError!||" +
                        $"rawResponse:{response.RawResponse}||" +
                        $"ErrorMessage:{response.ErrorMessage}||" +
                        $"StatusCode:{response.StatusCode}||" +
                        $"Data:{response.Data}");
                    UIDialogHelper.ShowNormalStyle(errormsg, requestError, true, false, confirmText: ok);
                    return;
                }
                if (response.Data.Success)
                {
                    HandleSuccess(response.Data);
                }
                else
                {
                    HandleFailed(response.Data);
                }
            }
            //未知错误
            catch (Exception e)
            {
                LogKit.E($"[{GetType().FullName}] An unexpected error occurred during login: {e.Message}");
                this.SendEvent(new ResetPasswordFailedEvent());
                if (!tcs.IsCancellationRequested)
                {
                    tcs.Cancel();
                }
                await loadingDialog;
                UIDialogHelper.ShowNormalStyle(errormsg, networkError, true, false, confirmText: ok);
            }
            finally
            {
                tcs.Dispose();
            }
        }

        protected void HandleSuccess(BackendApiResponse<ResetSuccessData> apiResponse)
        {
            AfterResetSuccess();
            LogKit.I($"[{GetType().FullName}::HandleSuccess] 密码重置成功");
            this.SendEvent(new ResetPasswordSuccessEvent());
        }

        protected void HandleFailed(BackendApiResponse<ResetSuccessData> apiResponse)
        {
            this.SendEvent(new ResetPasswordFailedEvent());
            var localizationSystem = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
            string error = localizationSystem.GetLocalizationContent("Error");
            string ok = localizationSystem.GetLocalizationContent("OK");
            var erroKey = apiResponse.MsgKey.IsNullOrEmpty()? "RequestError":apiResponse.MsgKey;
            var errorMsg = localizationSystem.GetLocalizationContent(erroKey);
            UIDialogHelper.ShowNormalStyle(error, errorMsg, true, false,
                confirmText: ok);
        }
    }
}
