using Client.System.Camera;
using QFramework;

namespace GameClient.Command.Camera
{
    public class StartCameraShakeCommand : AbstractCommand
    {
        private string _eventName; 
        public StartCameraShakeCommand(string eventName)
        {
            _eventName = eventName; 
        }

        protected override void OnExecute()
        {
            base.OnExecute();
            var system = LaunchMainArch.Interface.GetSystem<CameraSystem>();
            system.TriggerConfigShake(_eventName);  
        }
    }
}
