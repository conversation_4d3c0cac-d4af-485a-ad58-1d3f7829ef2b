using GameShare.Networking.Command;
using LiteNetLib;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient.Networking.Command
{
    public class HandleCharacterSelectCommand : NetworkMsgCommandBase<S2CPlayerDataUpdateResponse>
    {
        public HandleCharacterSelectCommand(NetPeer peer, S2CPlayerDataUpdateResponse packet) : base(peer, packet)
        {
            
        }

        protected override void OnExecute()
        {
            base.OnExecute();
            LogKit.I("S2CSelectCharacterResult!!!"); 
        }
    }
}
