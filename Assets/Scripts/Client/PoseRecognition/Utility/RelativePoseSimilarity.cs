using System.Collections.Generic;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient
{
    /// <summary>
    /// 基于相对旋转的姿势相似度计算工具类
    /// 实现姿势不变性：消除整体旋转影响，专注于关节间相对关系
    /// 使用鲁棒统计方法抵抗异常值，支持不同体位下相同姿势的识别
    /// </summary>
    public static class RelativePoseSimilarity
    {
        /// <summary>
        /// 计算基于相对旋转的鲁棒相似度
        /// 消除整体旋转影响，专注于关节间相对关系，使用中位数抵抗异常值
        /// 实现"站立拱手"、"弯腰拱手"、"躺下拱手"都能被识别为同一姿势
        /// </summary>
        /// <param name="rotationJoints">旋转关节点列表，第一个作为基准点</param>
        /// <param name="templateNormalizedValues">模板的归一化值字典，包含相对四元数</param>
        /// <param name="currentData">当前运动数据</param>
        /// <returns>鲁棒相似度值(0-1)，1表示完全相同，0表示完全不同</returns>
        public static float CalculateRobustSimilarity(List<HumanBodyBones> rotationJoints, Dictionary<string, MovementData.QuaternionData> templateNormalizedValues, MovementData currentData)
        {
            // Early Exit：基础验证
            if (rotationJoints == null || rotationJoints.Count <= 1) return 1f;
            if (templateNormalizedValues == null || currentData?.Datas == null) return 0f;

            // 获取基准关节点（第一个关节点）
            var baseJoint = rotationJoints[0];
            var baseJointIndex = BoneIndexCache.GetIndex(baseJoint);
            if (baseJointIndex == -1) return 0f;

            // 计算基准关节点的当前旋转
            var baseRotData = currentData.Datas[baseJointIndex].Rot;
            var baseCurrentRotation = new Quaternion(baseRotData.x, baseRotData.y, baseRotData.z, baseRotData.w);

            // 收集所有非根节点的相似度
            var similarities = new float[rotationJoints.Count - 1];
            var count = 0;

            // 计算其他关节点相对于基准关节点的相似度
            for (int i = 1; i < rotationJoints.Count; i++)
            {
                var joint = rotationJoints[i];
                var jointIndex = BoneIndexCache.GetIndex(joint);
                if (jointIndex == -1) continue;

                // 从模板获取相对四元数
                var key = $"rotation_{joint}";
                if (!templateNormalizedValues.TryGetValue(key, out var quatData))
                    continue;

                var templateRelativeQuaternion = new Quaternion(quatData.x, quatData.y, quatData.z, quatData.w);

                // 计算当前的相对四元数
                var jointRotData = currentData.Datas[jointIndex].Rot;
                var jointCurrentRotation = new Quaternion(jointRotData.x, jointRotData.y, jointRotData.z, jointRotData.w);
                var currentRelativeQuaternion = Quaternion.Inverse(baseCurrentRotation) * jointCurrentRotation;

                // 使用角度差计算相似度，采用分段容差策略
                var angle = Quaternion.Angle(templateRelativeQuaternion, currentRelativeQuaternion);

                // 分段容差：对不同角度范围使用不同的严格程度
                float similarity;
                if (angle <= 15f)
                {
                    // 15度以内：高精度区域，线性映射
                    similarity = 1f - (angle / 15f) * 0.2f;  // 最多扣除20%
                }
                else if (angle <= 45f)
                {
                    // 15-45度：中等精度区域
                    var normalizedAngle = (angle - 15f) / 30f;  // 归一化到0-1
                    similarity = 0.8f - normalizedAngle * 0.5f;  // 从80%降到30%
                }
                else
                {
                    // 45度以上：低精度区域，快速衰减但不为0
                    var normalizedAngle = Mathf.Clamp01((angle - 45f) / 90f);
                    similarity = 0.3f * (1f - normalizedAngle);  // 从30%降到0%
                }
                similarities[count] = similarity;

                count++;
            }

            if (count == 0) return 0f;

            // 使用中位数作为鲁棒相似度度量，抵抗异常值影响
            return StatisticsHelper.CalculateMedian(similarities, count);
        }

        /// <summary>
        /// 计算标准化的相对旋转数组
        /// 将第一个关节点作为参考点（设为单位四元数），其他关节点表示为相对偏移
        /// </summary>
        /// <param name="rotationJoints">旋转关节点列表</param>
        /// <param name="currentData">当前运动数据</param>
        /// <returns>标准化的相对旋转数组，第一个元素为单位四元数</returns>
        public static Quaternion[] CalculateNormalizedRelativeRotations(
            List<HumanBodyBones> rotationJoints,
            MovementData currentData)
        {
            if (rotationJoints == null || rotationJoints.Count == 0 || currentData?.Datas == null)
                return new Quaternion[0];

            var result = new Quaternion[rotationJoints.Count];

            // 第一个关节点设为单位四元数（参考点）
            result[0] = Quaternion.identity;

            if (rotationJoints.Count == 1) return result;

            // 获取基准关节点的旋转
            var baseJoint = rotationJoints[0];
            var baseJointIndex = BoneIndexCache.GetIndex(baseJoint);
            if (baseJointIndex == -1) return result;

            var baseRotData = currentData.Datas[baseJointIndex].Rot;
            var baseRotation = new Quaternion(baseRotData.x, baseRotData.y, baseRotData.z, baseRotData.w);
            var invBaseRotation = Quaternion.Inverse(baseRotation);

            // 计算其他关节点相对于基准点的旋转
            for (int i = 1; i < rotationJoints.Count; i++)
            {
                var joint = rotationJoints[i];
                var jointIndex = BoneIndexCache.GetIndex(joint);
                if (jointIndex == -1)
                {
                    result[i] = Quaternion.identity; // 无效关节点使用单位四元数
                    continue;
                }

                var jointRotData = currentData.Datas[jointIndex].Rot;
                var jointRotation = new Quaternion(jointRotData.x, jointRotData.y, jointRotData.z, jointRotData.w);

                // 计算相对旋转：relativeRot = baseRot^-1 * jointRot
                result[i] = invBaseRotation * jointRotation;
            }

            return result;
        }

        /// <summary>
        /// 比较两组标准化相对旋转的相似度
        /// 使用多种鲁棒统计方法，可选择不同的相似度计算策略
        /// </summary>
        /// <param name="rotationsA">第一组标准化相对旋转</param>
        /// <param name="rotationsB">第二组标准化相对旋转</param>
        /// <param name="method">相似度计算方法</param>
        /// <returns>相似度值(0-1)</returns>
        public static float CompareNormalizedRotations(
            Quaternion[] rotationsA,
            Quaternion[] rotationsB,
            SimilarityMethod method = SimilarityMethod.Median)
        {
            if (rotationsA == null || rotationsB == null || 
                rotationsA.Length != rotationsB.Length || 
                rotationsA.Length <= 1)
                return rotationsA?.Length == rotationsB?.Length ? 1f : 0f;

            var similarities = new float[rotationsA.Length - 1];
            var count = 0;

            // 从索引1开始，跳过基准点（都是单位四元数）
            for (int i = 1; i < rotationsA.Length; i++)
            {
                var angle = Quaternion.Angle(rotationsA[i], rotationsB[i]);
                similarities[count] = 1 - Mathf.Clamp01(angle / 180f);
                count++;
            }

            if (count == 0) return 1f;

            // 根据选择的方法计算最终相似度
            switch (method)
            {
                case SimilarityMethod.Median:
                    return StatisticsHelper.CalculateMedian(similarities, count);
                
                case SimilarityMethod.WinsorizedMean:
                    return StatisticsHelper.CalculateWinsorizedMean(similarities, count, 0.1f);
                
                case SimilarityMethod.Mean:
                default:
                    var sum = 0f;
                    for (int i = 0; i < count; i++)
                    {
                        sum += similarities[i];
                    }
                    return sum / count;
            }
        }

        /// <summary>
        /// 相似度计算方法枚举
        /// </summary>
        public enum SimilarityMethod
        {
            /// <summary>简单平均值（对异常值敏感）</summary>
            Mean,
            /// <summary>中位数（鲁棒，推荐）</summary>
            Median,
            /// <summary>Winsorized均值（中等鲁棒性）</summary>
            WinsorizedMean
        }
    }
}
