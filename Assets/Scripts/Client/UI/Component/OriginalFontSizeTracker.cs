using UnityEngine;

namespace GameClient.UI.Component
{
    /// <summary>
    /// 原始字体大小追踪器
    /// 用于记录TextMeshProUGUI组件的原始字体大小，以便在字体大小调整时能够正确计算
    /// </summary>
    [SerializeField]
    public class OriginalFontSizeTracker : MonoBehaviour
    {
        [SerializeField] private float _originalSize = 0f;
        
        /// <summary>
        /// 原始字体大小
        /// </summary>
        public float OriginalSize
        {
            get => _originalSize;
            set => _originalSize = value;
        }
        
        /// <summary>
        /// 组件初始化时自动记录原始字体大小
        /// </summary>
        private void Awake()
        {
            if (_originalSize <= 0f)
            {
                var textComponent = GetComponent<TMPro.TextMeshProUGUI>();
                if (textComponent != null)
                {
                    _originalSize = textComponent.fontSize;
                }
            }
        }
    }
}
