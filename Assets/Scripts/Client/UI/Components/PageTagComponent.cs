/****************************************************************************
 * 2025.7 DESKTOP-A2IBMLK
 ****************************************************************************/

using System;
using System.Collections.Generic;
using GameClient;
using GameClient.UI.Component;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    public class ViewBattleSettingStateChangeEvent
    {
        public readonly string StateKey;

        public ViewBattleSettingStateChangeEvent(string stateKey)
        {
            StateKey = stateKey;
        }
    }

    public partial class PageTagComponent : UIComponent, ICanSendEvent
    {
        public void Init(BattleSettingPanelSO panelConfig)
        {
            UITabSelector.OnSelectionChanged += UITabSelectorOnOnSelectionChanged;
            /*
            for (int i = 0; i < UITabSelector.TabButtons.Count; i++)
            {
                UITabSelector.TabButtons[i].GetComponentInChildren<LocalizeStringEventWrapper>().SetLocalizationKey(UITabSelector.Options[i].LocalizationKey);
            }
            */

            int defaulteIndex = 0;
            if (!panelConfig.HasBaseSettingGroup)
            {
                UITabSelector.TabButtons[0].GetComponent<CanvasGroup>().DisableView();
                UITabSelector.TabButtons[0].GetComponent<LayoutElement>().ignoreLayout = true;
                defaulteIndex++;
            }
            if (!panelConfig.HasAdvancedSetting)
            {
                UITabSelector.TabButtons[1].GetComponent<CanvasGroup>().DisableView();
                UITabSelector.TabButtons[1].GetComponent<LayoutElement>().ignoreLayout = true;
                defaulteIndex++;
            }
            if (!panelConfig.HasControlSetting)
            {
                UITabSelector.TabButtons[2].GetComponent<CanvasGroup>().DisableView();
                UITabSelector.TabButtons[2].GetComponent<LayoutElement>().ignoreLayout = true;
                defaulteIndex++;
            }
            if (!panelConfig.HasSkillSetting)
            {
                UITabSelector.TabButtons[3].GetComponent<CanvasGroup>().DisableView();
                UITabSelector.TabButtons[3].GetComponent<LayoutElement>().ignoreLayout = true;
                defaulteIndex++;
            }

            //0基础设置，1高级设置，2控制设置，3技能设置
            defaulteIndex = UnityEngine.Mathf.Clamp(defaulteIndex, 0, UITabSelector.TabButtons.Count);
            UITabSelector.Select(defaulteIndex);
        }

        public void DeInit()
        {
            UITabSelector.OnSelectionChanged -= UITabSelectorOnOnSelectionChanged;
        }

        private void UITabSelectorOnOnSelectionChanged(int index, TabOption option)
        {
            LogKit.I($"[{GetType().FullName}::UITabSelectorOnOnSelectionChanged] 切换子面板");
            this.SendEvent(new ViewBattleSettingStateChangeEvent(option.Value));
        }

        protected override void OnBeforeDestroy()
        {
            Clear();
        }

        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
