/****************************************************************************
 * 2025.7 DESKTOP-A2IBMLK
 ****************************************************************************/

using System;
using System.Collections.Generic;
using Client.Model;
using Cysharp.Threading.Tasks;
using GameClient;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TFG.UI.Common;

namespace TFG.UI
{
    public partial class RoundDurationComponent : UIComponent,IController
    {
        private void Awake()
        {
        }

        public override void Show()
        {
            CanvasGroup.EnableView();
            LayoutElement.ignoreLayout = false;
        }

        public override void Hide()
        {
            CanvasGroup.DisableView();
            LayoutElement.ignoreLayout = true;
        }
        public void InitAndShow(bool needShow,List<SelectionOption> options,SelectionOption defaultOption)
        {
            Init(options,defaultOption);
            if (needShow)
            {
                Show();
            }
            else
            {
                Hide();
            }
        }

        public void Init(List<SelectionOption> options,SelectionOption defaultOption)
        {
            RoundTimeSelecter.OnSelectionChanged += RoundTimeSelecterOnOnSelectionChanged;
            RoundTimeSelecter.SetOptions(options, defaultOption);
        }

        public void HideAndDeInit()
        {
            Hide();
            RoundTimeSelecter.ClearAllListener();
        }

        protected override void OnBeforeDestroy()
        {
            HideAndDeInit();
            Clear();
        }
        
        private void RoundTimeSelecterOnOnSelectionChanged(int index, SelectionOption option)
        {
            GetArchitecture().GetModel<ClientRoomSettingModel>().RoomSettingDataConfig.RoundDuration =
                OptionToRoundDuration();
        }
               
        private float OptionToRoundDuration()
        {
            switch (this.RoundTimeSelecter.CurrentOption.Value)
            {
                case "30":
                    return 30;
                case "60":
                    return 60;
                case "90":
                    return 90;
                case "120":
                    return 120;
                case "150":
                    return 150;
                case "Unlimited":
                    return 999999;
                default:
                    return 0;
            }
        }
        
        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
    }
}
