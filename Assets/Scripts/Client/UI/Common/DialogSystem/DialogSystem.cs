using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using QFramework;
using UnityEngine;
using Object = UnityEngine.Object;

namespace TFG.UI.Common.DialogSystem
{
    public class DialogSystem : AbstractSystem, IDialogSystem
    {
        // 使用 SemaphoreSlim(1, 1) 作为异步锁，确保同一时间只有一个弹窗在显示
        private readonly SemaphoreSlim _dialogLock = new SemaphoreSlim(1, 1);
        private DialogStyleRegistrySO _styleRegistry;
        private Transform _uiRoot;
        private DialogView _currentDialogView;
        private CancellationTokenSource _dialogQueueCts = new CancellationTokenSource();
        private ResLoader _resLoader;

        // 用于手动缓存已加载的弹窗预制件
        private readonly Dictionary<string, GameObject> _prefabCache = new Dictionary<string, GameObject>();

        protected override void OnInit()
        {
            _resLoader = ResLoader.Allocate();
            var uiRootGo = UIKit.Root;
            if (uiRootGo != null)
            {
                _uiRoot = uiRootGo.transform;
            }
            else
            {
                LogKit.W($"[{GetType().FullName}] Cannot Find UIRoot");
            }
        }

        protected override void OnDeinit()
        {
            base.OnDeinit();
            _prefabCache.Clear();
            _resLoader.Recycle2Cache();
            _resLoader = null;
            _dialogQueueCts.Cancel();
            _dialogQueueCts.Dispose();
        }

        public async UniTask<DialogResult> Show(string styleName, string title, string content, bool showConfirm = true,
            bool showCancel = true, bool useBlocker = true, string confirmText = null, string cancelText = null, bool allowMuti = false)
        {
            bool lockAcquired = false;
            try
            {
                if (allowMuti)
                {
                    // 【排队行为】如果允许排队，则无限等待锁
                    await _dialogLock.WaitAsync(_dialogQueueCts.Token);
                    lockAcquired = true;
                }
                else
                {
                    // 【非排队行为】默认行为，尝试立即获取锁，超时时间为0
                    lockAcquired = await _dialogLock.WaitAsync(0, _dialogQueueCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                // 等待被取消 (因为CloseAllDialogs被调用)
                return DialogResult.Cancel;
            }

            // 【新增】如果锁没有被获取，说明已有弹窗且不允许排队，直接拒绝请求
            if (!lockAcquired)
            {
                LogKit.I($"[{GetType().FullName}] A dialog is already active and queueing is not allowed. The new request for style '{styleName}' was rejected.");
                return DialogResult.Rejected; // 或者可以定义一个 DialogResult.Rejected
            }

            // 只有成功获取锁之后，才执行真正的弹窗逻辑，并确保锁会被释放
            try
            {
                // 懒加载DialogStyleRegistrySO
                if (_styleRegistry == null)
                {
                    // 关键：第一次调用Show时，异步加载注册表
                    _styleRegistry = await _resLoader.LoadAssetUniTask<DialogStyleRegistrySO>("DialogStyleRegistry");
                    if (_styleRegistry == null)
                    {
                        LogKit.E($"[{GetType().FullName}] Failed to load 'DialogStyleRegistry' from ResLoader!");
                        return DialogResult.Cancel;
                    }
                }

                if (_uiRoot == null)
                {
                    LogKit.W($"[{GetType().FullName}] DialogSystem Is Not Inited (UIRoot is null)!");
                    return DialogResult.Cancel;
                }

                var style = _styleRegistry.GetStyle(styleName);
                if (style == null || string.IsNullOrEmpty(style.DialogPrefabName))
                {
                    LogKit.W($"[{GetType().FullName}] Cannot Find DialogStyle or PrefabName is empty for '{styleName}'!");
                    return DialogResult.Cancel;
                }

                // 缓存加载资源并实例化资源
                GameObject dialogPrefab;
                if (!_prefabCache.TryGetValue(styleName, out dialogPrefab))
                {
                    // 缓存未命中，异步加载
                    dialogPrefab = await _resLoader.LoadAssetUniTask<GameObject>(style.DialogPrefabName);
                    if (dialogPrefab == null)
                    {
                        LogKit.E($"[{GetType().FullName}] Failed to load prefab '{style.DialogPrefabName}' for style '{styleName}'!");
                        return DialogResult.Cancel;
                    }

                    // 加载成功，存入缓存
                    _prefabCache[styleName] = dialogPrefab;
                }

                // 使用获取到的预制件（无论是来自缓存还是新加载的）进行实例化
                var dialogInstance = Object.Instantiate(dialogPrefab, UIKit.Root.PopUI);
                var dialogView = dialogInstance.GetComponent<DialogView>();
                if (dialogView == null)
                {
                    LogKit.W($"[{GetType().FullName}::DialogSystem] Cannot Find DialogView!");
                    Object.Destroy(dialogInstance);
                    return DialogResult.Cancel;
                }

                _currentDialogView = dialogView;
                var finalConfirmText = !string.IsNullOrEmpty(confirmText) ? confirmText : style.DefaultConfirmText;
                var finalCancelText = !string.IsNullOrEmpty(cancelText) ? cancelText : style.DefaultCancelText;
                var tcs = new UniTaskCompletionSource<DialogResult>();
                dialogView.Init(title, content, showConfirm, showCancel, useBlocker, tcs, finalConfirmText, finalCancelText);
                if (showConfirm || showCancel)
                {
                    // 模式1: 交互式对话框 (有按钮)
                    // 等待用户交互，任务结束后才释放锁和引用
                    try
                    {
                        return await tcs.Task;
                    }
                    finally
                    {
                        _currentDialogView = null;
                        _dialogLock.Release();
                    }
                }
                else
                {
                    // 模式2: 指令性提示框 (无按钮)
                    _dialogLock.Release();
                    // 返回一个中性值，让调用方(UIDialogHelper)的fire-and-forget逻辑继续
                    return DialogResult.Cancel; 
                }
            }
            catch(Exception e)
            {
                // 确保异常情况下锁也能被释放
                LogKit.I($"[{GetType().FullName}::Show] An exception occurred during Show: {e}"); 
                if (lockAcquired) _dialogLock.Release();
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 关闭当前正在显示的对话框
        /// </summary>
        public void CloseCurrentDialog()
        {
            if (_currentDialogView != null)
            {
                _currentDialogView.CloseDialog();
                _currentDialogView = null; 
            }
        }

        /// <summary>
        /// 关闭所有对话框并清空队列
        /// </summary>
        public void CloseAllDialogs()
        {
            _dialogQueueCts.Cancel();
            _dialogQueueCts.Dispose();
            _dialogQueueCts = new CancellationTokenSource();
            CloseCurrentDialog();
        }
    }
}
