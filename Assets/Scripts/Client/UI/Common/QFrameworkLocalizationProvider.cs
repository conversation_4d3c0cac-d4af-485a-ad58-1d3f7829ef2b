using Client.System.SettingSystem;
using GameClient;
using UnityEngine;

namespace TFG.UI.Common
{

    public class QFrameworkLocalizationProvider : MonoBehaviour,ILocalizationProvider
    {
        private LocalizationSystem _system;

        public void Awake()
        {
            _system = LaunchMainArch.Interface.GetSystem<LocalizationSystem>();
        }

        public string GetLocalizedString(string key, string fallback = null)
        {
            string result = _system.GetLocalizationContent(key);
            return (result == key && fallback != null) ? fallback : result;
        }
    }
}
