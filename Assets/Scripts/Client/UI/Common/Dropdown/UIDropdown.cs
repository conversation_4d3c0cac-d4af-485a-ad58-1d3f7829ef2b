/* description: 一个功能完备的下拉选择框 (Dropdown/ComboBox)
 * 1. 点击热区可展开/折叠选项面板
 * 2. 支持带搜索功能的动态列表
 * 3. 列表项由独立的Prefab动态生成
 * 4. 使用DOTween和UniTask处理流畅的面板动画
 * 5. 架构清晰，易于扩展和维护
 * 6. 列表中明确高亮当前选中项
 * 7. 通过事件与外部UI解耦，实现选择结果的联动
 * keywords: UI, Dropdown, ComboBox, Search, UGUI, DOTween, UniTask
 */

using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using QFramework;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace TFG.UI.Common
{
    public class UIDropdown : UIBehaviour
    {
        public string ComponentName => this.GetType().ToString();

        [Header("UI元素引用")] [Tooltip("折叠时的主交互区域按钮")]
        public UISmartButton HotspotButton;

        [Tooltip("热区上显示当前选项图标的Image")] public Image CurrentSelectionIcon;
        [Tooltip("热区上显示当前选项文本")] public TextMeshProUGUI CurrentSelectionText;
        //[Tooltip("热区上显示当前选项文本的附加文本")] public TextMeshProUGUI CurrentSelectionAddText;
        [Tooltip("展开的下拉面板根对象")] public RectTransform DropdownPanel;
        [Tooltip("搜索输入框")] public TMP_InputField SearchInputField;
        [Tooltip("用于动态生成列表项的父容器")] public RectTransform ContentArea;

        [Tooltip("单个列表项的预制件，必须挂载UIDropdownItem脚本")]
        public UIDropdownItem OptionItemPrefab;

        [Header("是否使用SO配置载入数据源")] [SerializeField] private bool useSOConfig = true;
        [Tooltip("当 useSOConfig 为 true 时，需要加载的 DropdownOptionsSO 资源名")] [SerializeField] private string DropdownSO_AssetName;
        [Header("当使用SO的时候的数据源")] [SerializeField] private List<DropdownOption> m_options = new List<DropdownOption>();

        [Header("动画设置")] [Tooltip("面板展开/折叠的动画时长")]
        public float AnimationDuration = 0.2f;

        [Header("默认设置")] [Tooltip("启动时默认选中的选项索引")]
        public int DefaultIndex = 0;
        /// <summary>
        /// 在下拉面板即将开始展开动画前触发。
        /// </summary>
        public event Action OnPanelWillOpen;
        
        /// <summary>
        /// 在下拉面板完全关闭（动画结束且对象失活）后触发。
        /// </summary>
        public event Action OnAfterPanelClose;

        public event Action<int, DropdownOption> OnSelectionChanged;

        // 内部状态
        private int m_currentIndex = -1;
        private bool m_isPanelOpen = false;
        private readonly List<UIDropdownItem> m_instantiatedItems = new List<UIDropdownItem>();
        private CancellationTokenSource m_animationCts;
        private CanvasGroup m_panelCanvasGroup;
        public int CurrentIndex => m_currentIndex;

        public DropdownOption CurrentOption =>
            (m_currentIndex >= 0 && m_currentIndex < m_options.Count) ? m_options[m_currentIndex] : null;
        /// <summary>
        /// 获取当前选中项的 minLit 值。如果没有选中项，则返回3。
        /// </summary>
        public int CurrentMinLit => CurrentOption?.minLit ?? 3;
        /// <summary>
        /// 获取当前选中项的 maxLit 值。如果没有选中项，则返回11。
        /// </summary>
        public int CurrentMaxLit => CurrentOption?.maxLit ?? 11;
        private ILocalizationProvider m_localizationProvider; //多语言支持

        private GameObject m_blocker;
        private ResLoader m_resLoader;
        protected override void Awake()
        {
            base.Awake();
            m_resLoader = ResLoader.Allocate();
            // 获取或添加必要的组件
            m_panelCanvasGroup = DropdownPanel.GetComponent<CanvasGroup>();
            if (m_panelCanvasGroup == null) m_panelCanvasGroup = DropdownPanel.gameObject.AddComponent<CanvasGroup>();

            // 初始隐藏面板
            m_panelCanvasGroup.alpha = 0;
            m_panelCanvasGroup.interactable = false;
            m_panelCanvasGroup.blocksRaycasts = false;
            DropdownPanel.gameObject.SetActive(false);
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            HotspotButton.OnClickEvent += TogglePanel;
            SearchInputField.onValueChanged.AddListener(FilterOptions);
        }

        protected override void Start()
        {
            base.Start();
            if (m_currentIndex == -1)
            {
                if (useSOConfig)
                {
                    OnStartLoadSO().Forget();
                }
                else
                {
                    SetOptions(m_options, DefaultIndex);
                }
            }
        }

        private async UniTask OnStartLoadSO()
        {
            if (string.IsNullOrEmpty(DropdownSO_AssetName))
            {
                LogKit.E($"[{ComponentName}] 'useSOConfig' is true, but 'DropdownSO_AssetName' is empty!");
                return;
            }

            var configSO = await m_resLoader.LoadAssetUniTask<DropdownOptionsSO>(DropdownSO_AssetName);
            LogKit.I($"[{GetType().FullName}::OnStartLoadSO] 已成功加载配置DropdownSO_AssetName");
            if (configSO == null)
            {
                LogKit.E($"[{ComponentName}] Failed to load DropdownOptionsSO with name: {DropdownSO_AssetName}");
                return;
            }
            SetOptions(configSO.Options, DefaultIndex);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            HotspotButton.OnClickEvent -= TogglePanel;
            SearchInputField.onValueChanged.RemoveListener(FilterOptions);
            m_animationCts?.Cancel();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            m_resLoader.Recycle2Cache();
            m_resLoader = null;
            m_animationCts?.Cancel();
            m_animationCts?.Dispose();
            m_animationCts = null;
            OnSelectionChanged = null;
            OnPanelWillOpen = null;
            OnAfterPanelClose = null;
            
            if (m_blocker != null)
            {
                Destroy(m_blocker);
                m_blocker = null;
            }
        }
        
        public void SetLocalizationProvider(ILocalizationProvider provider)
        {
            m_localizationProvider = provider;
            
            // 如果已经有数据，则刷新显示以应用新的语言
            if (m_currentIndex != -1)
            {
                UpdateHotspotDisplay();
                PopulateList(); // 重新生成列表以应用新语言
            }
        }

        public void SetOptions(List<DropdownOption> newOptions, int defaultIndex = 0)
        {
            m_options = newOptions ?? new List<DropdownOption>();
            PopulateList();
            int selectionIndex = Mathf.Clamp(defaultIndex, 0, m_options.Count - 1);
            if (m_options.Count > 0)
            {
                SelectWithoutNotify(selectionIndex);
            }
        }

        public void Select(int index)
        {
            if (index < 0 || index >= m_options.Count) return;
            
            // 如果点击的是已选项，则仅关闭面板
            if (index == m_currentIndex)
            {
                if (m_isPanelOpen) HidePanelAsync().Forget();
                return;
            }
            
            m_currentIndex = index;
            UpdateHotspotDisplay();
            UpdateSelectionVisuals();
            OnSelectionChanged?.Invoke(m_currentIndex, CurrentOption);

            if (m_isPanelOpen) HidePanelAsync().Forget();
        }

        public void SelectWithoutNotify(int index)
        {
            if (index < 0 || index >= m_options.Count || index == m_currentIndex) return;
            
            m_currentIndex = index;
            UpdateHotspotDisplay();
            UpdateSelectionVisuals(); // 更新列表项的选中状态
            
            if (m_isPanelOpen) HidePanelAsync().Forget();
        }
        public int GetIndexByFind(string fitter)
        {
            if (string.IsNullOrEmpty(fitter) || m_options == null || m_options.Count == 0)
            {
                return -1;
            }
            return m_options.FindIndex(option => option.additionalText == fitter);
        }

        public void ClearAllEvents()
        {
            OnSelectionChanged = null;
            OnPanelWillOpen = null;
            OnAfterPanelClose = null;
        }

        private void TogglePanel()
        {
            if (m_isPanelOpen)
                HidePanelAsync().Forget();
            else
                ShowPanelAsync().Forget();
        }

        private async UniTaskVoid ShowPanelAsync()
        {
            if (m_isPanelOpen) return;
            OnPanelWillOpen?.Invoke();
            m_isPanelOpen = true;
            m_animationCts?.Cancel();
            m_animationCts?.Dispose(); 
            m_animationCts = new CancellationTokenSource();
            CreateBlocker();
            if (m_instantiatedItems.Count == 0) PopulateList();
            DropdownPanel.gameObject.SetActive(true);
            m_panelCanvasGroup.blocksRaycasts = true;
            m_panelCanvasGroup.interactable = true;
            await m_panelCanvasGroup.DOFade(1f, AnimationDuration).SetEase(Ease.OutCubic)
                .ToUniTask(cancellationToken: m_animationCts.Token);
        }

        private async UniTaskVoid HidePanelAsync()
        {
            if (!m_isPanelOpen) return;
            m_isPanelOpen = false;
            m_animationCts?.Cancel();
            m_animationCts?.Dispose(); 
            m_animationCts = new CancellationTokenSource();
            DestroyBlocker();
            m_panelCanvasGroup.blocksRaycasts = false;
            m_panelCanvasGroup.interactable = false;
            await m_panelCanvasGroup.DOFade(0f, AnimationDuration).SetEase(Ease.InCubic)
                .ToUniTask(cancellationToken: m_animationCts.Token);
            if (!m_isPanelOpen) // 确保在动画期间没有再次被打开
            {
                DropdownPanel.gameObject.SetActive(false);
                OnAfterPanelClose?.Invoke();
            }
        }

        // 创建Blocker的方法
        private void CreateBlocker()
        {
            if (m_blocker != null) return;

            var canvas = this.GetComponentInParent<Canvas>();
            if (canvas == null)
            {
                LogKit.E("UIDropdown需要在一个Canvas下才能创建Blocker。");
                return;
            }

            m_blocker = new GameObject("Dropdown_Blocker");
            var blockerRect = m_blocker.AddComponent<RectTransform>();
            blockerRect.SetParent(canvas.transform, false);
            blockerRect.anchorMin = Vector2.zero;
            blockerRect.anchorMax = Vector2.one;
            blockerRect.sizeDelta = Vector2.zero;

            // 将Blocker置于DropdownPanel之下
            blockerRect.SetSiblingIndex(DropdownPanel.transform.GetSiblingIndex());

            var blockerImage = m_blocker.AddComponent<Image>();
            blockerImage.color = Color.clear; // 完全透明

            var blockerButton = m_blocker.AddComponent<Button>();
            blockerButton.onClick.AddListener(() => HidePanelAsync().Forget());
        }

        // 销毁Blocker的方法
        private void DestroyBlocker()
        {
            if (m_blocker != null)
            {
                Destroy(m_blocker);
                m_blocker = null;
            }
        }
        private void PopulateList()
        {
            foreach (var item in m_instantiatedItems) Destroy(item.gameObject);
            m_instantiatedItems.Clear();

            for (int i = 0; i < m_options.Count; i++)
            {
                var optionData = m_options[i];
                var itemInstance = Instantiate(OptionItemPrefab, ContentArea);
                // 将本地化提供者传递给列表项
                itemInstance.Setup(optionData, i, OnOptionItemSelected, m_localizationProvider); 
                //itemInstance.Setup(optionData, i, OnOptionItemSelected);
                m_instantiatedItems.Add(itemInstance);
            }
            
            // 列表生成后，立即更新一次选中状态
            UpdateSelectionVisuals();
        }

        /// <summary>
        /// 更新所有列表项的选中状态指示器
        /// </summary>
        private void UpdateSelectionVisuals()
        {
            for (int i = 0; i < m_instantiatedItems.Count; i++)
            {
                m_instantiatedItems[i].SetSelectedState(i == m_currentIndex);
            }
        }
        private void OnOptionItemSelected(int index)
        {
            Select(index);
        }

        private void FilterOptions(string searchTerm)
        {
            string lowerSearchTerm = searchTerm.ToLower();
            foreach (var item in m_instantiatedItems)
            {
                // 搜索逻辑现在会同时匹配主标题和描述文本
                bool isMatch = string.IsNullOrEmpty(lowerSearchTerm) || 
                               item.DisplayText.text.ToLower().Contains(lowerSearchTerm) ||
                               (item.DescriptionText != null && item.DescriptionText.text.ToLower().Contains(lowerSearchTerm))||
                               (item.AdditionalText != null && item.AdditionalText.text.ToLower().Contains(lowerSearchTerm));
                item.gameObject.SetActive(isMatch);
            }
        }

        private void UpdateHotspotDisplay()
        {
            if (CurrentOption == null) return;
            if (CurrentSelectionText != null)
            {
                string textToDisplay = CurrentOption.displayText; // 默认使用后备文本
                if (m_localizationProvider != null && !string.IsNullOrEmpty(CurrentOption.LocalizationKey))
                {
                    textToDisplay = m_localizationProvider.GetLocalizedString(CurrentOption.LocalizationKey, CurrentOption.displayText);
                }
                CurrentSelectionText.text = textToDisplay;
            }

            //if (CurrentSelectionAddText != null) CurrentSelectionAddText.text = CurrentOption.additionalText;
            if (CurrentSelectionIcon != null)
            {
                CurrentSelectionIcon.sprite = CurrentOption.displayIcon;
                CurrentSelectionIcon.gameObject.SetActive(CurrentOption.displayIcon != null);
            }
        }
    }
}
