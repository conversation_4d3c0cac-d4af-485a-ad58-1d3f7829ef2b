using System;
using Cysharp.Threading.Tasks;
using TFG.UI.Common;
using UnityEngine;

namespace TFG.UI
{
    /// <summary>
    /// 控制设置状态 - 对应"控制设置"标签页
    /// </summary>
    [Serializable]
    public class StateControlSettings : UIStateBase
    {
        private UIBattleSettingPanel _panel;
        
        public void BindPanel(UIBattleSettingPanel parent) => _panel = parent;
        
        public override UIStateBase ParentState { get; set; } = null;

        public override async UniTask Enter(object data = null)
        {
            //首先要做的是隐藏老的控件组避免穿帮
            if (_panel.ScrollRect.content!=null)
            {
                _panel.ScrollRect.content.GetComponent<CanvasGroup>().DisableView();
            }
            //之后重置位置
            _panel.ScrollRect.verticalNormalizedPosition = 1;//1 is start(top)
            //接下来切换到对应的控件组
            _panel.ScrollRect.content = _panel.ControlSetting.GetComponent<RectTransform>();
            
            //code here
            var config = _panel.panelConfig;
            var currentSettings = _panel.currentRoomSetting;
            var showInputStrictness = config.HasControlSetting && config.SetActionPrecision;

            _panel.InputStrictnessComponent.OnValueChanged += OnInputStrictnessChanged;
            
            _panel.InputStrictnessComponent.InitAndShow(showInputStrictness, config.DefaultInputStrictnessRange.x, config.DefaultInputStrictnessRange.y, currentSettings.InputStrictness);
            //再次重置位置确保正常
            _panel.ScrollRect.verticalNormalizedPosition = 1;//1 is start(top)
            //显示对应的控件组
            _panel.ControlSetting.EnableView();
            await UniTask.CompletedTask;
        }

        public override async UniTask Exit()
        {
            _panel.InputStrictnessComponent.HideAndDeInit();
            _panel.InputStrictnessComponent.OnValueChanged -= OnInputStrictnessChanged;
            
            // 离开控制设置状态时的清理逻辑
            await UniTask.CompletedTask;
        }
        
        private void OnInputStrictnessChanged(float value) => _panel.clientRoomSettingModel.RoomSettingDataConfig.InputStrictness = value;


        public override void OnUpdate()
        {
            // 控制设置状态的更新逻辑
        }
    }
} 
