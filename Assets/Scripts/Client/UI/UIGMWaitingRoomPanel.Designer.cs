using TFG.UI.Common;
using TMPro;
using UnityEngine;
using UnityEngine.UIElements;

namespace TFG.UI
{
    public partial class UIGMWaitingRoomPanel
    {
        /// <summary>
        /// 面板名称常量
        /// </summary>
        public const string Name = "UIGMWaitingRoomPanel";

        [SerializeField] private TMP_Text _roomNanmeText; //房间名
        [SerializeField] private TMP_Text _bluePlayerNanmeText; //蓝色方玩家名
        [SerializeField] private TMP_Text _redPlayerNanmeText; //红色方玩家名
        [SerializeField] private TMP_Text _ownerIdNameText; //房主玩家名
        [SerializeField] private TMP_Text _blueReadyState; //蓝色方准备状态
        [SerializeField] private TMP_Text _redReadyState; //红色准备状态
        
        [SerializeField] private UISelectionCycler _mapSelecter; //地图选择器
        [SerializeField] private UISelectionCycler _roundDrationSelecter; //回合时长选择器
        [SerializeField] private UISelectionCycler _matchModeSelecter; //赛制选择器（bo3 bo5）
        
        [SerializeField] private UISmartButton _exitRoomBtn; //退出房间按钮
        [SerializeField] private UISmartButton _startGameBtn; //开始游戏按钮
        [SerializeField] private UISmartButton _roomSettingBtn; //房间设置按钮
        
        public TMP_Text RoomNameText => _roomNanmeText;
        public TMP_Text BluePlayerNanmeText => _bluePlayerNanmeText;
        public TMP_Text BlueReadyState => _blueReadyState;
        public TMP_Text RedPlayerNanmeText => _redPlayerNanmeText;
        public TMP_Text RedReadyState => _redReadyState;
        public TMP_Text OwnerIdNameText => _ownerIdNameText;

        public UISelectionCycler MapSelecter => _mapSelecter;
        public UISelectionCycler RoundDrationSelecter => _roundDrationSelecter;
        public UISelectionCycler MatchModeSelecter => _matchModeSelecter;
        
        public UISmartButton ExitRoomBtn => _exitRoomBtn;
        public UISmartButton StartGameBtn => _startGameBtn;
        public UISmartButton RoomSettingBtn => _roomSettingBtn;
        
        private UIGMWaitingRoomPanelData mPrivateData = null;
		
        /// <summary>
        /// 清理UI组件引用
        /// </summary>
        protected override void ClearUIComponents()
        {
            mData = null;
            _roomNanmeText = null;
            _bluePlayerNanmeText = null;
            _redPlayerNanmeText = null;
            _ownerIdNameText = null;
            _blueReadyState = null;
            _redReadyState = null;

            _mapSelecter = null;
            _roundDrationSelecter = null;
            _matchModeSelecter = null;
            
            _exitRoomBtn = null;
            _startGameBtn = null;
            _roomSettingBtn = null;
        }
		
        /// <summary>
        /// 获取面板数据
        /// </summary>
        public UIGMWaitingRoomPanelData Data
        {
            get { return mData; }
        }
		
        UIGMWaitingRoomPanelData mData
        {
            get { return mPrivateData ?? (mPrivateData = new UIGMWaitingRoomPanelData()); }
            set { mUIData = value; mPrivateData = value; }
        }
    }
}
