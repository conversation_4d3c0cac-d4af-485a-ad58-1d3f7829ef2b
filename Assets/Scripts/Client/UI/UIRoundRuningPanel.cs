using UnityEngine;
using UnityEngine.UI;
using QFramework;
using Client.Model.Battle;
using GameClient;
using GameClient.System;
using System.Collections;
using Client.Event;
using GameClient.Helper;
using GameShare.System;
using TMPro;
using System;
using Client.Command.Battle;
using Cysharp.Threading.Tasks;
using TFG.Shared;

namespace TFG.UI
{
	public class UIRoundRuningPanelData : UIPanelData
	{
	}
	
	public partial class UIRoundRuningPanel : UIPanel, ICanGetModel, ICanGetSystem, ICanRegisterEvent
	{
		private float _updateTimer = 0.0f;
		private const float UPDATE_INTERVAL = 0.1f; // 更新间隔，秒
		private float _localRoundTimer = 0.0f;  // 本地倒计时
		private bool _isPaused = false;  // 暂停状态
        
        // 保存事件注册的引用，以便在关闭时取消注册
        //private IUnRegister _pauseEventRegister;
        private readonly UnRegisterList unRegisterHelper = new ();
		
		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as UIRoundRuningPanelData ?? new UIRoundRuningPanelData();
		}
		
		protected override void OnOpen(IUIData uiData = null)
		{
			// 初始化UI显示
			InitializeLocalTimer();
			
			// 注册按钮事件
			BtnTimeOut.onClick.AddListener(OnTimeOutButtonClick);
			
			// 注册事件监听
			this.RegisterEvent<BattlePauseEvent>(OnBattlePauseEvent).AddToUnregisterList(unRegisterHelper);
			this.RegisterEvent<DamageEffectEvent>(OnDamageEffectEvent).AddToUnregisterList(unRegisterHelper);
			this.RegisterEvent<EnergyUpdateEvent>(OnEnergyUpdateEvent).AddToUnregisterList(unRegisterHelper);
			
			// 打开烬火登临UI面板
			//OpenFlameEmberUI();
			
			// 开始UI更新
			StartCoroutine(UpdateUI());
		}
		
		/// <summary>
		/// 初始化本地倒计时
		/// </summary>
		private void InitializeLocalTimer()
		{
			// 获取当前战斗
			var battleModel = this.GetModel<IClientBattleModel>();
			var currentBattle = battleModel.GetCurrentBattle();
			
			if (currentBattle == null)
			{
				LogKit.E("[UIRoundRuningPanel] 找不到当前战斗数据");
				return;
			}
			
			// 从战斗模型中获取初始倒计时和暂停状态
			_localRoundTimer = currentBattle.RoundTimer;
			_isPaused = currentBattle.IsPaused;
			
			UpdateTimerDisplay();
		}
		
		private IEnumerator UpdateUI()
		{
			while (true)
			{
				_updateTimer += Time.deltaTime;
				
				if (_updateTimer >= UPDATE_INTERVAL && !_isPaused)
				{
					_updateTimer = 0;
					// 只在非暂停状态下更新倒计时
					_localRoundTimer -= UPDATE_INTERVAL;
					if (_localRoundTimer < 0) _localRoundTimer = 0;
					UpdateTimerDisplay();
				}
				
				yield return null;
			}
		}
		
		/// <summary>
		/// 更新计时器显示
		/// </summary>
		private void UpdateTimerDisplay()
		{
			// 更新计时器显示
			int minutes = Mathf.FloorToInt(_localRoundTimer / 60.0f);
			int seconds = Mathf.FloorToInt(_localRoundTimer % 60.0f);
			TxtTimer.text = $"{minutes:00}:{seconds:00}";
			
			// 获取当前战斗
			var battleModel = this.GetModel<IClientBattleModel>();
			var currentBattle = battleModel.GetCurrentBattle();
			
			if (currentBattle != null)
			{
				if (currentBattle.PlayerNumericModels.Count >= 2)
				{
					// 更新玩家血量显示
					TxtPlayer1Health.text = $"玩家1血量: {currentBattle.PlayerNumericModels[currentBattle.Player1Id].GetAsInt(NumericType.Hp)}";
					TxtPlayer2Health.text = $"玩家2血量: {currentBattle.PlayerNumericModels[currentBattle.Player2Id].GetAsInt(NumericType.Hp)}";
					TxtPlayer1Energy.text = $"玩家1怒气: {currentBattle.PlayerNumericModels[currentBattle.Player1Id].GetAsFloat(NumericType.Energy)}";
					TxtPlayer2Energy.text = $"玩家2怒气: {currentBattle.PlayerNumericModels[currentBattle.Player2Id].GetAsFloat(NumericType.Energy)}";
				}
	
				
				// 更新暂停按钮文本
				var buttonText = BtnTimeOut.GetComponentInChildren<TextMeshProUGUI>();
				if (buttonText != null)
				{
					buttonText.text = _isPaused ? "继续" : "暂停";
				}
			}
		}
		
		/// <summary>
		/// 暂停按钮点击处理
		/// </summary>
		private void OnTimeOutButtonClick()
		{
			// 获取当前战斗
			var battleModel = this.GetModel<IClientBattleModel>();
			var currentBattle = battleModel.GetCurrentBattle();
			
			if (currentBattle == null)
			{
				LogKit.E("[UIRoundRuningPanel] 找不到当前战斗数据");
				return;
			}
			
			// 获取玩家ID
			var playerSystem = this.GetSystem<ClientPlayerSystem>();
			if (playerSystem == null || playerSystem.LocalPlayerModel.LocalSelfPlayerData == null)
			{
				LogKit.E("[UIRoundRuningPanel] 找不到本地玩家数据");
				return;
			}
			
			string playerId = playerSystem.LocalPlayerModel.LocalSelfPlayerData.PlayerId;
			
			// 发送暂停/恢复请求
			bool requestPause = !_isPaused;  // 切换暂停状态
			ClientBattleMessageHelper.SendBattlePauseRequest(currentBattle.BattleId, requestPause);
			
			LogKit.I($"[UIRoundRuningPanel] 请求{(requestPause ? "暂停" : "恢复")}战斗: BattleId={currentBattle.BattleId}, PlayerId={playerId}");
		}
		
		/// <summary>
		/// 处理战斗暂停/恢复事件
		/// </summary>
		private void OnBattlePauseEvent(BattlePauseEvent e)
		{
			// 获取当前战斗
			var battleModel = this.GetModel<IClientBattleModel>();
			var currentBattle = battleModel.GetCurrentBattle();
			
			// 只处理当前战斗的暂停事件
			if (currentBattle != null && e.BattleId == currentBattle.BattleId)
			{
				_isPaused = e.IsPause;
				_localRoundTimer = currentBattle.RoundTimer;
				UpdateTimerDisplay();
				LogKit.I($"[UIRoundRuningPanel] 战斗{(_isPaused ? "暂停" : "恢复")}: BattleId={e.BattleId}");
			}
		}

		private void OnDamageEffectEvent(DamageEffectEvent e)
		{
			// LogKit.I($"[UIRoundRuningPanel] 处理伤害事件: SourceUnitId={e.SourceUnitId}, TargetUnitId={e.TargetUnitId}, DamageValue={e.DamageValue}");
			// var battleModel = this.GetModel<IClientBattleModel>();
			// var currentBattle = battleModel.GetCurrentBattle();
			// if (currentBattle != null)
			// {
			// 	if(currentBattle.PlayerHealths.ContainsKey(e.TargetUnitId))
			// 	{
			// 		currentBattle.PlayerHealths[e.TargetUnitId] -= e.DamageValue;
			// 	}
			// }
		}
		
		private void  OnEnergyUpdateEvent(EnergyUpdateEvent e)
		{
			// var battleModel = this.GetModel<IClientBattleModel>();
			// var currentBattle = battleModel.GetCurrentBattle();
			// if (currentBattle != null)
			// {
			// 	if (currentBattle.PlayerEnergys.ContainsKey(e.PlayerId))
			// 	{
			// 		currentBattle.PlayerEnergys[e.PlayerId] = (int)e.EnergyValue;
			// 	}
			// }
		}
		
		/// <summary>
		/// 打开烬火登临UI
		/// </summary>
		private void OpenFlameEmberUI()
		{
			// 获取当前玩家ID
			var playerSystem = this.GetSystem<ClientPlayerSystem>();
			if (playerSystem?.LocalPlayerModel?.LocalSelfPlayerData != null)
			{
				string playerId = playerSystem.LocalPlayerModel.LocalSelfPlayerData.PlayerId;
				
				// 打开烬火登临UI面板
				var flameEmberData = new TFG.UI.UIFireHeroPanelData
				{
					PlayerId = playerId
				};
				
				UIKit.OpenPanel<TFG.UI.UIFireHeroPanel>(flameEmberData).Forget();
				
				Debug.Log($"[UIRoundRuningPanel] 为玩家 {playerId} 打开烬火登临UI");
			}
		}
		
		protected override void OnShow()
		{
		}
		
		protected override void OnHide()
		{
		}
		
		protected override void OnClose()
		{
			// 取消按钮事件
			BtnTimeOut.onClick.RemoveListener(OnTimeOutButtonClick);
			
			// 取消事件注册
			unRegisterHelper.UnRegisterAll();
			
			// 停止所有协程
			StopAllCoroutines();
		}
        
        public IArchitecture GetArchitecture()
        {
            return GameClient.LaunchMainArch.Interface;
        }
	}
}
