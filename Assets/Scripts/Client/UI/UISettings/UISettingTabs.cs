using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace TFG.UI
{
	public class UISettingTabsData : UIPanelData
	{
	}
	public partial class UISettingTabs : UIPanel
	{
		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as UISettingTabsData ?? new UISettingTabsData();
			// please add init code here
		}
		
		protected override void OnOpen(IUIData uiData = null)
		{
		}
		
		protected override void OnShow()
		{
		}
		
		protected override void OnHide()
		{
		}
		
		protected override void OnClose()
		{
		}
	}
}
