using System.Configuration;
using Client.System.SettingSystem;
using GameClient;
using GameClient.System;
using GameClient.System.ConfigSystem;
using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace TFG.UI
{
	public class UIAudioSettingsPanelData : UIPanelData
	{
	}
	public partial class UIAudioSettingsPanel : SettingPanelBase
	{
		private ClientSettingsSystem _system; 

		public override void Init()
		{
			base.Init();
			_system = LaunchMainArch.Interface.GetSystem<ClientSettingsSystem>(); 
			
			OnDeinit(); 
			UIInitialize(); 
			LoadCache(); 
		}

		protected override void OnOpen(IUIData uiData = null)
		{
			base.OnOpen(uiData);
			OnInit();
		}

		protected override void OnShow()
		{
			gameObject.SetActive(true);
			Init();
		}

		protected override void OnHide()
		{
			gameObject.SetActive(false);
		}


		protected override void OnDeinit()
		{
			Slider_MasterVolume.UnregisterAllListeners();
			Slider_MusicVolume.UnregisterAllListeners();
			Slider_VoiceVolume.UnregisterAllListeners();
			Slider_UIVolume.UnregisterAllListeners();
		}

		protected override void UIInitialize()
		{
			// 范围赋值
			Slider_MasterVolume.Init(new UISliderData(GetRange(1).Item1, GetRange(1).Item2,GetRange(1).Item3));
			Slider_MusicVolume.Init(new UISliderData(GetRange(2).Item1, GetRange(2).Item2,GetRange(2).Item3));
			Slider_VoiceVolume.Init(new UISliderData(GetRange(3).Item1, GetRange(3).Item2, GetRange(3).Item3)); 
			Slider_UIVolume.Init(new UISliderData(GetRange(4).Item1, GetRange(4).Item2, GetRange(4).Item3)); 
			
			// 事件绑定  
			Slider_MasterVolume.UISliderValueChangedCallback += OnMasterVolumeValueChanged; 
			Slider_MusicVolume.UISliderValueChangedCallback += OnMusicVolumeValueChanged;
			Slider_VoiceVolume.UISliderValueChangedCallback += OnVoiceVolumeValueChanged;
			Slider_UIVolume.UISliderValueChangedCallback += OnUIVolumeValueChanged; 
		}

		private (int,int,int) GetRange(int idx)
		{
			var data = LaunchMainArch.Interface.GetModel<ConfigModel>().
				AllTables.Settings.DataMap; 
			return (int.Parse((data[idx].Scale[0])), 
				int.Parse(data[idx].Scale[1]), 
				int.Parse(data[idx].Default));
		}

		public override void ResetSettingsValue()
		{
			_system.UpdateValue(ClientSettingsConstants.MASTER_VOLUME, null);
			_system.UpdateValue(ClientSettingsConstants.MUSIC_VOLUME, null);
			_system.UpdateValue(ClientSettingsConstants.VOICE_VOLUME, null);
			_system.UpdateValue(ClientSettingsConstants.UI_VOLUME, null); 
		}

		protected override void LoadCache()
		{
			var data = LaunchMainArch.Interface.GetModel<ClientSettingsModel>().Data;

			Slider_MasterVolume.Slider.value = data.MasterVolume;
			Slider_MusicVolume.Slider.value = data.MusicVolume;
			Slider_VoiceVolume.Slider.value = data.VoiceVolume;
			Slider_UIVolume.Slider.value = data.UserInterfaceVolume; 
		}

		private void OnUIVolumeValueChanged(int value)
		{
			_system.UpdateValue(ClientSettingsConstants.UI_VOLUME, value.ToString());
		}

		private void OnVoiceVolumeValueChanged(int value)
		{
			_system.UpdateValue(ClientSettingsConstants.VOICE_VOLUME, value.ToString());
		}

		private void OnMusicVolumeValueChanged(int value)
		{
			_system.UpdateValue(ClientSettingsConstants.MUSIC_VOLUME, value.ToString());
		}

		private void OnMasterVolumeValueChanged(int value)
		{
			_system.UpdateValue(ClientSettingsConstants.MASTER_VOLUME, value.ToString());
		}
		
	}
}
