using System.Collections.Generic;
using System.Linq;
using Client.System.SettingSystem;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.Command.GameSettings;
using GameClient.System;
using GameMain;
using UnityEngine.UI;
using QFramework;

namespace TFG.UI
{
	public class UISettingsData : UIPanelData
	{
	}
	public partial class UISettings : UIPanel
	{
		private readonly Dictionary<Button, SettingPanelBase> _panels = new();
		public Button Select
		{
			get => _currentSelect;  
			set => OnPanelSelect(value);
		}

		private Button _currentSelect = null; 

		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as UISettingsData ?? new UISettingsData();
			// please add init code here
		}
		
		protected override void OnOpen(IUIData uiData = null)
		{
			BeforePanelsInitialize(); 
			PanelsInitialize();  
			
			Btn_Back.onClick.RemoveAllListeners();
			Btn_Back.onClick.AddListener(async () => { 
				await ExitSettings();
			});
			
			Btn_ResetSettings.onClick.RemoveAllListeners();
			Btn_ResetSettings.onClick.AddListener(() =>
			{
				LaunchMainArch.Interface.SendCommand(new
					ShowDialogCommand("DialogResetSettings", ResetCurrentSettings)); 
			});
		}

		private void BeforePanelsInitialize()
		{
			_panels.Clear();
			var platform = GameRootArch.Interface.GetSystem<PlatformSystem>(); 	
			_panels.Add(UISettingTabs.Btn_AudioSettings, UIAudioSettingsPanel);  
			_panels.Add(UISettingTabs.Btn_GeneralSettings, UIGeneralSettingsPanel);  
			_panels.Add(UISettingTabs.Btn_LanguageSettings, UILanguageSettingsPanel);

			//====== Calibration 模块为 VR 端特有；Graphics 模块为 PC 端特有；===================================//  
			if (platform.IsPlatformXr())
			{
				UISettingTabs.Btn_GraphicSettings.gameObject.SetActive(false);
				_panels.Add(UISettingTabs.Btn_CalibrationSettings, UICalibrationPanel);
			}
			else
			{
				UISettingTabs.Btn_CalibrationSettings.gameObject.SetActive(false); 
				_panels.Add(UISettingTabs.Btn_GraphicSettings, UIGraphicsPanel); 
			}

		}

		private void PanelsInitialize()
		{
			foreach (var kvp in _panels)
			{
				var button = kvp.Key; 
				button.onClick.AddListener(() =>
				{
					Select =  button; 
				});
				kvp.Value.Init();
			}
			
			Select = UISettingTabs.Btn_GeneralSettings;  
		}
		
		private void ResetCurrentSettings()
		{
			_panels[_currentSelect].ResetSettingsValue();  
			_panels[_currentSelect].Init();
		}

		private async UniTask ExitSettings()
		{
			CloseSelf();
			await UIKit.OpenPanel<UILoginPanel>();
		}

		private void OnPanelSelect(Button button)
		{
			if(button == null) return; 
			_currentSelect = button;
			_panels[button]?.Show(); 
			foreach (var kvp in _panels.Where(kvp => button != kvp.Key))
			{
				kvp.Value.Hide(); 
			}
		}

		protected override void OnShow()
		{
		}
		
		protected override void OnHide()
		{
		}
		
		protected override void OnClose()
		{
			foreach (var kvp in _panels)
			{
				kvp.Value.OnClosed(() =>
				{
					
				});
			}
		}
	}
}
