using Client.System.SettingSystem;
using ClientConfig.config;
using GameClient;
using GameClient.System;
using GameClient.System.ConfigSystem;
using GameMain;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using SimpleJSON;

namespace TFG.UI
{
    public class UIGeneralSettingsPanelData : UIPanelData
    {
    }

    public partial class UIGeneralSettingsPanel : SettingPanelBase
    {
        private ClientSettingsSystem _system;

        public override void Init()
        {
            base.Init();
            _system = LaunchMainArch.Interface.GetSystem<ClientSettingsSystem>();

            OnDeinit();
            UIInitialize();
            LoadCache();
        }


        protected override void UIInitialize()
        {
            var data = LaunchMainArch.Interface.GetModel<ConfigModel>().AllTables.Settings.DataMap;
            // 非vr平台 
            if (!GameRoot.Instance.GetSystem<PlatformSystem>().IsPlatformXr())
            {
                Toggle_MotionBlur.transform.parent.gameObject.SetActive(false);
                Toggle_Vibratory.transform.parent.gameObject.SetActive(false);
                SelectBox_VibratorySettings.transform.parent.gameObject.SetActive(false);
                var scale = data[13].Scale;
                Slider_ScreenBrightness.Init(new UISliderData(int.Parse(scale[0]), int.Parse(scale[1]),
                    int.Parse(data[13].Default)));
                //Frame Rate Display 
                Toggle_FrameRateDisplay.Init();
                Toggle_FrameRateDisplay.OnToggleValueChangedCallback += OnFrameRateDisplayChanged;
                return;
            }

            Slider_ScreenBrightness.transform.parent.gameObject.SetActive(false);
            Toggle_FrameRateDisplay.transform.parent.gameObject.SetActive(false);
            
            Slider_ScreenBrightness.UISliderValueChangedCallback += OnScreenBrightnessValueChanged;


            Toggle_MotionBlur.Init();
            Toggle_MotionBlur.OnToggleValueChangedCallback += OnMotionBlurStateChanged;

            Toggle_Vibratory.Init();
            Toggle_Vibratory.OnToggleValueChangedCallback += OnVibratoryStateChanged;

            var vibratoryOptions = data[19].Scale;
            SelectBox_VibratorySettings.Init(new SelectBoxData(vibratoryOptions));
            SelectBox_VibratorySettings.OnSelectBoxValueChangedCallback += OnVibratorySettingsChanged;
        }


        // 战斗中可能出现的动态模糊效果的控制 
        private void OnMotionBlurStateChanged(bool state)
        {
            _system.UpdateValue(ClientSettingsConstants.MOTION_BLUR, state.ToString());
        }

        private void OnVibratoryStateChanged(bool state)
        {
            _system.UpdateValue(ClientSettingsConstants.VIBRATORY, state.ToString());
            SelectBox_VibratorySettings.SetEnabled(state);
        }

        private void OnVibratorySettingsChanged(int index)
        {
            _system.UpdateValue(ClientSettingsConstants.VIBRATORY_SETTINGS, SelectBox_VibratorySettings.CurrentValue);
        }

        private void OnScreenBrightnessValueChanged(int value)
        {
            _system.UpdateValue(ClientSettingsConstants.SCREEN_BRIGHTNESS, value.ToString());
        }

        private void OnFrameRateDisplayChanged(bool state)
        {
            _system.UpdateValue(ClientSettingsConstants.FRAME_RATE_DISPLAY, state.ToString());
        }

        /// <summary>
        /// 恢复默认设置 
        /// </summary>
        public override void ResetSettingsValue()
        {
            _system.UpdateValue(ClientSettingsConstants.FRAME_RATE_DISPLAY, null);
            _system.UpdateValue(ClientSettingsConstants.SCREEN_BRIGHTNESS, null);
            _system.UpdateValue(ClientSettingsConstants.VIBRATORY, null);
            _system.UpdateValue(ClientSettingsConstants.VIBRATORY_SETTINGS, null);
            _system.UpdateValue(ClientSettingsConstants.MOTION_BLUR, null);
        }

        protected override void LoadCache()
        {
            var data = LaunchMainArch.Interface.GetModel<ClientSettingsModel>().Data;

            Toggle_FrameRateDisplay.SetToggleState(data.FrameRateDisplay);
            Slider_ScreenBrightness.Slider.value = data.ScreenBrightness;

            Toggle_MotionBlur.SetToggleState(data.MotionBlur);
            Toggle_Vibratory.SetToggleState(data.Vibratory);
            SelectBox_VibratorySettings.SetSelectValue(data.VibratorySettings);
        }

        protected override void OnDeinit()
        {
            Toggle_MotionBlur.UnregisterAllListeners();
            Toggle_Vibratory.UnregisterAllListeners();
            SelectBox_VibratorySettings.UnregisterAllListeners();
            Slider_ScreenBrightness.UnregisterAllListeners();
            Toggle_FrameRateDisplay.UnregisterAllListeners();
        }


        protected override void OnShow()
        {
            gameObject.SetActive(true);
            Init();
        }

        protected override void OnHide()
        {
            gameObject.SetActive(false);
        }

        protected override void OnClose()
        {
        }
    }
}
