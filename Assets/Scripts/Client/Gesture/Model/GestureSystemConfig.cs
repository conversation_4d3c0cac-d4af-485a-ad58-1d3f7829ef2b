using QFramework;
using TFGShare.Utility;
using UnityEngine;

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势系统配置参数
    /// </summary>
    [CreateAssetMenu(fileName = "GestureSystemConfig", menuName = "TFG/Gesture System Config")]
    public class GestureSystemConfig : ScriptableObject
    {
        /// <summary>
        /// 手势数据存储路径常量
        /// </summary>
        public const string GESTURE_DATA_PATH = "../GestureData";

        /// <summary>
        /// 录制频率常量（Hz）
        /// </summary>
        public const float RECORDING_FREQUENCY = 50f;

        [Header("Recording Settings")]
        [Tooltip("最大录制时长（秒）")]
        public float maxRecordingDuration = 10f;
        
        [Tooltip("最小录制时长（秒）")]
        public float minRecordingDuration = 0.5f;
        
        [Tooltip("采样率（Hz）")]
        public float samplingRate = 50f;
        
        [Header("Recognition Settings")]
        [Tooltip("相似度阈值")]
        [Range(0f, 1f)]
        public float similarityThreshold = 0.8f;
        
        [Tooltip("识别间隔（秒）")]
        public float recognitionInterval = 0.1f;
        
        [Tooltip("缓冲区大小")]
        public int bufferSize = 100;
        
        [Tooltip("相似度保持显示时间（秒）")]
        public float similarityDisplayDuration = 5f;
        
        [Header("Playback Settings")]
        [Tooltip("默认播放速度")]
        public float playbackSpeed = 1f;
        
        [Tooltip("默认循环播放")]
        public bool loopPlayback = false;
        
        [Header("Algorithm Settings")]
        [Tooltip("位置权重")]
        [Range(0f, 1f)]
        public float positionWeight = 0.7f;

        [Tooltip("旋转权重")]
        [Range(0f, 1f)]
        public float rotationWeight = 0.3f;

        [Header("Performance Monitoring")]
        [Tooltip("启用性能监控和详细日志")]
        public bool enablePerformanceMonitoring = false;

        [Tooltip("启用识别耗时统计")]
        public bool enableTimingStats = false;

        [Tooltip("启用相似度详细日志")]
        public bool enableSimilarityLogging = false;

        [Tooltip("启用缓冲区状态监控")]
        public bool enableBufferMonitoring = false;

        [Tooltip("启用内存使用监控")]
        public bool enableMemoryMonitoring = false;

        [Header("Continuous Recognition Settings")]
        [Tooltip("相似度识别阈值")]
        [Range(0.1f, 1.0f)]
        public float recognitionThreshold = 0.8f;

        [Tooltip("数据缓冲区最大大小")]
        [Range(50, 500)]
        public int maxBufferSize = 200;

        [Tooltip("最小数据点数量（开始识别所需）")]
        [Range(5, 50)]
        public int minDataPoints = 10;

        [Tooltip("滑动窗口步长")]
        [Range(1, 10)]
        public int slidingWindowStepSize = 1;

        [Tooltip("启用早期退出优化")]
        public bool enableEarlyExit = true;

        [Tooltip("性能统计记录数量")]
        [Range(50, 500)]
        public int performanceStatsCount = 100;

        [Tooltip("内存监控间隔（识别次数）")]
        [Range(10, 200)]
        public int memoryMonitoringInterval = 50;

        [Tooltip("归一化序列长度")]
        public int normalizedSequenceLength = 50;
        
        /// <summary>
        /// 验证配置参数的有效性
        /// </summary>
        private void OnValidate()
        {
            // 确保权重总和为1
            var totalWeight = positionWeight + rotationWeight;
            if (Mathf.Abs(totalWeight - 1f) > 0.01f)
            {
                var ratio = 1f / totalWeight;
                positionWeight *= ratio;
                rotationWeight *= ratio;
            }
            
            // 确保其他参数在合理范围内
            maxRecordingDuration = Mathf.Max(0.1f, maxRecordingDuration);
            minRecordingDuration = Mathf.Max(0.1f, minRecordingDuration);
            samplingRate = Mathf.Clamp(samplingRate, 1f, 120f);
            similarityDisplayDuration = Mathf.Max(0.1f, similarityDisplayDuration);
            playbackSpeed = Mathf.Clamp(playbackSpeed, 0.1f, 5f);
            normalizedSequenceLength = Mathf.Max(10, normalizedSequenceLength);

            // 连续识别参数验证
            recognitionThreshold = Mathf.Clamp(recognitionThreshold, 0.1f, 1.0f);
            recognitionInterval = Mathf.Clamp(recognitionInterval, 0.05f, 1.0f);
            maxBufferSize = Mathf.Clamp(maxBufferSize, 50, 500);
            minDataPoints = Mathf.Clamp(minDataPoints, 5, 50);
            slidingWindowStepSize = Mathf.Clamp(slidingWindowStepSize, 1, 10);
            performanceStatsCount = Mathf.Clamp(performanceStatsCount, 50, 500);
            memoryMonitoringInterval = Mathf.Clamp(memoryMonitoringInterval, 10, 200);

            // 确保minDataPoints不超过maxBufferSize的一半
            if (minDataPoints > maxBufferSize / 2)
            {
                minDataPoints = maxBufferSize / 2;
            }
        }

        /// <summary>
        /// 运行时调整识别阈值
        /// </summary>
        /// <param name="newThreshold">新的阈值</param>
        public void SetRecognitionThreshold(float newThreshold)
        {
            recognitionThreshold = Mathf.Clamp(newThreshold, 0.1f, 1.0f);
            LogKit.I($"[GestureSystemConfig] Recognition threshold updated to: {recognitionThreshold:F2}");
        }

        /// <summary>
        /// 运行时调整识别间隔
        /// </summary>
        /// <param name="newInterval">新的间隔时间</param>
        public void SetRecognitionInterval(float newInterval)
        {
            recognitionInterval = Mathf.Clamp(newInterval, 0.05f, 1.0f);
            LogKit.I($"[GestureSystemConfig] Recognition interval updated to: {recognitionInterval:F3}s");
        }

        /// <summary>
        /// 运行时调整缓冲区大小
        /// </summary>
        /// <param name="newSize">新的缓冲区大小</param>
        public void SetMaxBufferSize(int newSize)
        {
            maxBufferSize = Mathf.Clamp(newSize, 50, 500);
            // 确保minDataPoints不超过新缓冲区大小的一半
            if (minDataPoints > maxBufferSize / 2)
            {
                minDataPoints = maxBufferSize / 2;
            }
            LogKit.I($"[GestureSystemConfig] Max buffer size updated to: {maxBufferSize}, Min data points adjusted to: {minDataPoints}");
        }

        /// <summary>
        /// 获取当前配置的摘要信息
        /// </summary>
        /// <returns>配置摘要字符串</returns>
        public string GetConfigSummary()
        {
            return $"Recognition Config - Threshold: {recognitionThreshold:F2}, Interval: {recognitionInterval:F3}s, " +
                   $"Buffer: {maxBufferSize}, MinData: {minDataPoints}, StepSize: {slidingWindowStepSize}";
        }
    }
}
