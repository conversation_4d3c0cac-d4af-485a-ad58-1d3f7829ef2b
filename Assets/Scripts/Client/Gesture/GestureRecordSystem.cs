using System;
using System.Collections.Generic;
using System.IO;
using GameShare.Events;
using GameShare.Utility;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;
#if UNITY_EDITOR
using Newtonsoft.Json;
using UnityEditor;
#endif

namespace GameClient.Gesture
{
    /// <summary>
    /// 手势录制系统，负责手势数据的录制、存储和管理
    /// </summary>
    public class GestureRecordSystem : AbstractSystem
    {
        #region 私有字段
        
        /// <summary>
        /// 手势系统配置
        /// </summary>
        private GestureSystemConfig _config;
        
        /// <summary>
        /// 当前是否正在录制
        /// </summary>
        private bool _isRecording = false;
        
        /// <summary>
        /// 录制的移动数据列表
        /// </summary>
        private readonly List<MovementData> _recordedMovements = new();
        
        /// <summary>
        /// 当前手势名称
        /// </summary>
        private string _currentGestureName = "NewGesture";

        /// <summary>
        /// 头部朝向模式（是否启用头部朝向相对化）
        /// </summary>
        private bool _useHeadOrientation = true;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取当前是否正在录制
        /// </summary>
        public bool IsRecording => _isRecording;

        /// <summary>
        /// 获取已录制的数据数量
        /// </summary>
        public int RecordedDataCount => _recordedMovements.Count;

        /// <summary>
        /// 获取当前手势名称
        /// </summary>
        public string CurrentGestureName => _currentGestureName;

        /// <summary>
        /// 获取当前头部朝向模式
        /// </summary>
        public bool UseHeadOrientation => _useHeadOrientation;

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置头部朝向模式
        /// </summary>
        /// <param name="useHeadOrientation">是否启用头部朝向相对化</param>
        public void SetHeadOrientationMode(bool useHeadOrientation)
        {
            _useHeadOrientation = useHeadOrientation;
            LogKit.I($"[{GetType().FullName}::SetHeadOrientationMode] Head orientation mode set to: {useHeadOrientation}");
        }

        #endregion

        #region 系统生命周期
        
        /// <summary>
        /// 系统初始化
        /// </summary>
        protected override void OnInit()
        {
            try
            {
                // 加载手势系统配置
                _config = Resources.Load<GestureSystemConfig>("GestureSystemConfig");
                if (_config == null)
                {
                    LogKit.E($"[{GetType().FullName}::OnInit] Failed to load GestureSystemConfig");
                    return;
                }
                
                // 重要：不在这里注册BattlePlayerMovementEvent
                // 事件注册将在StartRecording()中进行，以实现精确的事件控制
                
                LogKit.I($"[{GetType().FullName}::OnInit] GestureRecordSystem initialized successfully");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnInit] Error during initialization: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 系统清理
        /// </summary>
        protected override void OnDeinit()
        {
            try
            {
                // 如果正在录制，停止录制并清理
                if (_isRecording)
                {
                    LogKit.W($"[{GetType().FullName}::OnDeinit] Recording was active during deinit, stopping recording");
                    // 注意：这里不直接调用StopRecording()，因为它可能依赖其他已清理的资源
                    _isRecording = false;
                    // 手动注销事件（如果已注册）
                    try
                    {
                        this.UnRegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);
                    }
                    catch
                    {
                        // 忽略注销失败，可能事件未注册
                    }
                }
                
                // 清理录制数据
                _recordedMovements.Clear();
                
                LogKit.I($"[{GetType().FullName}::OnDeinit] GestureRecordSystem deinitialized successfully");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::OnDeinit] Error during cleanup: {ex.Message}");
            }
        }
        
        #endregion

        #region 公共接口方法

        /// <summary>
        /// 开始手势录制
        /// </summary>
        /// <param name="gestureName">手势名称，如果为空则使用时间戳格式</param>
        /// <returns>是否成功开始录制</returns>
        public bool StartGestureRecording(string gestureName = null)
        {
            try
            {
                if (_isRecording)
                {
                    LogKit.W($"[{GetType().FullName}::StartGestureRecording] Already recording");
                    return false;
                }

                if (string.IsNullOrEmpty(gestureName))
                {
                    _currentGestureName = $"Gesture_{DateTime.Now:yyyyMMdd_HHmmss}";
                }
                else
                {
                    _currentGestureName = gestureName;
                }

                StartRecording();
                return true;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::StartGestureRecording] Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止手势录制
        /// </summary>
        /// <returns>是否成功停止录制</returns>
        public bool StopGestureRecording()
        {
            try
            {
                if (!_isRecording)
                {
                    LogKit.W($"[{GetType().FullName}::StopGestureRecording] Not recording");
                    return false;
                }

                StopRecording();
                return true;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::StopGestureRecording] Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存录制的手势数据
        /// </summary>
        /// <returns>是否成功保存</returns>
        public bool SaveGestureRecording()
        {
            try
            {
                if (_recordedMovements.Count == 0)
                {
                    LogKit.W($"[{GetType().FullName}::SaveGestureRecording] No data to save");
                    return false;
                }

                SaveRecordingToJson();
                return true;
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::SaveGestureRecording] Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清除录制数据
        /// </summary>
        public void ClearRecordingData()
        {
            try
            {
                if (_isRecording)
                {
                    LogKit.W($"[{GetType().FullName}::ClearRecordingData] Cannot clear data while recording");
                    return;
                }

                _recordedMovements.Clear();
                LogKit.I($"[{GetType().FullName}::ClearRecordingData] Recording data cleared");
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::ClearRecordingData] Error: {ex.Message}");
            }
        }

        #endregion

        #region 录制核心功能方法

        /// <summary>
        /// 开始录制（内部实现）
        /// </summary>
        private void StartRecording()
        {
            _isRecording = true;
            _recordedMovements.Clear();

            this.RegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);

            LogKit.I($"[{GetType().FullName}::StartRecording] Started recording gesture: {_currentGestureName}");
        }

        /// <summary>
        /// 停止录制（内部实现）
        /// </summary>
        private void StopRecording()
        {
            _isRecording = false;

            this.UnRegisterEvent<BattlePlayerMovementEvent>(OnBattlePlayerMovement);

            LogKit.I($"[{GetType().FullName}::StopRecording] Stopped recording, collected {_recordedMovements.Count} data points");
        }

        /// <summary>
        /// 保存录制数据到手势文件
        /// </summary>
        private void SaveRecordingToJson()
        {
            try
            {
                if (_recordedMovements.Count == 0)
                {
                    LogKit.W($"[{GetType().FullName}::SaveRecordingToJson] No data to save");
                    return;
                }

                // 使用DatFileManager保存手势数据，应用头部朝向模式
                var selectedJoints = new List<TrajectoryExtractor.JointIndex>
                {
                    TrajectoryExtractor.JointIndex.Head,
                    TrajectoryExtractor.JointIndex.LeftHand,
                    TrajectoryExtractor.JointIndex.RightHand
                };

                var savedPath = DatFileManager.SaveDatFile(_currentGestureName, _recordedMovements, selectedJoints, _useHeadOrientation);

                if (!string.IsNullOrEmpty(savedPath))
                {
                    LogKit.I($"[{GetType().FullName}::SaveRecordingToJson] Gesture saved to: {savedPath} (HeadOrientation: {_useHeadOrientation})");
                }
                else
                {
                    LogKit.E($"[{GetType().FullName}::SaveRecordingToJson] Failed to save gesture data");
                }

#if UNITY_EDITOR
                // 可选：同时保存为JSON文件用于调试
                var debugSavePath = EditorUtility.SaveFilePanel("保存调试数据", "", "recording_debug", "json");
                if (!string.IsNullOrEmpty(debugSavePath))
                {
                    var json = JsonConvert.SerializeObject(_recordedMovements, JsonConverterHelper.GetDefaultSettings());
                    File.WriteAllText(debugSavePath, json);
                    LogKit.I($"[{GetType().FullName}::SaveRecordingToJson] Debug data saved to: {debugSavePath}");
                }
#endif
            }
            catch (Exception ex)
            {
                LogKit.E($"[{GetType().FullName}::SaveRecordingToJson] Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理玩家移动事件
        /// </summary>
        private void OnBattlePlayerMovement(BattlePlayerMovementEvent e)
        {
            // 由于事件只在录制时注册，这里不需要再检查_isRecording状态
            if (e?.MovementData != null)
            {
                _recordedMovements.Add(e.MovementData);
            }
        }

        #endregion
    }
}
