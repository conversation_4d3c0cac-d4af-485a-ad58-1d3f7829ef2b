using System;
using System.Collections.Generic;
using GameShare.Character;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient.Character
{
    /// <summary>
    /// Client端角色碰撞组件 - 简化版本，主要负责碰撞体的创建和基础管理
    /// </summary>
    public class CharacterCollision : MonoBehaviour
    {
        private static readonly Dictionary<Collider, ClientCharacterCollider> _dictColliders = new();
        private readonly List<ClientCharacterCollider> _colliders = new();
        private string _ownerId;
        private Animator _animator;
        private bool _isInitialized = false;
        private static bool _layerMatrixSetup = false;

        /// <summary>
        /// 初始化碰撞组件
        /// </summary>
        /// <param name="ownerId">角色拥有者ID</param>
        /// <param name="animator">角色动画器</param>
        public void Initialize(string ownerId, Animator animator)
        {
            if (_isInitialized)
            {
                LogKit.W(CharacterCollisionUtility.CreateLogMessage(GetType(), "Initialize", $"碰撞组件已经初始化过了: {ownerId}"));
                return;
            }

            CharacterCollisionUtility.SafeExecute(() =>
            {
                _ownerId = ownerId;
                _animator = animator;
                _isInitialized = true;

                CreateColliders();

                // 确保碰撞矩阵只设置一次（全局设置）
                if (!_layerMatrixSetup)
                {
                    CharacterCollisionUtility.SetupLayerCollisionMatrix();
                    _layerMatrixSetup = true;
                }
            }, GetType(), "Initialize", $"初始化Client端碰撞组件: {ownerId}");
        }

        /// <summary>
        /// 创建所有碰撞体
        /// </summary>
        private void CreateColliders()
        {
            if (!CharacterCollisionUtility.ValidateAnimator(_animator, GetType(), "CreateColliders"))
            {
                return;
            }

            CharacterCollisionUtility.SafeExecute(() =>
            {
                // 创建不同类型的碰撞体
                CreateCollidersOfType(CharacterColliderType.Striker, PrimitiveType.Capsule);
                CreateCollidersOfType(CharacterColliderType.Blocker, PrimitiveType.Capsule);
                CreateCollidersOfType(CharacterColliderType.HitBox, PrimitiveType.Capsule);
                CreateCollidersOfType(CharacterColliderType.DetectorBox, PrimitiveType.Cube);

                // 设置自身碰撞忽略
                SetupSelfCollisionIgnore();
            }, GetType(), "CreateColliders", "创建碰撞体");

            // 在SafeExecute执行完成后记录实际创建的数量
            LogKit.I(CharacterCollisionUtility.CreateLogMessage(GetType(), "CreateColliders", $"成功创建了 {_colliders.Count} 个碰撞体"));
        }

        /// <summary>
        /// 创建指定类型的碰撞体
        /// </summary>
        private void CreateCollidersOfType(CharacterColliderType colliderType, PrimitiveType primitiveType)
        {
            var colliderDefs = CharacterColliderDefs.GetColliderTransformDef(colliderType);
            if (colliderDefs == null)
            {
                return;
            }

            var layerToSet = CharacterColliderDefs.GetColliderTypeLayer(colliderType);
            foreach (var kvp in colliderDefs)
            {
                var bone = kvp.Key;
                var colliderDef = kvp.Value;
                var parentBone = _animator.GetBoneTransform(bone);

                if (parentBone == null)
                {
                    LogKit.W(CharacterCollisionUtility.CreateLogMessage(GetType(), "CreateCollidersOfType", $"找不到骨骼: {bone}"));
                    continue;
                }

                // 创建碰撞体GameObject
                var colliderObj = CreateColliderGameObject(colliderDef, layerToSet, colliderType, parentBone, primitiveType);
                var thisCollider = colliderObj.GetComponent<Collider>();
                
                // 添加Client端碰撞组件
                var clientCollider = colliderObj.AddComponent<ClientCharacterCollider>();
                clientCollider.Initialize(_ownerId, colliderType, colliderDef.GameBodyPart, thisCollider);


                _dictColliders[thisCollider] = clientCollider;
                _colliders.Add(clientCollider);
            }
        }

        /// <summary>
        /// 查找客户端角色碰撞体
        /// </summary>
        /// <param name="collider">碰撞器</param>
        /// <returns>对应的客户端角色碰撞体，如果不存在返回null</returns>
        public static ClientCharacterCollider FindClientCharacterCollider(Collider collider)
        {
            return collider == null ? null : _dictColliders.GetValueOrDefault(collider);
        }

        /// <summary>
        /// 创建碰撞体GameObject
        /// </summary>
        private GameObject CreateColliderGameObject(ColliderTransformDef colliderDef, int layerToSet, 
            CharacterColliderType colliderType, Transform parentBone, PrimitiveType primitiveType)
        {
            var colliderObj = GameObject.CreatePrimitive(primitiveType);
            
            Destroy(colliderObj.GetComponent<MeshRenderer>());
            Destroy(colliderObj.GetComponent<MeshFilter>());
            
            // 设置名称和图层
            colliderObj.layer = layerToSet;
            colliderObj.name = $"{colliderDef.GameBodyPart}_{colliderType}";

            // 设置变换属性
            colliderObj.transform.parent = parentBone;
            colliderObj.transform.localPosition = colliderDef.LocalPosition;
            colliderObj.transform.localRotation = Quaternion.Euler(colliderDef.LocalEulerRotation);
            colliderObj.transform.localScale = colliderDef.LocalScale;

            // 添加刚体组件
            var rigid = colliderObj.AddComponent<Rigidbody>();
            // 使用ClientCharacterCollider的公共方法调用基类的SetupRigidbody
            var clientCollider = colliderObj.GetComponent<ClientCharacterCollider>();
            if (clientCollider != null)
            {
                clientCollider.SetupRigidbodyPublic(rigid, colliderType);
            }

            return colliderObj;
        }



        /// <summary>
        /// 设置自身碰撞忽略
        /// </summary>
        private void SetupSelfCollisionIgnore()
        {
            for (var i = 0; i < _colliders.Count; i++)
            {
                for (var j = i + 1; j < _colliders.Count; j++)
                {
                    var collider1 = _colliders[i].Collider;
                    var collider2 = _colliders[j].Collider;
                    
                    if (collider1 != null && collider2 != null)
                    {
                        Physics.IgnoreCollision(collider1, collider2, true);
                    }
                }
            }
        }
        
        /// <summary>
        /// 销毁所有碰撞体
        /// </summary>
        public void DestroyColliders()
        {
            CharacterCollisionUtility.SafeExecute(() =>
            {
                foreach (var collider in _colliders)
                {
                    if (collider?.Collider != null)
                    {
                        _dictColliders.Remove(collider.Collider);
                    }

                    if (collider != null && collider.gameObject != null)
                    {
                        UnityEngine.Object.Destroy(collider.gameObject);
                    }
                }
                _colliders.Clear();
                _isInitialized = false;
            }, GetType(), "DestroyColliders", $"销毁碰撞体: {_ownerId}");
        }

        /// <summary>
        /// 获取所有碰撞体（只读集合，避免不必要的拷贝）
        /// </summary>
        public IReadOnlyList<ClientCharacterCollider> GetAllColliders()
        {
            return _colliders.AsReadOnly();
        }

        /// <summary>
        /// 获取碰撞体数量
        /// </summary>
        public int ColliderCount => _colliders.Count;

        private void OnDestroy()
        {
            DestroyColliders();
        }
    }
}
