using UnityEngine;
using QFramework;
using System.Collections.Generic;

namespace Client.Model.Battle
{
    /// <summary>
    /// 环境场景模型接口
    /// 负责管理场景加载状态、当前场景信息和Ring对象
    /// </summary>
    public interface IEnvScenesModel : IModel
    {
        BindableProperty<string> CurrentSceneName { get; }
        BindableProperty<string> BattleSceneName { get; }
        
        BindableProperty<GameObject> CurrentRing { get; }
        BindableProperty<string> LoadingRingName { get; }
        
        BindableProperty<bool> IsSceneLoading { get; }
        
        HashSet<string> LoadingScenes { get; }
        HashSet<string> LoadedScenes { get; }
        
        void AddLoadingScene(string sceneName);
        void RemoveLoadingScene(string sceneName);
        void AddLoadedScene(string sceneName);
        void RemoveLoadedScene(string sceneName);
        void ClearSceneLists();
        
        // 常量
        string DefaultSceneName { get; }
        string DefaultRingName { get; }
    }

    public class EnvScenesModel : AbstractModel, IEnvScenesModel
    {
        // 临时修改 出包暂时使用 Galaxy 场景, Cliff_02 在PICO上运行只有5帧
#if UNITY_ANDROID && !UNITY_EDITOR
        public static string DefaultName = "Galaxy";
#else
        //public static string DefaultName = "Cliff_02";
        public static string DefaultName = "Galaxy";
#endif
        
        public static string DefaultRing = "Galaxy_Ring";

        public BindableProperty<string> CurrentSceneName { get; } = new BindableProperty<string>();
        public BindableProperty<string> BattleSceneName { get; } = new BindableProperty<string>();
        
        public BindableProperty<GameObject> CurrentRing { get; } = new BindableProperty<GameObject>();
        public BindableProperty<string> LoadingRingName { get; } = new BindableProperty<string>();
        
        public BindableProperty<bool> IsSceneLoading { get; } = new BindableProperty<bool>();
        
        private readonly HashSet<string> _loadingScenes = new HashSet<string>();
        private readonly HashSet<string> _loadedScenes = new HashSet<string>();
        
        public HashSet<string> LoadingScenes => _loadingScenes;
        public HashSet<string> LoadedScenes => _loadedScenes;
        
        public string DefaultSceneName => DefaultName;
        public string DefaultRingName => DefaultRing;

        protected override void OnInit()
        {
            CurrentSceneName.Value = string.Empty;
            BattleSceneName.Value = DefaultSceneName;
            
            CurrentRing.Value = null;
            LoadingRingName.Value = string.Empty;
            
            IsSceneLoading.Value = false;
            
            _loadingScenes.Clear();
            _loadedScenes.Clear();
        }

        public void AddLoadingScene(string sceneName)
        {
            if (!string.IsNullOrEmpty(sceneName))
            {
                _loadingScenes.Add(sceneName);
            }
        }

        public void RemoveLoadingScene(string sceneName)
        {
            if (!string.IsNullOrEmpty(sceneName))
            {
                _loadingScenes.Remove(sceneName);
            }
        }

        public void AddLoadedScene(string sceneName)
        {
            if (!string.IsNullOrEmpty(sceneName))
            {
                _loadedScenes.Add(sceneName);
            }
        }

        public void RemoveLoadedScene(string sceneName)
        {
            if (!string.IsNullOrEmpty(sceneName))
            {
                _loadedScenes.Remove(sceneName);
            }
        }

        public void ClearSceneLists()
        {
            _loadingScenes.Clear();
            _loadedScenes.Clear();
        }
    }
} 
