using QFramework;
using System.Collections.Generic;
using GameShare.Model.Battle;
using TFGShare.Protocol;

namespace Client.Model.Battle
{
    /// <summary>
    /// 客户端战斗模型接口
    /// </summary>
    public interface IClientBattleModel : ISharedBattleModel<ClientBattleData>
    {
        /// <summary>
        /// 获取当前战斗ID
        /// </summary>
        string CurrentBattleId { get; }

        /// <summary>
        /// 设置当前战斗ID
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        void SetCurrentBattleId(string battleId);

        /// <summary>
        /// 获取当前战斗数据
        /// </summary>
        /// <returns>当前战斗数据</returns>
        ClientBattleData GetCurrentBattle();

        /// <summary>
        /// 创建战斗数据
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="roomId">房间ID</param>
        /// <param name="player1Id">玩家1ID</param>
        /// <param name="player2Id">玩家2ID</param>
        void CreateBattle(string battleId, string roomId, string player1Id, string player2Id);

        /// <summary>
        /// 更新战斗状态
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="previousState">前一状态</param>
        /// <param name="currentState">当前状态</param>
        /// <param name="remainingTime">剩余时间</param>
        void UpdateBattleState(string battleId, MatchState previousState, MatchState currentState, float remainingTime);

        /// <summary>
        /// 更新回合信息
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="roundNumber">回合数</param>
        /// <param name="roundDuration">回合持续时间</param>
        /// <param name="playerHealths">玩家血量</param>
        void UpdateRoundInfo(string battleId, int roundNumber, float roundDuration, Dictionary<string, int> playerHealths);

        /// <summary>
        /// 更新回合结果
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="roundNumber">回合数</param>
        /// <param name="result">回合结果</param>
        /// <param name="playerHealths">玩家血量</param>
        /// <param name="player1Wins">玩家1获胜回合数</param>
        /// <param name="player2Wins">玩家2获胜回合数</param>
        /// <param name="intermissionDuration">休息时间</param>
        void UpdateRoundEnd(string battleId, int roundNumber, BattleResult result, Dictionary<string, int> playerHealths, int player1Wins, int player2Wins, float intermissionDuration);

        /// <summary>
        /// 更新比赛结果
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="result">比赛结果</param>
        /// <param name="player1Wins">玩家1获胜回合数</param>
        /// <param name="player2Wins">玩家2获胜回合数</param>
        /// <param name="playerScores">玩家分数</param>
        void UpdateMatchEnd(string battleId, BattleResult result, int player1Wins, int player2Wins, Dictionary<string, int> playerScores);

        /// <summary>
        /// 更新玩家血量
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="playerId">玩家ID</param>
        /// <param name="health">血量</param>
        void UpdatePlayerHealth(string battleId, string playerId, int health);

        /// <summary>
        /// 清除战斗数据
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        void ClearBattle(string battleId);

        /// <summary>
        /// 清理过期战斗数据
        /// </summary>
        void CleanupBattles();

        /// <summary>
        /// 添加回合战报
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        /// <param name="roundReport">回合战报数据</param>
        void AddRoundReport(string battleId, RoundReportData roundReport);
    }
} 
