using System;
using System.Collections.Generic;
using GameClient;
using QFramework;

namespace Client.Model
{

    //账号操作行为枚举
    public enum AccountFlowType
    {
        Login,
        ResetPassword,
        Register
    }

    //用于为账号的登录、注册、重置密码提供数据支持
    public class ClientAccountFlowModel : AbstractModel
    {
        public string CurrentEmail { get; set; } = "";
        public string CurrentPhone { get; set; } = "";
        public string CurrentCountryCode { get; set; } = "";
        public int CurrentCountryCodeIndex { get; set; } = 0;
        public CheckType CheckType { get; set; } = CheckType.Verification;
        public AccountType AccountType  { get; set; }= AccountType.PhoneNumber;
        
        public string CacheVerifyCode { get; set; }
        public string CachePassword { get; set; }
        // 用于“设置密码”流程中，临时存储第一次输入的密码
        public string FirstPasswordAttempt { get; set; }
        
        // 验证码对应的token
        public Dictionary<string, string> Account2VerifyTokenMap { get; set; } = new Dictionary<string, string>();
        // 账号请求过登录验证码
        public Dictionary<string, bool> Account2HasLoginVerify { get; set; } = new Dictionary<string, bool>();
        public Dictionary<string, bool> Account2HasRegisterVerify { get; set; } = new Dictionary<string, bool>();
        public Dictionary<string, bool> Account2HasResetVerify { get; set; } = new Dictionary<string, bool>();

        public string GetEmailAccount() => this.CurrentEmail;
        public string GetPhoneAccount() => string.Concat("+", this.CurrentCountryCode, "-", this.CurrentPhone);

        /// <summary>
        /// 从持久化存储中加载指定流程的用户偏好到本流程模型。
        /// </summary>
        /// <param name="flowType">要加载哪个流程的偏好数据（登录、注册或重置密码）</param>
        public void LoadFromPreferences(AccountFlowType flowType)
        {
            var prefsModel = LaunchMainArch.Interface.GetModel<ClientAccountPreferencesModel>();
            var preferenceData = prefsModel.GetPreferencesForFlow(flowType);
            UpdateFlowAccount(preferenceData.LastEmail, preferenceData.LastPhone,preferenceData.LastCountryCodeIndex);
        }

        /// <summary>
        /// 在流程中更新当前操作的账户信息。
        /// </summary>
        private void UpdateFlowAccount(string email = "", string phone = "", int countryCodeIndex = 0)
        {
            CurrentEmail = email;
            CurrentPhone = phone;
            CurrentCountryCodeIndex = countryCodeIndex;
        }

        public bool HasRequestedVerificationCode(string account,AccountFlowType accountFlowType)
        { 
            var targetDictionary = accountFlowType switch
            {
                AccountFlowType.Login => Account2HasLoginVerify,
                AccountFlowType.ResetPassword => Account2HasResetVerify,
                AccountFlowType.Register => Account2HasRegisterVerify,
                _ => null // 对于未知类型，返回null
            };
            if (targetDictionary == null)
            {
                return false;
            }
            targetDictionary.TryGetValue(account, out bool hasRequested);
            return hasRequested;
        }

        public void SaveVerification(string account,string token,AccountFlowType accountFlowType)
        {
            Account2VerifyTokenMap[account] = token;
            switch (accountFlowType)
            {
                case AccountFlowType.Login:
                    Account2HasLoginVerify[account] = true;
                    
                    Account2HasResetVerify[account] = false;
                    Account2HasResetVerify[account] = false;
                    break;
                case AccountFlowType.ResetPassword:
                    Account2HasResetVerify[account] = true;
                    
                    Account2HasLoginVerify[account] = false;
                    Account2HasRegisterVerify[account] = false;
                    break;
                case AccountFlowType.Register:
                    Account2HasRegisterVerify[account] = true;
                    
                    Account2HasLoginVerify[account] = false;
                    Account2HasResetVerify[account] = false;
                    break;
            }
        }
        public void ClearVerification(string account,AccountFlowType accountFlowType)
        {
            Account2VerifyTokenMap[account] = "";
            switch (accountFlowType)
            {
                case AccountFlowType.Login:
                    Account2HasLoginVerify[account] = false;
                    break;
                case AccountFlowType.ResetPassword:
                    Account2HasResetVerify[account] = false;
                    break;
                case AccountFlowType.Register:
                    Account2HasRegisterVerify[account] = false;
                    break;
            }
        }

        public void ClearVerification()
        {
            Account2VerifyTokenMap.Clear();
            Account2HasLoginVerify.Clear();
            Account2HasResetVerify.Clear();
            Account2HasRegisterVerify.Clear();
        }

        // 初始化方法，在Model被获取时调用
        protected override void OnInit()
        {
            // 这里可以进行初始化，但BindableProperty已在声明时初始化
        }

        // 提供一个清晰的重置方法
        public void ClearAccountFlowData()
        {
            CurrentEmail = "";
            CurrentPhone = "";
            CurrentCountryCode = "";
            CurrentCountryCodeIndex = 0;
            CachePassword = "";
            CacheVerifyCode = "";
            FirstPasswordAttempt = "";
        }
    }
}
