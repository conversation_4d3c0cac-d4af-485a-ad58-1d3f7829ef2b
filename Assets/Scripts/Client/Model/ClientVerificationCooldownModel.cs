using System;
using System.Collections.Generic;
using QFramework;

namespace Client.Model
{
    public class VerificationCooldownModel : AbstractModel
    {
        private readonly Dictionary<string, DateTime> _cooldownEndTimes = new Dictionary<string, DateTime>();

        // 公共方法，用于判断当前是否处于冷却中
        public bool IsInCooldown(string key)
        {
            if (_cooldownEndTimes.TryGetValue(key, out var endTime))
            {
                return DateTime.UtcNow < endTime;
            }
            return false; // 如果不存在该key的记录，则认为不在冷却中
        }

        // 获取剩余的冷却秒数
        public int GetRemainingSeconds(string key)
        {
            if (_cooldownEndTimes.TryGetValue(key, out var endTime))
            {
                if (DateTime.UtcNow < endTime)
                {
                    return (int)(endTime - DateTime.UtcNow).TotalSeconds;
                }
            }
            return 0;
        }

        // 开始一个新的冷却周期
        public void StartCooldown(string key, float durationInSeconds)
        {
            var endTime = DateTime.UtcNow.AddSeconds(durationInSeconds);
            _cooldownEndTimes[key] = endTime; // 如果key已存在则更新，不存在则添加
        }
        /// <summary>
        /// 强制重置冷却时间。
        /// </summary>
        public void ResetCooldown(string key)
        {
            if (_cooldownEndTimes.ContainsKey(key))
            {
                _cooldownEndTimes[key] = DateTime.MinValue;
                LogKit.I($"[{GetType().FullName}::ResetCooldown] Cooldown for key '{key}' has been forcibly reset.");
            }
        }
        protected override void OnInit() { }
    }
}
