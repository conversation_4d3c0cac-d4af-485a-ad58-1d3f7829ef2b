using System;
using System.Diagnostics;
using System.Net.Sockets;
using System.Threading;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.System.ConfigSystem;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;

namespace Client.Model
{
    /// <summary>
    /// 用于客户端获取与App业务无关的客户端参数
    /// 比如Fps、version、ping、所在平台、使用设备等
    /// </summary>
    public class ClientApplicationModel : AbstractModel
    {
        public string AppVersion
        {
            get => Application.version;
        }

        public ClientPlatform Platform
        {
            get
            {
#if UNITY_EDITOR
                return ClientPlatform.Editor;
#elif UNITY_STANDALONE_WIN
        return ClientPlatform.Windows;
#elif UNITY_STANDALONE_OSX
        return ClientPlatform.Mac;
#elif UNITY_STANDALONE_LINUX
        return ClientPlatform.Linux;
#elif UNITY_ANDROID
        return ClientPlatform.Android;
#elif UNITY_IOS
        return ClientPlatform.Ios;
#elif UNITY_WEBGL
        return ClientPlatform.WebGl;
#else
        Debug.LogWarning("PlatformDetector: Unhandled platform detected. Defaulting to 'Other'.");
        return ClientPlatform.Other;
#endif
            }
        }


        public int Fps
        {
            get => LaunchMainArch.Interface.GetSystem<ClientSettingsSystem>().FpsValue;
        }

        private readonly string ServerUrl = "*************";

        protected override void OnInit()
        {
        }

        /// <summary>
        /// 执行一个标准的ICMP Ping来测量到指定主机的网络延迟。
        /// </summary>
        /// <param name="host">目标主机名或IP地址</param>
        /// <param name="timeoutMilliseconds">超时时间（毫秒）</param>
        /// <param name="cancellationToken">用于从外部取消操作的CancellationToken</param>
        /// <returns>延迟时间（毫秒），-1表示失败或超时</returns>
        public async UniTask<long> PingIcmpAsync(string host = "", int timeoutMilliseconds = 2000,
            CancellationToken cancellationToken = default)
        {
            string url = host.IsNullOrEmpty() ? ServerUrl : host;
            var ping = new Ping(url);
            try
            {
                await UniTask.WaitUntil(() => ping.isDone, PlayerLoopTiming.Update, cancellationToken)
                    .Timeout(TimeSpan.FromMilliseconds(timeoutMilliseconds));
                return ping.time;
            }
            catch (OperationCanceledException)
            {
                LogKit.W($"[NetworkPinger] ICMP Ping to {host} was canceled or timed out.");
                return -1;
            }
            catch (Exception ex)
            {
                LogKit.E(
                    $"[NetworkPinger] An unexpected error occurred during ICMP Ping to {host}: {ex.Message}");
                return -1;
            }
        }
    }
}
