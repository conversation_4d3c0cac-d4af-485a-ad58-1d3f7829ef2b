
using System;
using System.Linq;
using UnityEngine;

namespace GameClient.Config
{
    [Serializable]
    public class HitLagConfig
    {
        public int MinDamage;
        public int MaxDamage;
        public AnimationCurve Curve;
        public float Duration;
    }

    [CreateAssetMenu(fileName = "HitLagSO", menuName = "TFG/ScriptableObject/HitLagSO", order = 1)]
    public class HitLagSO : ScriptableObject
    {
        public HitLagConfig[] HitLagConfigs;
        
        public HitLagConfig GetHitLagConfig(float energy)
        {
            foreach (var hitLagConfig in HitLagConfigs)
            {
                if (energy > hitLagConfig.MinDamage && energy <= hitLagConfig.MaxDamage)
                {
                    return hitLagConfig;
                }
            }

            return null;
        }
    }
}
