using System;
using System.Collections.Generic;
using QFramework;
using UnityEngine;

namespace GameClient.Controller
{
    public interface IInteractionController
    {
        //交互目标
        IEnumerable<IInteractable> InteractableTargets { get; }

        //为true时执行TryInteract
        bool OnCondition { get; }
        void TryInteract();
        void RemoveTarget(IInteractable target);
    }

    public interface IInteractable
    {
        //是否在忙碌中
        bool IsBusy { get; }

        //根据是否是成功的交互触发不同的交互样式
        bool WillInteractionSucceed { get; }
        //交互触发阈值（秒）
        float InteractionThreshold { get; }

        /// <summary>
        /// 执行交互
        /// </summary>
        /// <param name="interactor">发起交互的控制器</param>
        void OnInteract(IInteractionController interactor);

        //交互预览，比如虚影
        void OnInteractionPreview();

        //取消交互预览
        void OnInteractionCanceled();
    }

    public abstract class MonoBaseInteractionController : Mono<PERSON>ehaviour, IController, IInteractionController
    {
        public virtual IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }

        public abstract IEnumerable<IInteractable> InteractableTargets { get; }
        public abstract bool OnCondition { get; }
        public abstract void TryInteract();
        public abstract void RemoveTarget(IInteractable target);
    }
}
