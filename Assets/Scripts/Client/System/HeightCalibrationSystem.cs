using Client.Event.InputEvents;
using Client.Input;
using Client.Model;
using Client.Model.Character;
using ClientDeviceManager;
using Cysharp.Threading.Tasks;
using GameClient.Model;
using GameClient.System.ConfigSystem;
using QFramework;
using TFG.UI;
using TFG.UI.Common.DialogSystem;
using UnityEngine.InputSystem;
using XrReference;
using NotImplementedException = System.NotImplementedException;

namespace GameClient.System
{
    public class HeightCalibrationSystem : AbstractSystem
    {
        private float _height;
        private bool _caliBrationState = false; 

        protected override void OnInit()
        {
            this.RegisterEvent<InputActionPerformedEvent>(InputActionPerformed);
        }

        private void InputActionPerformed(InputActionPerformedEvent e)
        {
            if (e.ActionName == InputActionsConstant.Calibration)
            {
                CheckDualTriggerHold();
            }
        }

        private void CheckDualTriggerHold()
        {
            if (!_caliBrationState) return; 
            
            StartCalibrateSkeleton();
            var playerId = this.GetModel<LocalPlayerModel>().SelfPlayerID;
            var model = LaunchMainArch.Interface.GetModel<CharacterManagerModel>();
            if (model.CharacterDic.ContainsKey(playerId))
            {
                return;
            }

            LaunchMainArch.Interface.GetSystem<CharacterManagerSystem>().CreateCharacter(playerId, null, true).Forget();
        }

        public void StartCalibrateSkeleton(float height = -1f)
        {
            if(height < 0)
                height = DeviceManager.Instance.GetVRNodesTransform(VRDeviceNodeType.XRMainCam).transform.position.y;
            XRWrapper.Instance.CurrentDevice.CalibrateSkeleton(height);
            LogKit.I($"[HeightCalibrationSystem] Success!] 校准身高: {height}cm");
        }

        public void UpdateSkeleton(float height)
        {
            if(height < 0)
                height = DeviceManager.Instance.GetVRNodesTransform(VRDeviceNodeType.XRMainCam).transform.position.y;
            XRWrapper.Instance.CurrentDevice.UpdateSkeleton(height);
        }

        // 只有当 此状态为true 时开始调用校准  
        public void ResetCalibrationState(bool state)
        {
            _caliBrationState = state; 
        }

        protected override void OnDeinit()
        {
            base.OnDeinit();
            this.UnRegisterEvent<InputActionPerformedEvent>(InputActionPerformed);
        }

        public float GetDefaultHeight()
        {
            var model = LaunchMainArch.Interface.GetModel<ConfigModel>();
            var data = model.AllTables.Settings.DataMap;
            return float.Parse(data[16].Default);  
        }
    }
}
