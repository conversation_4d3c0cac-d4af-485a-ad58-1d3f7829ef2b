using System;

namespace Client.System.Camera
{
    public interface ITimerHandle
    {
        public event Action<object> OnElapsedEvent;
        public bool Finished { get; }
        public bool Enabled { get; }

        public void Stop();
        public void Pause();
        public void UnPause();
        public void Reset(); 
        public void Tick(float deltaTime);
        void CancelCallback(); 
    }
}
