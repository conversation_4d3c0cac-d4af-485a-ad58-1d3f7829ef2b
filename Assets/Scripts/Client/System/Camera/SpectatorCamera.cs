using System;
using System.Threading;
using Cinemachine;
using Cysharp.Threading.Tasks;
using GameClient;
using QFramework;
using UnityEngine;

namespace Client.System.Camera
{
    public class SpectatorCamera : CameraSettings
    {
        private CinemachineVirtualCamera _camera;
        private TimerHandle _timer;
        private CinemachineBasicMultiChannelPerlin _perlin;
        private CancellationTokenSource _cancellationTokenSource;

        private float _shakeTimer = 1f;
        private NoiseSettings _noiseSettings;
        private float _shakeIntensity = 0.15f;
        private float _frequencyGain = 0.2f;


        private void Awake()
        {
            _camera = gameObject.GetOrAddComponent<CinemachineVirtualCamera>();
            _perlin = _camera.GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>(); 
            _noiseSettings = _perlin.m_NoiseProfile;
        }

        private void OnEnable()
        {
            if (_camera != null)
            {
                _camera.enabled = true;
                var sideViewCam = this.GetComponent<SideCameraDriver>();
                if (sideViewCam != null)
                {
                    sideViewCam.enabled = true;
                }
            }
        }

        private void OnDisable()
        {
            if (_camera != null)
            {
                _camera.enabled = false;
                var sideViewCam = this.GetComponent<SideCameraDriver>();
                if (sideViewCam != null)
                {
                    sideViewCam.enabled = false;
                }
            }
        }

        public void SetFollowTarget(Transform target)
        {
            if (_camera != null)
            {
                _camera.Follow = target;
            }
        }

        public void ShakeCamera(NoiseSettings profile)
        {
            var timerHandle = _timer;
            if (timerHandle != null)
            {
                _timer = null;
            }
            var mainCamera = UnityEngine.Camera.main;
            if (mainCamera != null)
            {
                // 检查是否有 CinemachineBrain
                var brain = mainCamera.GetComponent<CinemachineBrain>();
                if (brain != null && brain.ActiveVirtualCamera != null)
                {
                    // 从活跃的虚拟相机获取状态
                    var activeVCam = brain.ActiveVirtualCamera;
                    transform.position = activeVCam.State.FinalPosition;
                    transform.rotation = activeVCam.State.FinalOrientation;
                }
                else
                {
                    // 直接从主相机获取位置和旋转
                    transform.position = mainCamera.transform.position;
                    transform.rotation = mainCamera.transform.rotation;
                }
            }
            
            gameObject.SetActive(true);
            _camera.Priority = 1000;
            _camera.enabled = true;
            
            // 设置震动参数
            _perlin.m_NoiseProfile = profile;
            _perlin.m_AmplitudeGain = profile.AmplitudeGain != 0 ? profile.AmplitudeGain : _shakeIntensity;
            _perlin.m_FrequencyGain = profile.FrequencyGain != 0 ? profile.FrequencyGain : _frequencyGain;
            
            float time = profile.ShakeTime; 
            _timer = (TimerHandle)LaunchMainArch.Interface.GetSystem<TimerSystem>().CreateTimer(time, StopShake, false);
            StartSampling(_perlin.m_NoiseProfile).Forget();
        }

        private async UniTask StartSampling(NoiseSettings profile)
        {
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource = new CancellationTokenSource();
            await SampleCurve(profile, _cancellationTokenSource.Token);
        }

        private async UniTask SampleCurve(NoiseSettings profile, CancellationToken ct)
        {
            if (profile == null || profile.AmpCure == null || profile.AmpCure.keys.Length == 0)
            {
                Debug.LogError("Noise profile or ampCure curve is not assigned!");
                return;
            }

            float duration = profile.AmpCure.keys[^1].time; 

            float elapsedTime = 0f;
            while (!ct.IsCancellationRequested)
            {
                if (elapsedTime >= duration || CheckStop())
                    break;

                try
                {
                    // 计算当前时间在曲线上的位置
                    float t = elapsedTime;
                    float curveValue = profile.AmpCure.Evaluate(t); // 获取曲线值
                    profile.CurrentCurveValue = curveValue;

                    // 等待下一帧
                    await UniTask.Yield(cancellationToken: ct);
                    elapsedTime += Time.deltaTime; // 增加经过的时间
                }
                catch (Exception e)
                {
                    break;
                }
            }

            profile.CurrentCurveValue = 0;
        }
        
        
        
        /// <summary>
        ///  留个口子，将来可以添加一些限定条件；
        /// </summary>
        /// <returns></returns>
        private  bool CheckStop()
        {
            return false; 
        }
        
        public void StopShake()
        {
            LogKit.I("[SpectatorCamera] StopShake called");
            
            _cancellationTokenSource?.Cancel();
            
            // 重置震动参数
            if (_perlin != null)
            {
                _perlin.m_AmplitudeGain = 0f;
                _perlin.m_FrequencyGain = 0f;
                _perlin.m_NoiseProfile = _noiseSettings; // 恢复原始噪声设置
            }
            
            // 降低相机优先级或禁用
            if (_camera != null)
            {
                _camera.Priority = 10; // 恢复默认优先级
                LogKit.I($"[SpectatorCamera] Camera priority reset to: {_camera.Priority}");
            }
            
            // 可选：禁用GameObject
            gameObject.SetActive(false);
        }
    }
}
