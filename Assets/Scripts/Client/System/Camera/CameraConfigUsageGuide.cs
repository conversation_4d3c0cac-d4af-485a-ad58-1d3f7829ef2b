// using UnityEngine;
// using QFramework;
//
// namespace Client.System.Camera
// {
//     /// <summary>
//     /// CameraConfigController 使用指南和示例
//     /// </summary>
//     public class CameraConfigUsageGuide : MonoBehaviour
//     {
//         [Header("使用指南")]
//         [TextArea(10, 20)]
//         public string usageInstructions = @"
// CameraConfigController 使用指南：
//
// === 1. 创建配置文件 ===
// • 在Project窗口右键 → Create → CinemaChine → Camera Config Controller
// • 重命名为合适的名称（如：MainCameraConfig）
//
// === 2. 配置震动效果 ===
// • 打开创建的配置文件
// • 点击 '添加新配置项' 按钮
// • 设置事件名称（如：HitShake, ExplosionShake）
// • 分配 NoiseSettings 资源
// • 选择震动形状类型
// • 配置手柄震动参数
//
// === 3. 在代码中使用 ===
// // 获取相机系统
// var cameraSystem = this.GetSystem<CameraSystem>();
//
// // 触发震动
// cameraSystem.TriggerConfigShake('HitShake', 1.5f);
//
// // 在指定位置触发震动
// cameraSystem.TriggerConfigShakeAtPosition('ExplosionShake', explosionPos, 2f);
//
// === 4. 预览功能 ===
// • 运行游戏后在Inspector中点击'预览'按钮测试效果
// • 调整预览强度倍数
//
// === 5. 资源配置 ===
// • 将配置文件添加到YooAsset标签 'CameraConfigController'
// • NoiseSettings添加到标签 'CameraConfig'
//         ";
//
//         [Header("代码示例")]
//         [SerializeField] private CameraConfigController exampleConfig;
//         [SerializeField] private string[] testEventNames = { "HitShake", "ExplosionShake", "JumpShake" };
//
//         private CameraSystem cameraSystem;
//
//         void Start()
//         {
//             // 获取相机系统
//             cameraSystem = this.GetSystem<CameraSystem>();
//             
//             if (cameraSystem == null)
//             {
//                 Debug.LogError("CameraSystem not found! Make sure it's properly initialized.");
//                 return;
//             }
//
//             // 打印可用的配置事件
//             PrintAvailableEvents();
//         }
//
//         void Update()
//         {
//             // 示例：按键触发不同震动效果
//             if (Input.GetKeyDown(KeyCode.Alpha1))
//             {
//                 TriggerShakeExample("HitShake", 1f);
//             }
//             else if (Input.GetKeyDown(KeyCode.Alpha2))
//             {
//                 TriggerShakeExample("ExplosionShake", 2f);
//             }
//             else if (Input.GetKeyDown(KeyCode.Alpha3))
//             {
//                 TriggerShakeExample("JumpShake", 0.8f);
//             }
//         }
//
//         /// <summary>
//         /// 触发震动示例
//         /// </summary>
//         public void TriggerShakeExample(string eventName, float intensity = 1f)
//         {
//             if (cameraSystem == null)
//             {
//                 Debug.LogWarning("CameraSystem is null!");
//                 return;
//             }
//
//             if (cameraSystem.HasConfigItem(eventName))
//             {
//                 cameraSystem.TriggerConfigShake(eventName, intensity);
//                 Debug.Log($"触发震动: {eventName} (强度: {intensity})");
//             }
//             else
//             {
//                 Debug.LogWarning($"找不到震动配置: {eventName}");
//             }
//         }
//
//         /// <summary>
//         /// 在指定位置触发震动
//         /// </summary>
//         public void TriggerShakeAtPosition(string eventName, Vector3 position, float intensity = 1f)
//         {
//             if (cameraSystem != null && cameraSystem.HasConfigItem(eventName))
//             {
//                 cameraSystem.TriggerConfigShakeAtPosition(eventName, position, intensity);
//                 Debug.Log($"在位置 {position} 触发震动: {eventName}");
//             }
//         }
//
//         /// <summary>
//         /// 打印所有可用的配置事件
//         /// </summary>
//         public void PrintAvailableEvents()
//         {
//             if (cameraSystem == null) return;
//
//             var events = cameraSystem.GetAvailableConfigEvents();
//             Debug.Log($"可用的震动配置事件 ({events.Count}个):");
//             
//             foreach (var eventName in events)
//             {
//                 var configItem = cameraSystem.GetConfigItem(eventName);
//                 if (configItem != null)
//                 {
//                     Debug.Log($"  - {eventName}: {configItem.GetDisplayName()}");
//                 }
//             }
//         }
//
//         /// <summary>
//         /// 批量触发震动测试
//         /// </summary>
//         [ContextMenu("测试所有震动效果")]
//         public void TestAllShakeEffects()
//         {
//             if (cameraSystem == null) return;
//
//             var events = cameraSystem.GetAvailableConfigEvents();
//             
//             foreach (var eventName in events)
//             {
//                 // 延迟触发，避免同时触发多个震动
//                 StartCoroutine(DelayedShakeTest(eventName, events.IndexOf(eventName) * 1f));
//             }
//         }
//
//         private System.Collections.IEnumerator DelayedShakeTest(string eventName, float delay)
//         {
//             yield return new WaitForSeconds(delay);
//             TriggerShakeExample(eventName, 1f);
//         }
//
//         /// <summary>
//         /// 动态添加配置项示例
//         /// </summary>
//         public void AddConfigItemExample()
//         {
//             if (exampleConfig == null)
//             {
//                 Debug.LogWarning("请先分配 exampleConfig!");
//                 return;
//             }
//
//             // 添加新的配置项
//             exampleConfig.AddConfigItem("NewShakeEvent");
//             
//             // 获取刚添加的配置项并设置参数
//             var newItem = exampleConfig.GetConfigItem("NewShakeEvent");
//             if (newItem != null)
//             {
//                 newItem.description = "动态添加的震动效果";
//                 newItem.hapticConfig.duration = 0.3f;
//                 newItem.hapticConfig.leftMotorIntensity = 0.7f;
//                 newItem.hapticConfig.rightMotorIntensity = 0.5f;
//                 
//                 Debug.Log("成功添加新的配置项: NewShakeEvent");
//             }
//         }
//
//         /// <summary>
//         /// 重新加载配置
//         /// </summary>
//         public void ReloadConfig()
//         {
//             if (cameraSystem != null)
//             {
//                 cameraSystem.ReloadConfig();
//                 Debug.Log("配置已重新加载");
//             }
//         }
//
//         void OnGUI()
//         {
//             if (cameraSystem == null) return;
//
//             GUILayout.BeginArea(new Rect(10, 10, 400, 300));
//             GUILayout.Label("=== CameraConfigController 使用示例 ===", GUI.skin.box);
//             
//             GUILayout.Space(10);
//             
//             // 显示可用事件
//             var events = cameraSystem.GetAvailableConfigEvents();
//             GUILayout.Label($"可用配置事件: {events.Count}个");
//             
//             GUILayout.Space(10);
//             
//             // 快速测试按钮
//             if (GUILayout.Button("测试 HitShake (按键1)"))
//             {
//                 TriggerShakeExample("HitShake", 1f);
//             }
//             
//             if (GUILayout.Button("测试 ExplosionShake (按键2)"))
//             {
//                 TriggerShakeExample("ExplosionShake", 2f);
//             }
//             
//             if (GUILayout.Button("测试 JumpShake (按键3)"))
//             {
//                 TriggerShakeExample("JumpShake", 0.8f);
//             }
//             
//             GUILayout.Space(10);
//             
//             if (GUILayout.Button("测试所有震动效果"))
//             {
//                 TestAllShakeEffects();
//             }
//             
//             if (GUILayout.Button("重新加载配置"))
//             {
//                 ReloadConfig();
//             }
//             
//             if (GUILayout.Button("打印可用事件"))
//             {
//                 PrintAvailableEvents();
//             }
//             
//             GUILayout.EndArea();
//         }
//     }
// }
