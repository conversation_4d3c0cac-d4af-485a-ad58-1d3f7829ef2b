using Client.Event.GameSettingEvent;
using GameClient.System;
using QFramework;
using UnityEngine;
using Screen = UnityEngine.Device.Screen;

namespace Client.System.SettingSystem
{
    public class ScreenControllerSystem : AbstractSystem
    {
        private const string UNLIMITED_FRAMERATE = "Unlimited";
        private ClientSettingsModel _settingsModel;
        private string _currentMaxFrameRate = UNLIMITED_FRAMERATE;

        protected override void OnInit()
        {
            // 获取设置模型
            _settingsModel = this.GetModel<ClientSettingsModel>();
            this.RegisterEvent<GameSettingsLoadCompletedEvent>(InitializeFrameRate); 
            // 初始化帧率设置, 应等设置模块结束后  
            // InitializeFrameRate();
        } 

        protected override void OnDeinit()
        {
        }

        public void InitializeFrameRate(GameSettingsLoadCompletedEvent evt)
        {
            // 从配置中读取最高帧率限制
            string maxFrameRate = GetMaxFrameRateFromConfig();

            // 应用帧率设置
            ApplyFrameRateLimit(maxFrameRate);

            LogKit.I(
                $"[FrameController] 帧率初始化完成，当前限制: {(maxFrameRate == UNLIMITED_FRAMERATE ? "无限制" : maxFrameRate?.ToString())}");
        }

        private string GetMaxFrameRateFromConfig()
        {
            var settingData = _settingsModel.Data;
            return settingData.MaximumFrameRate;
        }

        private void ApplyFrameRateLimit(string maxFrameRate)
        {
            _currentMaxFrameRate = maxFrameRate;

            var frame = -1;
            int.TryParse(maxFrameRate, out frame); 
            if (frame <= 0)
            {
                // 无限制帧率
                Application.targetFrameRate = frame;
            }
            else
            {
                // 设置目标帧率并启动强制锁定监控
                Application.targetFrameRate = frame;
                LogKit.I($"[FrameController] 帧率已强制锁定为: {maxFrameRate} FPS");
            }
        }


        #region 公共接口

        /// <summary>
        /// 手动设置帧率限制
        /// </summary>
        /// <param name="maxFrameRate">最大帧率，-1为无限制</param>
        public void SetFrameRateLimit(string maxFrameRate)
        {
            ApplyFrameRateLimit(maxFrameRate);
        }

        /// <summary>
        /// 获取当前帧率限制
        /// </summary>
        /// <returns>当前帧率限制，-1为无限制</returns>
        public string GetCurrentFrameRateLimit()
        {
            return _currentMaxFrameRate;
        }

        /// <summary>
        /// 是否启用了帧率限制
        /// </summary>
        /// <returns>true表示有帧率限制</returns>
        public bool IsFrameRateLimited()
        {
            return _currentMaxFrameRate == UNLIMITED_FRAMERATE;  
        }

        /// <summary>
        /// 切换垂直同步（独立于帧率设置）
        /// </summary>
        /// <param name="enable">是否启用垂直同步</param>
        public void SetVSync(bool enable)
        {
            QualitySettings.vSyncCount = enable ? 1 : 0;
            LogKit.I($"[FrameController] 垂直同步: {(enable ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 获取垂直同步状态
        /// </summary>
        /// <returns>true表示垂直同步开启</returns>
        public bool IsVSyncEnabled()
        {
            return QualitySettings.vSyncCount > 0;
        }

        #endregion

        #region 调试信息

        /// <summary>
        /// 获取当前帧率信息（调试用）
        /// </summary>
        /// <returns>帧率信息字符串</returns>
        public string GetFrameRateInfo()
        {
            return $"目标帧率: {(Application.targetFrameRate == -1 ? "无限制" : Application.targetFrameRate.ToString())}, " +
                   $"垂直同步: {(QualitySettings.vSyncCount > 0 ? "开启" : "关闭")}, " +
                   $"当前限制: {(_currentMaxFrameRate == UNLIMITED_FRAMERATE ? "无限制" : _currentMaxFrameRate.ToString())}";
        }

        #endregion

        public void SetScreenMode(ScreenModeType type)
        {
            switch (type)
            {
                case ScreenModeType.FullScreen:
                {
                    Screen.fullScreenMode = FullScreenMode.ExclusiveFullScreen;
                    break;
                }
                case ScreenModeType.Windowed:
                {
                    Screen.fullScreenMode = FullScreenMode.Windowed;
                    break;
                }
                case ScreenModeType.BorderlessWindowed:
                {
                    Screen.fullScreenMode = FullScreenMode.FullScreenWindow;
                    break;
                }
            }
        }

        /// <summary>
        ///  设置分辨率 
        /// </summary>
        /// <param name="resolution"></param>
        public void SetDisplayResolution(string resolution)
        {
            var w_h = resolution.Split('*');
            Screen.SetResolution(int.Parse(w_h[0]), int.Parse(w_h[1]), false);
        }

        /// <summary>
        ///  设置屏幕亮度 
        /// </summary>
        /// <param name="value"></param>
        public void SetScreenBrightness(int value)
        {
            Screen.brightness = value;
        }
    }
}
