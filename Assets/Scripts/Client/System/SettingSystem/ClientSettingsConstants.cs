using System.Xml.Serialization.Configuration;

namespace GameClient.System.ConfigSystem
{
    public static class ClientSettingsConstants
    {
        // Language 
        public const string TEXT_LANGUAGE = "TextLanguage";
        public const string TEXT_SIZE = "TextSize";

        //Settings 
        public const string FRAME_RATE_DISPLAY = "FrameRateDisplay";
        public const string SCREEN_BRIGHTNESS = "ScreenBrightness";  
        
        // Music 
        public const string MUSIC_VOLUME = "MusicVolume";
        public const string VOICE_VOLUME = "VoiceVolume";
        public const string MASTER_VOLUME = "MasterVolume"; 
        public const string UI_VOLUME = "UserInterfaceVolume";
        
        // Graphics
        public const string QUICK_PRESET = "QuickPreset";
        public const string VSYNC = "Vsync"; 
        public const string DISPLAY_RESOLUTION = "DisplayResolution"; 
        public const string GRAPHICS_QUALITY = "GraphicsQuality";
        public const string MAXIMUM_FRAME_RATE = "MaximumFrameRate"; 
        public const string SCREEN_MODE = "ScreenMode";  
        
        //Generals 
        public const string MOTION_BLUR = "MotionBlur";  
        public const string VIBRATORY =  "Vibratory"; 
        public const string VIBRATORY_SETTINGS = "VibratorySettings";
        
        //Calibration 
        public static string HEIGHT_CALIBRATION = "HeightCalibration";
    }
}
