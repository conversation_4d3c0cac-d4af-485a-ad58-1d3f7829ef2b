using UnityEngine;
using QFramework;
using Client.Event.InputEvents;
using GameClient;
using GameClient.System;
using GameClient.Model;

namespace Client.System
{
    /// <summary>
    /// 战斗调试系统
    /// 监听输入动作事件，转换为技能触发和停止事件
    /// </summary>
    public class CombatDebugSystem : AbstractSystem, ICanSendEvent, ICanGetSystem
    {
        protected override void OnInit()
        {
            // 注册输入事件监听
            this.RegisterEvent<InputActionStartedEvent>(OnInputActionStarted);
            this.RegisterEvent<InputActionPerformedEvent>(OnInputActionPerformed);
            this.RegisterEvent<InputActionCancelledEvent>(OnInputActionCancelled);

            Debug.Log("[CombatDebugSystem] 战斗调试系统初始化完成");
        }

        protected override void OnDeinit()
        {
            // 取消事件注册
            this.UnRegisterEvent<InputActionStartedEvent>(OnInputActionStarted);
            this.UnRegisterEvent<InputActionPerformedEvent>(OnInputActionPerformed);
            this.UnRegisterEvent<InputActionCancelledEvent>(OnInputActionCancelled);

            base.OnDeinit();
        }

        /// <summary>
        /// 处理输入动作开始事件
        /// </summary>
        private void OnInputActionStarted(InputActionStartedEvent e)
        {
            TryTriggerSkills(e);
            TryGuidance(e);
        }

        private void TryTriggerSkills(InputActionStartedEvent e)
        {
            // 过滤掉非技能相关的输入
            var skill = GetSkill(e.ActionName);
            if (skill.IsNullOrEmpty())
                return;

            string playerId = this.GetModel<LocalPlayerModel>().SelfPlayerID;
            if (string.IsNullOrEmpty(playerId))
            {
                Debug.LogWarning("[CombatDebugSystem] 无法获取当前玩家ID，跳过技能触发");
                return;
            }

            // 获取位置信息
            var positionInfo = GetCameraPositionInfo();

            // 发送技能触发事件
            var skillTriggerEvent = new SkillTriggerEvent(
                playerId,
                skill,
                positionInfo.startPos,
                positionInfo.startDir,
                positionInfo.targetPos
            );

            this.SendEvent(skillTriggerEvent);

            Debug.Log($"[CombatDebugSystem] 触发技能: {skill}, 玩家: {playerId}");
        }

        private void TryGuidance(InputActionStartedEvent e)
        {
            if (e.ActionName == "StartGuidance")
            {
                Debug.Log("[CombatDebugSystem] 引导开始");
                this.SendEvent(new OnPoseRecognizeUpdate { PoseName = "FireHeroSkill1_Pose", Similarity = 0.99f });
            }
            else if (e.ActionName == "EndGuidance")
            {
                Debug.Log("[CombatDebugSystem] 引导结束");
                this.SendEvent(new OnPoseRecognizeUpdate { PoseName = "FireHeroSkill1_Pose", Similarity = 0.01f });
            }
        }

        /// <summary>
        /// 处理输入动作执行事件
        /// </summary>
        private void OnInputActionPerformed(InputActionPerformedEvent e)
        {
            // 目前不做特殊处理，Started事件已经处理了技能触发
            Debug.Log($"[CombatDebugSystem] 输入动作执行: {e.ActionName}");
        }

        /// <summary>
        /// 处理输入动作取消事件
        /// </summary>
        private void OnInputActionCancelled(InputActionCancelledEvent e)
        {
            // 过滤掉非技能相关的输入
            var skill = GetSkill(e.ActionName);
            if (skill.IsNullOrEmpty())
                return;

            string playerId = this.GetModel<LocalPlayerModel>().SelfPlayerID;
            if (string.IsNullOrEmpty(playerId))
            {
                Debug.LogWarning("[CombatDebugSystem] 无法获取当前玩家ID，跳过技能停止");
                return;
            }

            // 发送技能停止事件
            var skillStopEvent = new SkillStopEvent(playerId, skill);
            this.SendEvent(skillStopEvent);

            Debug.Log($"[CombatDebugSystem] 停止技能: {skill}, 玩家: {playerId}");
        }

        /// <summary>
        /// 判断是否为技能相关的动作
        /// </summary>
        private string GetSkill(string actionName)
        {
            if (string.IsNullOrEmpty(actionName))
                return null;
            if (actionName.Equals("Burst"))
                return "BurstSkill";
            return actionName;
        }

        /// <summary>
        /// 获取当前玩家ID
        /// </summary>
        private string GetCurrentPlayerId()
        {
            return this.GetModel<LocalPlayerModel>().SelfPlayerID;
        }

        /// <summary>
        /// 获取相机位置信息
        /// </summary>
        private (Vector3 startPos, Vector3 startDir, Vector3 targetPos) GetCameraPositionInfo()
        {
            return (Vector3.zero, Vector3.forward, Vector3.forward * 10f);
            UnityEngine.Camera mainCamera = UnityEngine.Camera.main; 

            if (mainCamera == null)
            {
                Debug.LogWarning("[CombatDebugSystem] 未找到主相机，使用默认位置");
                return (Vector3.zero, Vector3.forward, Vector3.forward * 10f);
            }

            Transform cameraTransform = mainCamera.transform;
            Vector3 startPos = cameraTransform.position;
            Vector3 startDir = cameraTransform.forward;
            Vector3 targetPos = startPos + startDir * 10f; // 默认目标点在前方10米

            return (startPos, startDir, targetPos);
        }
    }
}
