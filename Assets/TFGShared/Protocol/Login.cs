using System;
using LiteNetLib.Utils;
using TFGShare.Utility;
using MemoryPack;

namespace TFGShare.Protocol
{
    // 客户端登录请求
    [PacketType(TFGProtoType.C2SLoginRequest), MemoryPackable]
    public partial class C2SLoginRequest : MemoryPackablePacket<C2SLoginRequest>
    {
        public PlayerData PlayerData { get; set; }
        public string Username { get; set; }
        public string UserId { get; set; }
    }

    // 客户端登录响应
    [PacketType(TFGProtoType.S2CLoginResult), MemoryPackable]
    public partial class S2CLoginResult : MemoryPackablePacket<S2CLoginResult>
    {
        public string UserId { get; set; }
        public DateTime ServerTime { get; set; }

        public PlayerData PlayerData { get; set; }
        
        public bool IsReconnect { get; set; }
    }

    [PacketType(TFGProtoType.S2CPlayerData), MemoryPackable]
    public partial class S2CPlayerData : MemoryPackablePacket<S2CPlayerData>
    {
        public PlayerData PlayerData { get; set; }
        public int TeamId { get; set; }
    }
}
