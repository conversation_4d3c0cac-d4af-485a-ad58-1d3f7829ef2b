using LiteNetLib.Utils;
using UnityEngine;

namespace TFGShare.Protocol
{
    //暂时先不用，用于客户端触发技能，向服务器发送技能信息
    public class TriggerSkillData
    {
        // 行为名称
        public string BehaviorName { get; set; }

        // 起始位置
        public Vector3 StartPos { get; set; }

        // 方向
        public Vector3 Direction { get; set; }

        // 目标位置，表示行为最终到达的位置
        public Vector3 TargetPos { get; set; }

        // public void Serialize(NetDataWriter writer)
        // {
        //     writer.Put(BehaviorName ?? string.Empty);
        //     writer.Put(StartPos);
        //     writer.Put(Direction);
        //     writer.Put(TargetPos);
        // }
        //
        // public static TriggerSkillData Deserialize(NetDataReader reader)
        // {
        //     return new TriggerSkillData
        //     {
        //         BehaviorName = reader.GetString(),
        //         StartPos = reader.GetVector3(),
        //         Direction = reader.GetVector3(),
        //         TargetPos = reader.GetVector3()
        //     };
        // }
    }
}
