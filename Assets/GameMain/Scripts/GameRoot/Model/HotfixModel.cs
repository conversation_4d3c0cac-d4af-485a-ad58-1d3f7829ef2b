using System.Reflection;
using QFramework;
using System.Collections.Generic;

namespace GameMain
{
    /// <summary>
    /// 热更流程步骤枚举
    /// </summary>
    public enum EHotfixStep
    {
        eNone = 0,
        eError = 1,
        eCheckHotfixResFinish = 2,
        eLoadHotfixDllFinish = 3,
        eRunHotfixFinish = 4,
    }

    /// <summary>
    /// 热更模型接口
    /// </summary>
    public interface IHotfixModel : IModel
    {
        /// <summary>
        /// 游戏程序集字典
        /// </summary>
        BindableProperty<Dictionary<string, Assembly>> GameAssemblies { set; get; }
        
        /// <summary>
        /// 热更步骤状态
        /// </summary>
        BindableProperty<EHotfixStep> StepProperty { set; get; }

        /// <summary>
        /// 错误信息
        /// </summary>
        BindableProperty<string> ErrorMsg { set; get; }

        /// <summary>
        /// 进度信息
        /// </summary>
        BindableProperty<string> ProgressMsg { set; get; }

        /// <summary>
        /// 进度比率
        /// </summary>
        BindableProperty<float> ProgressRate { set; get; }
        
        /// <summary>
        /// 向后兼容属性，获取主游戏程序集
        /// </summary>
        BindableProperty<Assembly> GameAssembly { get; }
    }

    /// <summary>
    /// 热更模型实现，负责热更流程状态管理
    /// </summary>
    public partial class HotfixModel : AbstractModel, IHotfixModel
    {
        /// <summary>
        /// 游戏程序集字典
        /// </summary>
        public BindableProperty<Dictionary<string, Assembly>> GameAssemblies { set; get; }

        /// <summary>
        /// 热更步骤状态
        /// </summary>
        public BindableProperty<EHotfixStep> StepProperty { set; get; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public BindableProperty<string> ErrorMsg { set; get; }

        /// <summary>
        /// 进度信息
        /// </summary>
        public BindableProperty<string> ProgressMsg { set; get; }

        /// <summary>
        /// 进度比率
        /// </summary>
        public BindableProperty<float> ProgressRate { set; get; }
        
        /// <summary>
        /// 向后兼容的GameAssembly属性
        /// </summary>
        public BindableProperty<Assembly> GameAssembly 
        { 
            get 
            {
                var result = new BindableProperty<Assembly>();
                if (GameAssemblies.Value != null && GameAssemblies.Value.TryGetValue("GameClient.dll", out var assembly))
                {
                    result.Value = assembly;
                }
                return result;
            }
        }

        /// <summary>
        /// 初始化热更模型属性
        /// </summary>
        protected override void OnInit()
        {
            StepProperty = new()
            {
                Value = EHotfixStep.eNone
            };

            GameAssemblies = new()
            {
                Value = new Dictionary<string, Assembly>()
            };
            
            ErrorMsg = new();
            ProgressMsg = new();
            ProgressRate = new();
        }
    }
}
