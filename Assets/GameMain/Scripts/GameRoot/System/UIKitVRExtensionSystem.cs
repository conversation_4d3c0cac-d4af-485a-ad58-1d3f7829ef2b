using System;
using GameMainConfig.config;
using QFramework;
using UnityEngine;
using UIPanelPositionConfig = GameMainConfig.config.UIPanelPositionConfig;

namespace GameMain
{
    public class UIKitVRExtensionSystem : AbstractSystem
    {
        protected override void OnInit()
        {
            
        }

        public void Init()
        {
            GameMainConfigModel configModel = this.GetModel<GameMainConfigModel>();
            foreach (UIPanelPatternConfig uiPanelPatternConfig in configModel.AllTables.UIPanelPattern.DataList)
            {
                UIPanelPositionConfig uiPanelPositionConfig = configModel.AllTables.UIPanelPosition.Get(uiPanelPatternConfig.ConfigId);
                
                VRUIPanelPreset preset = new VRUIPanelPreset();
                preset.presetName = uiPanelPatternConfig.Pattern;
                
                if (Enum.IsDefined(typeof(UIPanelPositionType), uiPanelPositionConfig.PositionType))
                {
                    preset.positionType = (UIPanelPositionType)uiPanelPositionConfig.PositionType;
                }
                else
                {
                    LogKit.W("非法的 UIPanelPositionType 值: " + uiPanelPositionConfig.PositionType);
                    preset.positionType = UIPanelPositionType.FixedPosition;
                }
                preset.offset = new Vector3(uiPanelPositionConfig.Offset.X, uiPanelPositionConfig.Offset.Y, uiPanelPositionConfig.Offset.Z);
                preset.fixedPosition = new Vector3(uiPanelPositionConfig.FixedPosition.X, uiPanelPositionConfig.FixedPosition.Y, uiPanelPositionConfig.FixedPosition.Z);
                preset.fixedRotation = new Vector3(uiPanelPositionConfig.FixedRotation.X, uiPanelPositionConfig.FixedRotation.Y, uiPanelPositionConfig.FixedRotation.Z);
                
                preset.fixedPointPosition = new Vector3(uiPanelPositionConfig.FixedPointPosition.X, uiPanelPositionConfig.FixedPointPosition.Y, uiPanelPositionConfig.FixedPointPosition.Z);
                preset.smoothSpeed = uiPanelPositionConfig.SmoothSpeed;
                preset.lockToYAxisOnly = uiPanelPositionConfig.LockToYAxisOnly;
                preset.enablePositionFollow = uiPanelPositionConfig.EnablePositionFollow;
                preset.enableRotationFollow = uiPanelPositionConfig.EnableRotationFollow;
                
                preset.rotationThreshold = uiPanelPositionConfig.RotationThreshold;
                preset.fixedScale = uiPanelPositionConfig.FixedScale;
                preset.enableFixedScale = uiPanelPositionConfig.EnableFixedScale;

                preset.applicableLevels = Array.Empty<UILevel>();
                preset.panelNamePatterns = new[] { uiPanelPatternConfig.Pattern };

                if (string.Equals(uiPanelPatternConfig.Pattern, "Default", StringComparison.OrdinalIgnoreCase))
                {
                    UIKitVRExtension.Instance.SetDefaultPreset(preset);
                }
                else
                {
                    UIKitVRExtension.Instance.AddOrUpdatePreset(preset);
                }

            }
        }
    }
}
