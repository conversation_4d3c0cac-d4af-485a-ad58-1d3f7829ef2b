
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;
using Newtonsoft.Json.Linq;



namespace GameMainConfig
{

public partial struct vector4
{
    public vector4(JToken _buf) 
    {
        JObject _obj = _buf as JObject;
        X = (float)_obj.GetValue("x");
        Y = (float)_obj.GetValue("y");
        Z = (float)_obj.GetValue("z");
        W = (float)_obj.GetValue("w");
    }

    public static vector4 Deserializevector4(JToken _buf)
    {
        return new vector4(_buf);
    }

    public readonly float X;
    public readonly float Y;
    public readonly float Z;
    public readonly float W;



    public  void ResolveRef(GameMainTables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "x:" + X + ","
        + "y:" + Y + ","
        + "z:" + Z + ","
        + "w:" + W + ","
        + "}";
    }
}
}

