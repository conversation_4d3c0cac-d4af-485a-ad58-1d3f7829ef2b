using UnityEngine;
using UnityEditor;
using GameClient.Config;
using TMPro;
using SystemIO = System.IO;

namespace GameClient.Editor
{
    /// <summary>
    /// FontConfig 编辑器扩展
    /// </summary>
    [CustomEditor(typeof(FontConfig))]
    public class FontConfigEditor : UnityEditor.Editor
    {
        private SerializedProperty defaultFontProp;
        private SerializedProperty languageFontMappingsProp;
        private SerializedProperty fontResourcePathProp;
        private SerializedProperty enableFontSwitchingProp;
        private SerializedProperty enableFallbackFontsProp;
        
        void OnEnable()
        {
            defaultFontProp = serializedObject.FindProperty("defaultFont");
            languageFontMappingsProp = serializedObject.FindProperty("languageFontMappings");
            fontResourcePathProp = serializedObject.FindProperty("fontResourcePath");
            enableFontSwitchingProp = serializedObject.FindProperty("enableFontSwitching");
            enableFallbackFontsProp = serializedObject.FindProperty("enableFallbackFonts");
        }
        
        public override void OnInspectorGUI()
        {
            FontConfig config = (FontConfig)target;
            
            serializedObject.Update();
            
            EditorGUILayout.LabelField("字体配置", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 默认字体
            EditorGUILayout.PropertyField(defaultFontProp, new GUIContent("默认字体"));
            
            // 资源路径
            EditorGUILayout.PropertyField(fontResourcePathProp, new GUIContent("字体资源路径"));
            
            // 功能开关
            EditorGUILayout.PropertyField(enableFontSwitchingProp, new GUIContent("启用字体切换"));
            EditorGUILayout.PropertyField(enableFallbackFontsProp, new GUIContent("启用降级字体"));
            
            EditorGUILayout.Space();
            
            // 语言字体映射
            EditorGUILayout.PropertyField(languageFontMappingsProp, new GUIContent("映射列表"), true);
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("重置为默认配置"))
            {
                if (EditorUtility.DisplayDialog("确认", "确定要重置为默认配置吗？这将清除所有当前设置。", "确定", "取消"))
                {
                    config.ResetToDefault();
                    EditorUtility.SetDirty(config);
                }
            }
            
            if (GUILayout.Button("验证配置"))
            {
                bool isValid = config.ValidateConfig();
                string message = isValid ? "配置验证通过" : "配置验证失败，请检查控制台日志";
                EditorUtility.DisplayDialog("验证结果", message, "确定");
            }
            
            if (GUILayout.Button("自动配置"))
            {
                AutoConfigureFonts(config);
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // 信息显示
            EditorGUILayout.LabelField("配置信息", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"支持语言数: {config.languageFontMappings.Count}");
            EditorGUILayout.LabelField($"默认字体: {(config.defaultFont != null ? config.defaultFont.name : "未设置")}");
            
            serializedObject.ApplyModifiedProperties();
        }
        
        /// <summary>
        /// 自动配置字体
        /// </summary>
        /// <param name="config">字体配置</param>
        private void AutoConfigureFonts(FontConfig config)
        {
            // 尝试自动查找和配置字体
            string basePath = "Assets/Resources/" + config.fontResourcePath;
            
            // 查找默认字体
            if (config.defaultFont == null)
            {
                var defaultFont = Resources.Load<TMP_FontAsset>(config.fontResourcePath + "LiberationSans SDF");
                if (defaultFont != null)
                {
                    config.defaultFont = defaultFont;
                    Debug.Log("[FontConfigEditor] 自动设置默认字体: LiberationSans SDF");
                }
            }
            
            // 查找中文字体
            var chineseFont = Resources.Load<TMP_FontAsset>(config.fontResourcePath + "Alimama FangYuanTi VF SDF");
            if (chineseFont != null)
            {
                config.SetLanguageFont(LanguageType.Chinese, chineseFont, "中文字体");
                config.SetLanguageFont(LanguageType.Korean, chineseFont, "韩文使用中文字体");
                config.SetLanguageFont(LanguageType.Japanese, chineseFont, "日文使用中文字体");
                Debug.Log("[FontConfigEditor] 自动配置中文字体");
            }
            
            // 英文和其他语言使用默认字体
            if (config.defaultFont != null)
            {
                config.SetLanguageFont(LanguageType.English, config.defaultFont, "英文字体");
                config.SetLanguageFont(LanguageType.Spanish, config.defaultFont, "西班牙文使用英文字体");
                config.SetLanguageFont(LanguageType.French, config.defaultFont, "法文使用英文字体");
                Debug.Log("[FontConfigEditor] 自动配置英文字体");
            }
            
            EditorUtility.SetDirty(config);
            Debug.Log("[FontConfigEditor] 自动配置完成");
        }
    }
    
    /// <summary>
    /// 创建字体配置资源的菜单项
    /// </summary>
    public static class FontConfigCreator
    {
        [MenuItem("Tools/TFG/Create Font Config")]
        public static void CreateFontConfig()
        {
            string path = "Assets/Resources/Config/";
            
            // 确保目录存在
            if (!SystemIO.Directory.Exists(path))
            {
                SystemIO.Directory.CreateDirectory(path);
            }
            
            // 创建配置文件
            FontConfig config = ScriptableObject.CreateInstance<FontConfig>();
            
            // 设置默认配置
            config.defaultFont = Resources.Load<TMP_FontAsset>("Fonts & Materials/LiberationSans SDF");
            config.ResetToDefault();
            
            // 保存文件
            string assetPath = path + "FontConfig.asset";
            AssetDatabase.CreateAsset(config, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            // 选中新创建的文件
            Selection.activeObject = config;
            EditorGUIUtility.PingObject(config);
            
            Debug.Log($"[FontConfigCreator] 字体配置文件已创建: {assetPath}");
        }
    }
}
