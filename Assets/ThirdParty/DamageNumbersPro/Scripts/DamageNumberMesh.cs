using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using DamageNumbersPro.Internal;
using TMPro;

namespace DamageNumbersPro
{
    [DisallowMultipleComponent]
    public class DamageNumberMesh : DamageNumber
    {
        /* 
         * Contact me if you need any support.
         * Email: <EMAIL>
         * Discord: https://discord.com/invite/nWbRkN8Zxr
         * 
         * Check the manual for more information.
         * Manual: https://ekincantas.com/damage-numbers-pro/
         * 
         * Thank you for using my asset.
         * If you want to add your own code please use the functions below.
         * I recommend creating a duplicate of this script first and renaming it.
         * Otherwise you may loose your custom code when you update damage numbers pro.
         * 
         * Good Luck on your Project
         */

        //Custom Events:
        protected override void OnStart()
        {
            
        }
        protected override void OnStop()
        {
            
        }
        protected override void OnUpdate(float deltaTime)
        {
            
        }
        protected override void OnFade(float currentFade)
        {
            
        }
        protected override void OnTextUpdate()
        {

        }
        protected override void OnAbsorb(float number, float newSum)
        {

        }
    }
}
