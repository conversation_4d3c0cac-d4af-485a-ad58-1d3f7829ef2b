using UnityEngine;

public class WeightBlender
{
    private float targetWeight;
    private float duration;
    private float elapsed;
    private float weight;
    private bool isBlending;

    public WeightBlender(float initialWeight, float duration)
    {
        this.weight = initialWeight;
        this.duration = duration;
        this.elapsed = 0f;
        this.isBlending = true;
    }

    public void SetTargetWeight(float targetWeight)
    {
        this.targetWeight = targetWeight;
        this.elapsed = 0f;
        this.isBlending = true;
    }

    public float Update()
    {
        if (!isBlending) return weight;

        elapsed += Time.deltaTime;
        if (elapsed >= duration)
        {
            weight = targetWeight;
            isBlending = false;
            elapsed = 0f;
            return weight;
        }

        weight = Mathf.Lerp(weight, targetWeight, elapsed / duration);
        return weight;
    }

    public float GetCurrentWeight()
    {
        return weight;
    }
}

