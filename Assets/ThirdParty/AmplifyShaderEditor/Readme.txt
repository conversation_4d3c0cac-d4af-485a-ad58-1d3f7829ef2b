About

  Amplify Shader Editor (c) Amplify Creations, Lda. All rights reserved.

  Amplify Shader Editor is a node-based shader creation tool for Unity 5
	 
  Redistribution of Amplify Shader Editor is frowned upon. If you want to share the 
  software, please refer others to the official product page:

    http://amplify.pt/unity/amplify-shader-editor/
	
Description

 Amplify Shader Editor is a node-based shader creation tool inspired by industry leading
 editors. An open and tightly integrated solution, it provides a familiar and consistent
 development environment that seamlessly blends with Unity�s UI conventions and Shader use.
	
Features

  * Intuitive, familiar interface
  * Node based creation
  * Full source-code
  * Extensive node library
  * Growing sample collection
  * Open to user requests and contributions
  * Integrated Texture Array Creator tool
  * Node API
  * Custom Shader Templates
  * SRP HD and Lightweight support

Supported Platforms

  * All platforms 

Minimum Requirements

  Software

    Unity 5+

Quick Guide Amplify Shader Editor
  
  1) Open the editor canvas located in the main menu under Window > Amplify Shader Editor > Open Canvas.
  2) Create an Amplify Shader via the Menu under Assets > Create > Shaders > Amplify Surface shader.
  3) Double-click an ASE shader to open it in the ASE canvas.
  4) Create a new material and set its shader to the newly created Amplify Shader.
  5) Node and shader properties are on the left side, node list on the right.
  6) Drag out nodes from the node palette list on the right, or right-click anywhere in the canvas to open  
     a simpler searchable node list.
  7) Data flows from left to right, drag wires from node Input/Output ports to create connections.
  8) The 4 buttons on the main canvas next to the node and shader properties on the upper-left area serve as the main 
     save and update buttons. Button 1: Update and Save shader, Button 2: LIVE mode, automatically updates
     and saves the active shader, Button 3: Remove disconnected nodes, Button 4: Open shader in text editor.
  9) Quick access to current shader or material in use by hitting bottom left and or right buttons on main canvas
  10) Consult the Manual below for detailed information.
  
Quick Guide Texture Array Creator
  
  1) Open the tool in the main menu under Window > Amplify Shader Editor > Texture Array Creator
  2) Specify the desired Width and Height for the Texture Array through the X and Y Size
  3) Configure your texture properties
  4) Set your array Name and Path
  5) Add elements to the array by hitting the (+) button over the Texture List area and assign textures to them
  6) Hit the Build Array button on the top of the window to build your new Texture Array
  7) Please notice that you can't load previously created texture arrays into the tool so maintain it open until all the tweaks on the array are done

Documentation

  Please refer to the following website for an up-to-date online manual:

    http://amplify.pt/unity/amplify-shader-editor/manual
	
Nodes

  Please refer to the following website for a searchable node list:

    http://amplify.pt/unity/amplify-shader-editor/nodes
	
	
Feedback

  To file error reports, questions or suggestions, you may use 
  our feedback form online:
	
    http://amplify.pt/contact

  Or contact us directly:

    For general inquiries - <EMAIL>
    For technical support - <EMAIL> (customers only)
