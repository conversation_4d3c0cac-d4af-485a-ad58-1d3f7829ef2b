using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;

using System.Text;

#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;

namespace QFramework.PrefsKit
{
    /// <summary>
    /// 表示可以被序列化和反序列化的Prefs项
    /// 实现此接口的类必须提供无参构造函数
    /// </summary>
    public interface IPrefsItem
    {
        /// <summary>
        /// 将对象序列化为字符串
        /// </summary>
        /// <returns>序列化后的字符串，如果序列化失败则返回null或空字符串</returns>
        string Serialize();

        /// <summary>
        /// 从字符串反序列化对象
        /// </summary>
        /// <param name="value">要反序列化的字符串</param>
        /// <exception cref="ArgumentException">当输入的字符串格式无效时抛出</exception>
        void Deserialize(string value);
    }

    /// <summary>
    /// PrefsItem工厂类，负责创建和管理IPrefsItem实例
    /// 使用反射自动注册所有实现了IPrefsItem接口的类
    /// </summary>
    public static class PrefsItemFactory
    {
        // 存储类型到其创建器的映射
        private static readonly ConcurrentDictionary<Type, Func<IPrefsItem>> _prefsItemCreatorMap = new();
        // 存储已验证的类型集合
        private static readonly HashSet<Type> _validatedTypes = new();

        static PrefsItemFactory()
        {
            try
            {
                InitializeFactory();
            }
            catch (Exception e)
            {
                LogKit.E(e, "[PrefsItemFactory] 初始化工厂时发生错误");
            }
        }

        /// <summary>
        /// 初始化工厂，扫描所有程序集查找IPrefsItem实现
        /// </summary>
        private static void InitializeFactory()
        {
            var ipType = typeof(IPrefsItem);
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();

            foreach (var assembly in assemblies)
            {
                try
                {
                    var packetTypes = assembly.GetTypes()
                        .Where(t => t.IsClass && !t.IsAbstract && ipType.IsAssignableFrom(t));

                    foreach (var type in packetTypes)
                    {
                        RegisterType(type);
                    }
                }
                catch (ReflectionTypeLoadException e)
                {
                    LogKit.W($"[PrefsItemFactory] 加载程序集 {assembly.FullName} 的类型时出现警告: {e.Message}");
                    if (e.LoaderExceptions != null)
                    {
                        foreach (var loaderException in e.LoaderExceptions)
                        {
                            if (loaderException != null)
                            {
                                LogKit.W($"[PrefsItemFactory] 加载器异常: {loaderException.Message}");
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 注册类型到工厂
        /// </summary>
        /// <param name="type">要注册的类型</param>
        private static void RegisterType(Type type)
        {
            try
            {
                // 验证类型是否有无参构造函数
                var constructor = type.GetConstructor(Type.EmptyTypes);
                if (constructor == null)
                {
                    LogKit.E($"[PrefsItemFactory] 类型 {type.Name} 没有无参构造函数，无法注册");
                    return;
                }

                _prefsItemCreatorMap[type] = () =>
                {
                    try
                    {
                        return (IPrefsItem)Activator.CreateInstance(type);
                    }
                    catch (Exception e)
                    {
                        LogKit.E(e, $"[PrefsItemFactory] 创建类型 {type.Name} 的实例时出错");
                        return null;
                    }
                };

                _validatedTypes.Add(type);
                LogKit.I($"[PrefsItemFactory] 成功注册类型: {type.Name}");
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsItemFactory] 注册类型 {type.Name} 时出错");
            }
        }

        /// <summary>
        /// 创建指定类型的IPrefsItem实例并反序列化数据
        /// </summary>
        /// <param name="type">要创建的类型</param>
        /// <param name="data">要反序列化的数据</param>
        /// <returns>创建的实例，如果创建失败则返回null</returns>
        public static IPrefsItem Create(Type type, string data)
        {
            if (type == null)
            {
                LogKit.E("[PrefsItemFactory] 类型参数不能为null");
                return null;
            }

            if (!_validatedTypes.Contains(type))
            {
                LogKit.E($"[PrefsItemFactory] 类型 {type.Name} 未经过验证或注册");
                return null;
            }

            try
            {
                if (_prefsItemCreatorMap.TryGetValue(type, out var creator) && creator != null)
                {
                    LogKit.I($"[PrefsItemFactory] 正在创建类型 {type.Name} 的实例");
                    var item = creator();

                    if (item == null)
                    {
                        LogKit.E($"[PrefsItemFactory] 创建类型 {type.Name} 的实例失败");
                        return null;
                    }

                    if (!string.IsNullOrEmpty(data))
                    {
                        try
                        {
                            LogKit.I($"[PrefsItemFactory] 正在反序列化类型 {type.Name} 的数据");
                            item.Deserialize(data);
                        }
                        catch (Exception e)
                        {
                            LogKit.E(e, $"[PrefsItemFactory] 反序列化类型 {type.Name} 时出错");
                            return null;
                        }
                    }

                    return item;
                }

                LogKit.E($"[PrefsItemFactory] 找不到类型 {type.Name} 的创建器");
                return null;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsItemFactory] 创建类型 {type.Name} 时发生未知错误");
                return null;
            }
        }

        /// <summary>
        /// 检查类型是否已注册
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>如果类型已注册则返回true，否则返回false</returns>
        public static bool IsTypeRegistered(Type type)
        {
            return type != null && _validatedTypes.Contains(type);
        }
    }

    /// <summary>
    /// 存储接口，定义了基本的数据存储操作
    /// 实现此接口的类需要处理所有可能的异常情况
    /// </summary>
    public interface IPrefsStorage
    {
        /// <summary>
        /// 存储整数值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="value">要存储的值</param>
        /// <exception cref="ArgumentException">当key为null或空时抛出</exception>
        void SetInt(string key, int value);

        /// <summary>
        /// 存储浮点数值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="value">要存储的值</param>
        /// <exception cref="ArgumentException">当key为null或空时抛出</exception>
        void SetFloat(string key, float value);

        /// <summary>
        /// 存储字符串值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="value">要存储的值</param>
        /// <exception cref="ArgumentException">当key为null或空时抛出</exception>
        void SetString(string key, string value);

        /// <summary>
        /// 获取整数值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>存储的值，如果不存在则返回默认值</returns>
        int GetInt(string key, int defaultValue = 0);

        /// <summary>
        /// 获取浮点数值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>存储的值，如果不存在则返回默认值</returns>
        float GetFloat(string key, float defaultValue = 0f);

        /// <summary>
        /// 获取字符串值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>存储的值，如果不存在则返回默认值</returns>
        string GetString(string key, string defaultValue = "");

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">要检查的键名</param>
        /// <returns>如果键存在则返回true，否则返回false</returns>
        bool HasKey(string key);

        /// <summary>
        /// 删除指定键的数据
        /// </summary>
        /// <param name="key">要删除的键名</param>
        void DeleteKey(string key);

        /// <summary>
        /// 删除所有数据
        /// </summary>
        void DeleteAll();

        /// <summary>
        /// 保存更改
        /// 某些存储实现（如EditorPrefs）可能不需要显式保存
        /// </summary>
        void Save();

        /// <summary>
        /// 获取存储类型名称
        /// </summary>
        /// <returns>存储类型的名称</returns>
        string GetStorageTypeName();
    }

    /// <summary>
    /// PlayerPrefs存储实现
    /// 使用Unity的PlayerPrefs进行数据持久化存储
    /// </summary>
    public class PlayerPrefsStorage : IPrefsStorage
    {
        private const string STORAGE_TYPE_NAME = "PlayerPrefs";
        private readonly HashSet<string> _modifiedKeys = new();

        public void SetInt(string key, int value)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 设置Int值 Key:{key} Value:{value}");
                PlayerPrefs.SetInt(key, value);
                _modifiedKeys.Add(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 设置Int值时出错 Key:{key}");
                throw;
            }
        }

        public void SetFloat(string key, float value)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 设置Float值 Key:{key} Value:{value}");
                PlayerPrefs.SetFloat(key, value);
                _modifiedKeys.Add(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 设置Float值时出错 Key:{key}");
                throw;
            }
        }

        public void SetString(string key, string value)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 设置String值 Key:{key} Value:{value}");
                PlayerPrefs.SetString(key, value);
                _modifiedKeys.Add(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 设置String值时出错 Key:{key}");
                throw;
            }
        }

        public int GetInt(string key, int defaultValue = 0)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                var value = PlayerPrefs.GetInt(key, defaultValue);
                LogKit.I($"[{STORAGE_TYPE_NAME}] 获取Int值 Key:{key} Value:{value}");
                return value;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 获取Int值时出错 Key:{key}");
                return defaultValue;
            }
        }

        public float GetFloat(string key, float defaultValue = 0f)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                var value = PlayerPrefs.GetFloat(key, defaultValue);
                LogKit.I($"[{STORAGE_TYPE_NAME}] 获取Float值 Key:{key} Value:{value}");
                return value;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 获取Float值时出错 Key:{key}");
                return defaultValue;
            }
        }

        public string GetString(string key, string defaultValue = "")
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                var value = PlayerPrefs.GetString(key, defaultValue);
                LogKit.I($"[{STORAGE_TYPE_NAME}] 获取String值 Key:{key} Value:{value}");
                return value;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 获取String值时出错 Key:{key}");
                return defaultValue;
            }
        }

        public bool HasKey(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                return PlayerPrefs.HasKey(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 检查Key时出错 Key:{key}");
                return false;
            }
        }

        public void DeleteKey(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 删除Key:{key}");
                PlayerPrefs.DeleteKey(key);
                _modifiedKeys.Remove(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 删除Key时出错 Key:{key}");
                throw;
            }
        }

        public void DeleteAll()
        {
            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 删除所有数据");
                PlayerPrefs.DeleteAll();
                _modifiedKeys.Clear();
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 删除所有数据时出错");
                throw;
            }
        }

        public void Save()
        {
            if (_modifiedKeys.Count == 0)
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 没有需要保存的修改");
                return;
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 保存数据 修改的键数量:{_modifiedKeys.Count}");
                PlayerPrefs.Save();
                _modifiedKeys.Clear();
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 保存数据时出错");
                throw;
            }
        }

        public string GetStorageTypeName() => STORAGE_TYPE_NAME;
    }

#if UNITY_EDITOR
    /// <summary>
    /// EditorPrefs存储实现
    /// 使用Unity的EditorPrefs进行编辑器数据持久化存储
    /// </summary>
    public class EditorPrefsStorage : IPrefsStorage
    {
        private const string STORAGE_TYPE_NAME = "EditorPrefs";
        private readonly HashSet<string> _modifiedKeys = new();

        public void SetInt(string key, int value)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 设置Int值 Key:{key} Value:{value}");
                EditorPrefs.SetInt(key, value);
                _modifiedKeys.Add(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 设置Int值时出错 Key:{key}");
                throw;
            }
        }

        public void SetFloat(string key, float value)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 设置Float值 Key:{key} Value:{value}");
                EditorPrefs.SetFloat(key, value);
                _modifiedKeys.Add(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 设置Float值时出错 Key:{key}");
                throw;
            }
        }

        public void SetString(string key, string value)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 设置String值 Key:{key} Value:{value}");
                EditorPrefs.SetString(key, value);
                _modifiedKeys.Add(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 设置String值时出错 Key:{key}");
                throw;
            }
        }

        public int GetInt(string key, int defaultValue = 0)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                var value = EditorPrefs.GetInt(key, defaultValue);
                LogKit.I($"[{STORAGE_TYPE_NAME}] 获取Int值 Key:{key} Value:{value}");
                return value;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 获取Int值时出错 Key:{key}");
                return defaultValue;
            }
        }

        public float GetFloat(string key, float defaultValue = 0f)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                var value = EditorPrefs.GetFloat(key, defaultValue);
                LogKit.I($"[{STORAGE_TYPE_NAME}] 获取Float值 Key:{key} Value:{value}");
                return value;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 获取Float值时出错 Key:{key}");
                return defaultValue;
            }
        }

        public string GetString(string key, string defaultValue = "")
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                var value = EditorPrefs.GetString(key, defaultValue);
                LogKit.I($"[{STORAGE_TYPE_NAME}] 获取String值 Key:{key} Value:{value}");
                return value;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 获取String值时出错 Key:{key}");
                return defaultValue;
            }
        }

        public bool HasKey(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                return EditorPrefs.HasKey(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 检查Key时出错 Key:{key}");
                return false;
            }
        }

        public void DeleteKey(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                throw new ArgumentException($"[{STORAGE_TYPE_NAME}] Key不能为空");
            }

            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 删除Key:{key}");
                EditorPrefs.DeleteKey(key);
                _modifiedKeys.Remove(key);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 删除Key时出错 Key:{key}");
                throw;
            }
        }

        public void DeleteAll()
        {
            try
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] 删除所有数据");
                EditorPrefs.DeleteAll();
                _modifiedKeys.Clear();
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[{STORAGE_TYPE_NAME}] 删除所有数据时出错");
                throw;
            }
        }

        public void Save()
        {
            if (_modifiedKeys.Count > 0)
            {
                LogKit.I($"[{STORAGE_TYPE_NAME}] EditorPrefs自动保存，无需手动调用 修改的键数量:{_modifiedKeys.Count}");
                _modifiedKeys.Clear();
            }
        }

        public string GetStorageTypeName() => STORAGE_TYPE_NAME;
    }
#endif

    /// <summary>
    /// PrefsKit接口，定义了Prefs系统的核心功能
    /// 提供了类型安全的数据存储和检索操作
    /// </summary>
    public interface IPrefsKit
    {
        /// <summary>
        /// 设置加密盐值
        /// 用于增强存储键的安全性
        /// </summary>
        /// <param name="salt">盐值字符串</param>
        /// <exception cref="ArgumentException">当salt为null或空时抛出</exception>
        void SetSalt(string salt);

        /// <summary>
        /// 获取指定类型的值
        /// </summary>
        /// <typeparam name="T">值的类型</typeparam>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>存储的值，如果不存在则返回默认值</returns>
        T Get<T>(string key, T defaultValue);

        /// <summary>
        /// 设置指定类型的值
        /// </summary>
        /// <typeparam name="T">值的类型</typeparam>
        /// <param name="key">键名</param>
        /// <param name="value">要存储的值</param>
        void Set<T>(string key, T value);

        /// <summary>
        /// 删除指定类型的键
        /// </summary>
        /// <typeparam name="T">值的类型</typeparam>
        /// <param name="key">要删除的键名</param>
        void DeleteKey<T>(string key);

        /// <summary>
        /// 删除所有数据
        /// </summary>
        void DeleteAll();

        /// <summary>
        /// 保存更改
        /// </summary>
        void Save();

        /// <summary>
        /// 获取存储实现类型
        /// </summary>
        /// <returns>存储实现的类型名称</returns>
        string GetStorageType();
    }

    /// <summary>
    /// PrefsKit的具体实现
    /// </summary>
    public class PrefsKitImpl : IPrefsKit
    {
        private readonly IPrefsStorage _storage;
        private string _salt = string.Empty;

        public PrefsKitImpl(bool editorMode)
        {
            try
            {
                LogKit.I($"[PrefsKitImpl] 初始化存储模式: {(editorMode ? "Editor" : "Runtime")}");
#if UNITY_EDITOR
                _storage = editorMode ? new EditorPrefsStorage() : new PlayerPrefsStorage();
#else
                if (editorMode)
                {
                    throw new ArgumentException("[PrefsKitImpl] 运行时不能使用editor存储");
                }
                _storage = new PlayerPrefsStorage();
#endif
            }
            catch (Exception e)
            {
                LogKit.E(e, "[PrefsKitImpl] 初始化失败");
                throw;
            }
        }

        public void SetSalt(string salt)
        {
            if (string.IsNullOrEmpty(salt))
            {
                throw new ArgumentException("[PrefsKitImpl] Salt值不能为空");
            }
            _salt = salt;
            LogKit.I($"[PrefsKitImpl] 设置Salt: {salt}");
        }

        private string _saltMd5 = "";
        private string GetSaltMD5Value()
        {
            if (!string.IsNullOrEmpty(_saltMd5))
            {
                return _saltMd5;
            }
            using var md5 = new MD5CryptoServiceProvider();
            var mdStr = new StringBuilder();

            var retVal = md5.ComputeHash(Encoding.UTF8.GetBytes(_salt + Application.dataPath));
            for (int i = 0; i < retVal.Length; i++)
            {
                mdStr.Append(retVal[i].ToString("x2"));
            }
            _saltMd5 = mdStr.ToString();
            return _saltMd5;
        }

        private string GetSaltedKey<T>(string key)
        {
            if (string.IsNullOrEmpty(_salt))
            {
                LogKit.W("[PrefsKitImpl] 未设置Salt，使用原始key");
                return $"{typeof(T).FullName}_{key}";
            }

            return $"{key}_{GetSaltMD5Value()}";
        }

        private T GetPrefsItemValue<T>(string saltedKey, string key, T defaultValue)
        {
            var storedValue = _storage.GetString(saltedKey, null);
            if (string.IsNullOrEmpty(storedValue))
            {
                LogKit.I($"[PrefsKitImpl] 未找到存储的值，返回默认值 Type:{typeof(T).Name} Key:{key}");
                return defaultValue;
            }

            var item = PrefsItemFactory.Create(typeof(T), storedValue);
            if (item == null)
            {
                LogKit.E($"[PrefsKitImpl] 创建或反序列化类型 {typeof(T).Name} 失败");
                return defaultValue;
            }

            try
            {
                return (T)item;
            }
            catch (InvalidCastException e)
            {
                LogKit.E(e, $"[PrefsKitImpl] 类型转换失败 Type:{typeof(T).Name} Key:{key}");
                return defaultValue;
            }
        }

        public T Get<T>(string key, T defaultValue)
        {
            if (string.IsNullOrEmpty(key))
            {
                LogKit.E("[PrefsKitImpl] Key不能为空");
                return defaultValue;
            }

            try
            {
                var saltedKey = GetSaltedKey<T>(key);
                LogKit.I($"[PrefsKitImpl] 获取值 Type:{typeof(T).Name} Key:{key} SaltedKey:{saltedKey}");

                if (typeof(T).IsPrimitive || typeof(T) == typeof(string))
                {
                    return GetPrimitiveValue(saltedKey, defaultValue);
                }

                if (!typeof(IPrefsItem).IsAssignableFrom(typeof(T)))
                {
                    LogKit.E($"[PrefsKitImpl] 类型 {typeof(T).Name} 必须实现IPrefsItem接口");
                    return defaultValue;
                }

                return GetPrefsItemValue(saltedKey, key, defaultValue);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsKitImpl] 获取值时出错 Type:{typeof(T).Name} Key:{key}");
                return defaultValue;
            }
        }

        private T GetPrimitiveValue<T>(string saltedKey, T defaultValue)
        {
            try
            {
                if (typeof(T) == typeof(int))
                {
                    return (T)(object)_storage.GetInt(saltedKey, (int)(object)defaultValue);
                }
                if (typeof(T) == typeof(float))
                {
                    return (T)(object)_storage.GetFloat(saltedKey, (float)(object)defaultValue);
                }
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)_storage.GetString(saltedKey, (string)(object)defaultValue);
                }
                if (typeof(T) == typeof(bool))
                {
                    return (T)(object)(_storage.GetInt(saltedKey, ((bool)(object)defaultValue) ? 1 : 0) != 0);
                }

                LogKit.E($"[PrefsKitImpl] 不支持的基础类型: {typeof(T).Name}");
                return defaultValue;
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsKitImpl] 获取基础类型值时出错 Type:{typeof(T).Name}");
                return defaultValue;
            }
        }

        public void Set<T>(string key, T value)
        {
            if (string.IsNullOrEmpty(key))
            {
                LogKit.E("[PrefsKitImpl] Key不能为空");
                return;
            }

            try
            {
                var saltedKey = GetSaltedKey<T>(key);
                LogKit.I($"[PrefsKitImpl] 设置值 Type:{typeof(T).Name} Key:{key} SaltedKey:{saltedKey}");

                if (typeof(T).IsPrimitive || typeof(T) == typeof(string))
                {
                    SetPrimitiveValue(saltedKey, value);
                    return;
                }

                if (!typeof(IPrefsItem).IsAssignableFrom(typeof(T)))
                {
                    LogKit.E($"[PrefsKitImpl] 类型 {typeof(T).Name} 必须实现IPrefsItem接口");
                    return;
                }

                if (value == null)
                {
                    LogKit.E("[PrefsKitImpl] 值不能为null");
                    return;
                }

                var prefsItem = value as IPrefsItem;
                var serializedValue = prefsItem.Serialize();
                if (string.IsNullOrEmpty(serializedValue))
                {
                    LogKit.E($"[PrefsKitImpl] 序列化失败 Type:{typeof(T).Name} Key:{key}");
                    return;
                }

                _storage.SetString(saltedKey, serializedValue);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsKitImpl] 设置值时出错 Type:{typeof(T).Name} Key:{key}");
            }
        }

        private void SetPrimitiveValue<T>(string saltedKey, T value)
        {
            try
            {
                if (typeof(T) == typeof(int))
                {
                    _storage.SetInt(saltedKey, (int)(object)value);
                }
                else if (typeof(T) == typeof(float))
                {
                    _storage.SetFloat(saltedKey, (float)(object)value);
                }
                else if (typeof(T) == typeof(string))
                {
                    _storage.SetString(saltedKey, (string)(object)value);
                }
                else if (typeof(T) == typeof(bool))
                {
                    _storage.SetInt(saltedKey, (bool)(object)value ? 1 : 0);
                }
                else
                {
                    LogKit.E($"[PrefsKitImpl] 不支持的基础类型: {typeof(T).Name}");
                }
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsKitImpl] 设置基础类型值时出错 Type:{typeof(T).Name}");
            }
        }

        public void DeleteKey<T>(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                LogKit.E("[PrefsKitImpl] Key不能为空");
                return;
            }

            try
            {
                var saltedKey = GetSaltedKey<T>(key);
                LogKit.I($"[PrefsKitImpl] 删除键 Type:{typeof(T).Name} Key:{key} SaltedKey:{saltedKey}");
                _storage.DeleteKey(saltedKey);
            }
            catch (Exception e)
            {
                LogKit.E(e, $"[PrefsKitImpl] 删除键时出错 Type:{typeof(T).Name} Key:{key}");
            }
        }

        public void DeleteAll()
        {
            try
            {
                LogKit.I("[PrefsKitImpl] 删除所有数据");
                _storage.DeleteAll();
            }
            catch (Exception e)
            {
                LogKit.E(e, "[PrefsKitImpl] 删除所有数据时出错");
            }
        }

        public void Save()
        {
            try
            {
                LogKit.I("[PrefsKitImpl] 保存数据");
                _storage.Save();
            }
            catch (Exception e)
            {
                LogKit.E(e, "[PrefsKitImpl] 保存数据时出错");
            }
        }

        public string GetStorageType() => _storage.GetStorageTypeName();
    }

    /// <summary>
    /// PrefsKit工具类
    /// 统一的Prefs存储工具，自动根据运行环境选择合适的存储实现
    /// </summary>
    public static class PrefsKitUtil
    {
        private static readonly Lazy<IPrefsKit> LazyPrefsUtil = new(() =>
        {
#if UNITY_EDITOR
            return new PrefsKitImpl(!Application.isPlaying);
#else
            return new PrefsKitImpl(false);
#endif
        });

        /// <summary>
        /// 获取PrefsKit实例
        /// 在运行时使用PlayerPrefs存储
        /// 在编辑器模式下使用EditorPrefs存储
        /// </summary>
        public static IPrefsKit PrefsUtil => LazyPrefsUtil.Value;
    }
}
