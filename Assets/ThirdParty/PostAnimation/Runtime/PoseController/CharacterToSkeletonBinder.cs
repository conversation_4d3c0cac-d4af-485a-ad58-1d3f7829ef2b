using System;
using System.Collections.Generic;
using UnityEngine;

public class CharacterToSkeletonBinder : MonoBehaviour
{
    public Animator TargetAnimator;
    private Animator m_animator;
    private Dictionary<HumanBodyBones, float> m_defaultRightRotations;

    public Action OnPosesUpdated;

    private bool m_isCalibrating = false;
    
    void Awake()
    {
        Initialize();
    }

    private void OnEnable()
    {
        
    }

    private void OnDisable()
    {

    }

    private void Update()
    {
        UpdatePoses();
    }

    private void HandleOnCalibrationStatusChanged(bool isCalibrating)
    {
        m_isCalibrating = isCalibrating;
    }

    public void Initialize()
    {
        if (m_animator == null)
        {
            m_animator = GetComponent<Animator>();
        }

        m_defaultRightRotations = GetDefaultRotations();
    }

    private Dictionary<HumanBodyBones, float> GetDefaultRotations()
    {
        var defaultRightRotations = new Dictionary<HumanBodyBones, float>();

        KeyValuePair<HumanBodyBones, float> rightFootKvp = GetPair(HumanBodyBones.RightFoot);
        KeyValuePair<HumanBodyBones, float> leftFootKvp = GetPair(HumanBodyBones.LeftFoot);
        KeyValuePair<HumanBodyBones, float> rightToesKvp = GetPair(HumanBodyBones.RightToes);
        KeyValuePair<HumanBodyBones, float> leftToesKvp = GetPair(HumanBodyBones.LeftToes);


        defaultRightRotations.Add(rightFootKvp.Key, rightFootKvp.Value);
        defaultRightRotations.Add(leftFootKvp.Key, leftFootKvp.Value);
        defaultRightRotations.Add(rightToesKvp.Key, rightToesKvp.Value);
        defaultRightRotations.Add(leftToesKvp.Key, leftToesKvp.Value);


        return defaultRightRotations;
    }

    private KeyValuePair<HumanBodyBones, float> GetPair(HumanBodyBones bone)
    {
        float rotationValue = m_animator.GetBoneTransform(bone).localEulerAngles.x;
        return new KeyValuePair<HumanBodyBones, float>(bone, rotationValue);
    }

    private void OnDestroy()
    {

    }

    public void UpdatePoses()
    {
        if (TargetAnimator == null || m_animator == null)
            return;

        TransferBackBonesPoses();
        TransferArmPosesOnlyRotations();
        TransferLowerLimbsPoses();

        OnPosesUpdated?.Invoke();
    }

    private void TransferBackBonesPoses()
    {
        TransferBonePose(HumanBodyBones.Hips);
        TransferBonePose(HumanBodyBones.Spine);
        TransferBonePose(HumanBodyBones.Chest);
        TransferBonePose(HumanBodyBones.UpperChest);
        TransferBoneRotation(HumanBodyBones.Neck);
        TransferBoneRotation(HumanBodyBones.Head);
    }


    private void TransferArmPosesOnlyRotations()
    {

        TransferBoneRotation(HumanBodyBones.RightShoulder);
        TransferBoneRotation(HumanBodyBones.LeftShoulder);

        TransferBoneRotation(HumanBodyBones.RightUpperArm);
        TransferBoneRotation(HumanBodyBones.LeftUpperArm);

        TransferBoneRotation(HumanBodyBones.RightLowerArm);
        TransferBoneRotation(HumanBodyBones.LeftLowerArm);

        SetHandRotation(isRightArm: true);
        SetHandRotation(isRightArm: false);
    }
    

    private void TransferBonePose(HumanBodyBones bone)
    {
        m_animator.GetBoneTransform(bone).position = TargetAnimator.GetBoneTransform(bone).position;
        m_animator.GetBoneTransform(bone).rotation = TargetAnimator.GetBoneTransform(bone).rotation;
    }

    private void TransferBoneRotation(HumanBodyBones bone)
    {
        m_animator.GetBoneTransform(bone).rotation = TargetAnimator.GetBoneTransform(bone).rotation;
    }

    private void SetHandPosition(bool isRightArm)
    {
        HumanBodyBones handBone = isRightArm ? HumanBodyBones.RightHand : HumanBodyBones.LeftHand;
        m_animator.GetBoneTransform(handBone).position = TargetAnimator.GetBoneTransform(handBone).position;
    }

    private void SetHandRotation(bool isRightArm)
    {
        Vector3 forward;
        Vector3 up;

        if (isRightArm)
        {
            up = TargetAnimator.GetBoneTransform(HumanBodyBones.RightHand).forward;
            forward = TargetAnimator.GetBoneTransform(HumanBodyBones.RightHand).up;
        }
        else
        {
            up = TargetAnimator.GetBoneTransform(HumanBodyBones.LeftHand).forward;
            forward = TargetAnimator.GetBoneTransform(HumanBodyBones.LeftHand).up;
        }
        var rotation = Quaternion.LookRotation(forward, up);
        m_animator.GetBoneTransform(
            isRightArm ? HumanBodyBones.RightHand : HumanBodyBones.LeftHand).rotation = rotation;
    }

    private void TransferLowerLimbsPoses()
    {
        TransferBonePose(HumanBodyBones.RightUpperLeg);
        TransferBonePose(HumanBodyBones.RightLowerLeg);
        TransferBonePose(HumanBodyBones.LeftUpperLeg);
        TransferBonePose(HumanBodyBones.LeftLowerLeg);

        TransferFeetPoses();
    }

    private void TransferFeetPoses()
    {
        
        TransferBonePose(HumanBodyBones.RightFoot);
        TransferBonePose(HumanBodyBones.RightToes);
        
        TransferBonePose(HumanBodyBones.LeftFoot);
        TransferBonePose(HumanBodyBones.LeftToes);
    }

}