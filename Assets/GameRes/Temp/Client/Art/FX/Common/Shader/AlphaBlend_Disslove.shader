// Made with Amplify Shader Editor v1.9.1
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "VFX/AlphaBlend_Disslove"
{
	Properties
	{
		[HideInInspector] _EmissionColor("Emission Color", Color) = (1,1,1,1)
		[HideInInspector] _AlphaCutoff("Alpha Cutoff ", Range(0, 1)) = 0.5
		[ASEBegin][Enum(UnityEngine.Rendering.CullMode)]_Cull("Cull", Float) = 0
		[KeywordEnum(A,R)] _RorA("R or A", Float) = 0
		_Main_Tex("Main_Tex", 2D) = "white" {}
		[Toggle]Maintex_ClanpU("MainTex_Clamp_U", Float) = 0
		[Toggle]Clamp1("MainTex_Clamp_V", Float) = 0
		_Float64("MainTex_Rotator", Range( 0 , 360)) = 0
		[Toggle(_COLOR_TWO_ON)] _Color_Two("Color_Two", Float) = 0
		[HDR]_Main_Color("Main_Color", Color) = (1,1,1,1)
		[HDR]_Back_Color("Back_Color", Color) = (1,1,1,1)
		[Toggle]Costom_UV("MainTex_CostomUV", Float) = 0
		_Main_U_speed("Main_U_speed", Float) = 0
		_Main_V_Speed("Main_V_Speed", Float) = 0
		_Mask("Mask", 2D) = "white" {}
		_Float65("Mask_Rotator", Range( 0 , 360)) = 0
		_Mask_U_speed("Mask_U_speed", Float) = 0
		_Mask_V_Speed("Mask_V_Speed", Float) = 0
		_Disslove_Tex("Disslove_Tex", 2D) = "white" {}
		_Disslove_U_speed("Disslove_U_speed", Float) = 0
		_Disslove_V_Speed("Disslove_V_Speed", Float) = 0
		[Toggle]_Disslove_Costom("Disslove_Costom", Float) = 0
		_Disslove_Int("Disslove_Int", Range( 0 , 1)) = 0
		_Disslove_Hardness1("Disslove_Hardness", Range( 0 , 1)) = 0
		[Toggle(_KEYWORD0)] _Keyword0("Disslove_Liner_Open", Float) = 0
		_Disslove_Liner("Disslove_Liner", Range( 0 , 0.2)) = 0
		[HDR]_Disslove_Color("Disslove_Color", Color) = (1,1,1,1)
		[Toggle(_AXIAL_DISSLOVE_OPEN_ON)] _Axial_Disslove_Open("Axial_Disslove_Open", Float) = 0
		_Float66("AxialDisslove_Rotator", Range( 0 , 360)) = 0
		[ASEEnd]_Disslove_Axial("Disslove_Axial", Range( 0 , 1)) = 0


		[HideInInspector]_QueueOffset("_QueueOffset", Float) = 0
        [HideInInspector]_QueueControl("_QueueControl", Float) = -1

        [HideInInspector][NoScaleOffset]unity_Lightmaps("unity_Lightmaps", 2DArray) = "" {}
        [HideInInspector][NoScaleOffset]unity_LightmapsInd("unity_LightmapsInd", 2DArray) = "" {}
        [HideInInspector][NoScaleOffset]unity_ShadowMasks("unity_ShadowMasks", 2DArray) = "" {}

		//_TessPhongStrength( "Tess Phong Strength", Range( 0, 1 ) ) = 0.5
		//_TessValue( "Tess Max Tessellation", Range( 1, 32 ) ) = 16
		//_TessMin( "Tess Min Distance", Float ) = 10
		//_TessMax( "Tess Max Distance", Float ) = 25
		//_TessEdgeLength ( "Tess Edge length", Range( 2, 50 ) ) = 16
		//_TessMaxDisp( "Tess Max Displacement", Float ) = 25
	}

	SubShader
	{
		LOD 0

		

		Tags { "RenderPipeline"="UniversalPipeline" "RenderType"="Transparent" "Queue"="Transparent" }

		Cull [_Cull]
		AlphaToMask Off

		

		HLSLINCLUDE
		#pragma target 4.5
		#pragma prefer_hlslcc gles
		#pragma exclude_renderers xboxone xboxseries playstation ps4 ps5 switch // ensure rendering platforms toggle list is visible

		#ifndef ASE_TESS_FUNCS
		#define ASE_TESS_FUNCS
		float4 FixedTess( float tessValue )
		{
			return tessValue;
		}

		float CalcDistanceTessFactor (float4 vertex, float minDist, float maxDist, float tess, float4x4 o2w, float3 cameraPos )
		{
			float3 wpos = mul(o2w,vertex).xyz;
			float dist = distance (wpos, cameraPos);
			float f = clamp(1.0 - (dist - minDist) / (maxDist - minDist), 0.01, 1.0) * tess;
			return f;
		}

		float4 CalcTriEdgeTessFactors (float3 triVertexFactors)
		{
			float4 tess;
			tess.x = 0.5 * (triVertexFactors.y + triVertexFactors.z);
			tess.y = 0.5 * (triVertexFactors.x + triVertexFactors.z);
			tess.z = 0.5 * (triVertexFactors.x + triVertexFactors.y);
			tess.w = (triVertexFactors.x + triVertexFactors.y + triVertexFactors.z) / 3.0f;
			return tess;
		}

		float CalcEdgeTessFactor (float3 wpos0, float3 wpos1, float edgeLen, float3 cameraPos, float4 scParams )
		{
			float dist = distance (0.5 * (wpos0+wpos1), cameraPos);
			float len = distance(wpos0, wpos1);
			float f = max(len * scParams.y / (edgeLen * dist), 1.0);
			return f;
		}

		float DistanceFromPlane (float3 pos, float4 plane)
		{
			float d = dot (float4(pos,1.0f), plane);
			return d;
		}

		bool WorldViewFrustumCull (float3 wpos0, float3 wpos1, float3 wpos2, float cullEps, float4 planes[6] )
		{
			float4 planeTest;
			planeTest.x = (( DistanceFromPlane(wpos0, planes[0]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos1, planes[0]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos2, planes[0]) > -cullEps) ? 1.0f : 0.0f );
			planeTest.y = (( DistanceFromPlane(wpos0, planes[1]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos1, planes[1]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos2, planes[1]) > -cullEps) ? 1.0f : 0.0f );
			planeTest.z = (( DistanceFromPlane(wpos0, planes[2]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos1, planes[2]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos2, planes[2]) > -cullEps) ? 1.0f : 0.0f );
			planeTest.w = (( DistanceFromPlane(wpos0, planes[3]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos1, planes[3]) > -cullEps) ? 1.0f : 0.0f ) +
						  (( DistanceFromPlane(wpos2, planes[3]) > -cullEps) ? 1.0f : 0.0f );
			return !all (planeTest);
		}

		float4 DistanceBasedTess( float4 v0, float4 v1, float4 v2, float tess, float minDist, float maxDist, float4x4 o2w, float3 cameraPos )
		{
			float3 f;
			f.x = CalcDistanceTessFactor (v0,minDist,maxDist,tess,o2w,cameraPos);
			f.y = CalcDistanceTessFactor (v1,minDist,maxDist,tess,o2w,cameraPos);
			f.z = CalcDistanceTessFactor (v2,minDist,maxDist,tess,o2w,cameraPos);

			return CalcTriEdgeTessFactors (f);
		}

		float4 EdgeLengthBasedTess( float4 v0, float4 v1, float4 v2, float edgeLength, float4x4 o2w, float3 cameraPos, float4 scParams )
		{
			float3 pos0 = mul(o2w,v0).xyz;
			float3 pos1 = mul(o2w,v1).xyz;
			float3 pos2 = mul(o2w,v2).xyz;
			float4 tess;
			tess.x = CalcEdgeTessFactor (pos1, pos2, edgeLength, cameraPos, scParams);
			tess.y = CalcEdgeTessFactor (pos2, pos0, edgeLength, cameraPos, scParams);
			tess.z = CalcEdgeTessFactor (pos0, pos1, edgeLength, cameraPos, scParams);
			tess.w = (tess.x + tess.y + tess.z) / 3.0f;
			return tess;
		}

		float4 EdgeLengthBasedTessCull( float4 v0, float4 v1, float4 v2, float edgeLength, float maxDisplacement, float4x4 o2w, float3 cameraPos, float4 scParams, float4 planes[6] )
		{
			float3 pos0 = mul(o2w,v0).xyz;
			float3 pos1 = mul(o2w,v1).xyz;
			float3 pos2 = mul(o2w,v2).xyz;
			float4 tess;

			if (WorldViewFrustumCull(pos0, pos1, pos2, maxDisplacement, planes))
			{
				tess = 0.0f;
			}
			else
			{
				tess.x = CalcEdgeTessFactor (pos1, pos2, edgeLength, cameraPos, scParams);
				tess.y = CalcEdgeTessFactor (pos2, pos0, edgeLength, cameraPos, scParams);
				tess.z = CalcEdgeTessFactor (pos0, pos1, edgeLength, cameraPos, scParams);
				tess.w = (tess.x + tess.y + tess.z) / 3.0f;
			}
			return tess;
		}
		#endif //ASE_TESS_FUNCS
		ENDHLSL

		
		Pass
		{
			
			Name "Forward"
			Tags { "LightMode"="UniversalForwardOnly" }

			Blend SrcAlpha OneMinusSrcAlpha, One OneMinusSrcAlpha
			ZWrite Off
			ZTest LEqual
			Offset 0 , 0
			ColorMask RGBA

			

			HLSLPROGRAM

			#define _SURFACE_TYPE_TRANSPARENT 1
			#define _RECEIVE_SHADOWS_OFF 1
			#define ASE_SRP_VERSION 140011


			#pragma instancing_options renderinglayer

			#pragma multi_compile _ LIGHTMAP_ON
        	#pragma multi_compile _ DIRLIGHTMAP_COMBINED
        	#pragma shader_feature _ _SAMPLE_GI
        	#pragma multi_compile_fragment _ _DBUFFER_MRT1 _DBUFFER_MRT2 _DBUFFER_MRT3
        	#pragma multi_compile_fragment _ DEBUG_DISPLAY
        	#pragma multi_compile_fragment _ _SCREEN_SPACE_OCCLUSION
        	#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS

			#pragma vertex vert
			#pragma fragment frag

			#define SHADERPASS SHADERPASS_UNLIT

			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Texture.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/TextureStack.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DBuffer.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/Editor/ShaderGraph/Includes/ShaderPass.hlsl"

			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Debug/Debugging3D.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/SurfaceData.hlsl"

			#define ASE_NEEDS_FRAG_COLOR
			#pragma shader_feature_local _KEYWORD0
			#pragma shader_feature_local _COLOR_TWO_ON
			#pragma shader_feature_local _RORA_A _RORA_R
			#pragma shader_feature_local _AXIAL_DISSLOVE_OPEN_ON


			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 ase_normal : NORMAL;
				float4 ase_color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct VertexOutput
			{
				float4 clipPos : SV_POSITION;
				#if defined(ASE_NEEDS_FRAG_WORLD_POSITION)
					float3 worldPos : TEXCOORD0;
				#endif
				#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR) && defined(ASE_NEEDS_FRAG_SHADOWCOORDS)
					float4 shadowCoord : TEXCOORD1;
				#endif
				#ifdef ASE_FOG
					float fogFactor : TEXCOORD2;
				#endif
				float4 ase_color : COLOR;
				float4 ase_texcoord3 : TEXCOORD3;
				float4 ase_texcoord4 : TEXCOORD4;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			CBUFFER_START(UnityPerMaterial)
			float4 _Main_Color;
			float4 _Back_Color;
			float4 _Main_Tex_ST;
			float4 _Mask_ST;
			float4 _Disslove_Color;
			float4 _Disslove_Tex_ST;
			float _Float65;
			float _Disslove_Liner;
			float _Disslove_Costom;
			float _Disslove_Int;
			float _Disslove_Axial;
			float _Float66;
			float _Disslove_U_speed;
			float _Mask_U_speed;
			float _Disslove_Hardness1;
			float Costom_UV;
			float Clamp1;
			float Maintex_ClanpU;
			float _Main_V_Speed;
			float _Main_U_speed;
			float _Float64;
			float _Disslove_V_Speed;
			float _Mask_V_Speed;
			#ifdef ASE_TESSELLATION
				float _TessPhongStrength;
				float _TessValue;
				float _TessMin;
				float _TessMax;
				float _TessEdgeLength;
				float _TessMaxDisp;
			#endif
			CBUFFER_END

			sampler2D _Main_Tex;
			sampler2D _Disslove_Tex;
			sampler2D _Mask;
			
			#ifdef UNITY_DOTS_INSTANCING_ENABLED
			UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
				UNITY_DOTS_INSTANCED_PROP(float, _Cull)
			UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
			#define _Cull UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float , _Cull)
			#endif


			
			VertexOutput VertexFunction ( VertexInput v  )
			{
				VertexOutput o = (VertexOutput)0;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

				o.ase_color = v.ase_color;
				o.ase_texcoord3.xy = v.ase_texcoord.xy;
				o.ase_texcoord4 = v.ase_texcoord1;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord3.zw = 0;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					float3 defaultVertexValue = v.vertex.xyz;
				#else
					float3 defaultVertexValue = float3(0, 0, 0);
				#endif

				float3 vertexValue = defaultVertexValue;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					v.vertex.xyz = vertexValue;
				#else
					v.vertex.xyz += vertexValue;
				#endif

				v.ase_normal = v.ase_normal;

				float3 positionWS = TransformObjectToWorld( v.vertex.xyz );
				float4 positionCS = TransformWorldToHClip( positionWS );

				#if defined(ASE_NEEDS_FRAG_WORLD_POSITION)
					o.worldPos = positionWS;
				#endif

				#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR) && defined(ASE_NEEDS_FRAG_SHADOWCOORDS)
					VertexPositionInputs vertexInput = (VertexPositionInputs)0;
					vertexInput.positionWS = positionWS;
					vertexInput.positionCS = positionCS;
					o.shadowCoord = GetShadowCoord( vertexInput );
				#endif

				#ifdef ASE_FOG
					o.fogFactor = ComputeFogFactor( positionCS.z );
				#endif

				o.clipPos = positionCS;

				return o;
			}

			#if defined(ASE_TESSELLATION)
			struct VertexControl
			{
				float4 vertex : INTERNALTESSPOS;
				float3 ase_normal : NORMAL;
				float4 ase_color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;

				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct TessellationFactors
			{
				float edge[3] : SV_TessFactor;
				float inside : SV_InsideTessFactor;
			};

			VertexControl vert ( VertexInput v )
			{
				VertexControl o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				o.vertex = v.vertex;
				o.ase_normal = v.ase_normal;
				o.ase_color = v.ase_color;
				o.ase_texcoord = v.ase_texcoord;
				o.ase_texcoord1 = v.ase_texcoord1;
				return o;
			}

			TessellationFactors TessellationFunction (InputPatch<VertexControl,3> v)
			{
				TessellationFactors o;
				float4 tf = 1;
				float tessValue = _TessValue; float tessMin = _TessMin; float tessMax = _TessMax;
				float edgeLength = _TessEdgeLength; float tessMaxDisp = _TessMaxDisp;
				#if defined(ASE_FIXED_TESSELLATION)
				tf = FixedTess( tessValue );
				#elif defined(ASE_DISTANCE_TESSELLATION)
				tf = DistanceBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, tessValue, tessMin, tessMax, GetObjectToWorldMatrix(), _WorldSpaceCameraPos );
				#elif defined(ASE_LENGTH_TESSELLATION)
				tf = EdgeLengthBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams );
				#elif defined(ASE_LENGTH_CULL_TESSELLATION)
				tf = EdgeLengthBasedTessCull(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, tessMaxDisp, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams, unity_CameraWorldClipPlanes );
				#endif
				o.edge[0] = tf.x; o.edge[1] = tf.y; o.edge[2] = tf.z; o.inside = tf.w;
				return o;
			}

			[domain("tri")]
			[partitioning("fractional_odd")]
			[outputtopology("triangle_cw")]
			[patchconstantfunc("TessellationFunction")]
			[outputcontrolpoints(3)]
			VertexControl HullFunction(InputPatch<VertexControl, 3> patch, uint id : SV_OutputControlPointID)
			{
			   return patch[id];
			}

			[domain("tri")]
			VertexOutput DomainFunction(TessellationFactors factors, OutputPatch<VertexControl, 3> patch, float3 bary : SV_DomainLocation)
			{
				VertexInput o = (VertexInput) 0;
				o.vertex = patch[0].vertex * bary.x + patch[1].vertex * bary.y + patch[2].vertex * bary.z;
				o.ase_normal = patch[0].ase_normal * bary.x + patch[1].ase_normal * bary.y + patch[2].ase_normal * bary.z;
				o.ase_color = patch[0].ase_color * bary.x + patch[1].ase_color * bary.y + patch[2].ase_color * bary.z;
				o.ase_texcoord = patch[0].ase_texcoord * bary.x + patch[1].ase_texcoord * bary.y + patch[2].ase_texcoord * bary.z;
				o.ase_texcoord1 = patch[0].ase_texcoord1 * bary.x + patch[1].ase_texcoord1 * bary.y + patch[2].ase_texcoord1 * bary.z;
				#if defined(ASE_PHONG_TESSELLATION)
				float3 pp[3];
				for (int i = 0; i < 3; ++i)
					pp[i] = o.vertex.xyz - patch[i].ase_normal * (dot(o.vertex.xyz, patch[i].ase_normal) - dot(patch[i].vertex.xyz, patch[i].ase_normal));
				float phongStrength = _TessPhongStrength;
				o.vertex.xyz = phongStrength * (pp[0]*bary.x + pp[1]*bary.y + pp[2]*bary.z) + (1.0f-phongStrength) * o.vertex.xyz;
				#endif
				UNITY_TRANSFER_INSTANCE_ID(patch[0], o);
				return VertexFunction(o);
			}
			#else
			VertexOutput vert ( VertexInput v )
			{
				return VertexFunction( v );
			}
			#endif

			half4 frag ( VertexOutput IN
				#ifdef _WRITE_RENDERING_LAYERS
				, out float4 outRenderingLayers : SV_Target1
				#endif
				, bool ase_vface : SV_IsFrontFace ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID( IN );
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX( IN );

				#if defined(ASE_NEEDS_FRAG_WORLD_POSITION)
					float3 WorldPosition = IN.worldPos;
				#endif

				float4 ShadowCoords = float4( 0, 0, 0, 0 );

				#if defined(ASE_NEEDS_FRAG_SHADOWCOORDS)
					#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR)
						ShadowCoords = IN.shadowCoord;
					#elif defined(MAIN_LIGHT_CALCULATE_SHADOWS)
						ShadowCoords = TransformWorldToShadowCoord( WorldPosition );
					#endif
				#endif

				float4 switchResult207 = (((ase_vface>0)?(_Main_Color):(_Back_Color)));
				#ifdef _COLOR_TWO_ON
				float4 staticSwitch210 = switchResult207;
				#else
				float4 staticSwitch210 = _Main_Color;
				#endif
				float2 uv_Main_Tex = IN.ase_texcoord3.xy * _Main_Tex_ST.xy + _Main_Tex_ST.zw;
				float cos117 = cos( ( ( _Float64 / 180.0 ) * PI ) );
				float sin117 = sin( ( ( _Float64 / 180.0 ) * PI ) );
				float2 rotator117 = mul( uv_Main_Tex - float2( 0.5,0.5 ) , float2x2( cos117 , -sin117 , sin117 , cos117 )) + float2( 0.5,0.5 );
				float2 appendResult15_g15 = (float2(_Main_U_speed , _Main_V_Speed));
				float4 texCoord122 = IN.ase_texcoord4;
				texCoord122.xy = IN.ase_texcoord4.xy * float2( 1,1 ) + float2( 0,0 );
				float2 break116 = rotator117;
				float temp_output_112_0 = ( texCoord122.x + break116.x );
				float lerpResult108 = lerp( temp_output_112_0 , saturate( temp_output_112_0 ) , Maintex_ClanpU);
				float temp_output_113_0 = ( texCoord122.y + break116.y );
				float lerpResult109 = lerp( temp_output_113_0 , saturate( temp_output_113_0 ) , Clamp1);
				float2 appendResult107 = (float2(lerpResult108 , lerpResult109));
				float2 lerpResult106 = lerp( ( rotator117 + ( ( frac( ( appendResult15_g15 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) , appendResult107 , Costom_UV);
				float4 tex2DNode29 = tex2D( _Main_Tex, lerpResult106 );
				float4 temp_cast_0 = (tex2DNode29.r).xxxx;
				#if defined(_RORA_A)
				float4 staticSwitch133 = tex2DNode29;
				#elif defined(_RORA_R)
				float4 staticSwitch133 = temp_cast_0;
				#else
				float4 staticSwitch133 = tex2DNode29;
				#endif
				float2 uv_Disslove_Tex = IN.ase_texcoord3.xy * _Disslove_Tex_ST.xy + _Disslove_Tex_ST.zw;
				float2 appendResult15_g9 = (float2(_Disslove_U_speed , _Disslove_V_Speed));
				float4 tex2DNode12 = tex2D( _Disslove_Tex, ( uv_Disslove_Tex + ( ( frac( ( appendResult15_g9 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) );
				float2 texCoord222 = IN.ase_texcoord3.xy * float2( 1,1 ) + float2( 0,0 );
				float cos180 = cos( ( ( _Float66 / 180.0 ) * PI ) );
				float sin180 = sin( ( ( _Float66 / 180.0 ) * PI ) );
				float2 rotator180 = mul( texCoord222 - float2( 0.5,0.5 ) , float2x2( cos180 , -sin180 , sin180 , cos180 )) + float2( 0.5,0.5 );
				float lerpResult174 = lerp( tex2DNode12.r , ( rotator180.x + 0.0 ) , _Disslove_Axial);
				#ifdef _AXIAL_DISSLOVE_OPEN_ON
				float staticSwitch175 = lerpResult174;
				#else
				float staticSwitch175 = tex2DNode12.r;
				#endif
				float Costom_1_W123 = texCoord122.z;
				float lerpResult125 = lerp( _Disslove_Int , Costom_1_W123 , _Disslove_Costom);
				float smoothstepResult18_g14 = smoothstep( _Disslove_Hardness1 , 1.0 , ( ( 1.0 + staticSwitch175 ) - ( ( lerpResult125 + _Disslove_Liner ) * 2.0 ) ));
				float temp_output_201_0 = saturate( smoothstepResult18_g14 );
				float Disslove_Subject194 = temp_output_201_0;
				float4 Disslove_Color198 = ( ( 1.0 - temp_output_201_0 ) * _Disslove_Color );
				#ifdef _KEYWORD0
				float4 staticSwitch206 = ( ( staticSwitch210 * staticSwitch133 * IN.ase_color * Disslove_Subject194 ) + Disslove_Color198 );
				#else
				float4 staticSwitch206 = ( staticSwitch210 * IN.ase_color * staticSwitch133 );
				#endif
				
				#if defined(_RORA_A)
				float staticSwitch134 = tex2DNode29.a;
				#elif defined(_RORA_R)
				float staticSwitch134 = tex2DNode29.r;
				#else
				float staticSwitch134 = tex2DNode29.a;
				#endif
				float smoothstepResult18_g13 = smoothstep( _Disslove_Hardness1 , 1.0 , ( ( 1.0 + staticSwitch175 ) - ( lerpResult125 * 2.0 ) ));
				float Dissslove152 = saturate( smoothstepResult18_g13 );
				float2 uv_Mask = IN.ase_texcoord3.xy * _Mask_ST.xy + _Mask_ST.zw;
				float cos140 = cos( ( ( _Float65 / 180.0 ) * PI ) );
				float sin140 = sin( ( ( _Float65 / 180.0 ) * PI ) );
				float2 rotator140 = mul( uv_Mask - float2( 0.5,0.5 ) , float2x2( cos140 , -sin140 , sin140 , cos140 )) + float2( 0.5,0.5 );
				float2 appendResult15_g12 = (float2(_Mask_U_speed , _Mask_V_Speed));
				float Mask150 = tex2D( _Mask, ( rotator140 + ( ( frac( ( appendResult15_g12 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) ).r;
				
				float3 BakedAlbedo = 0;
				float3 BakedEmission = 0;
				float3 Color = staticSwitch206.rgb;
				float Alpha = saturate( ( staticSwitch134 * Dissslove152 * IN.ase_color.a * Mask150 * _Main_Color.a ) );
				float AlphaClipThreshold = 0.5;
				float AlphaClipThresholdShadow = 0.5;

				#ifdef _ALPHATEST_ON
					clip( Alpha - AlphaClipThreshold );
				#endif

				#if defined(_DBUFFER)
					ApplyDecalToBaseColor(IN.clipPos, Color);
				#endif

				#if defined(_ALPHAPREMULTIPLY_ON)
				Color *= Alpha;
				#endif

				#ifdef LOD_FADE_CROSSFADE
					LODDitheringTransition( IN.clipPos.xyz, unity_LODFade.x );
				#endif

				#ifdef ASE_FOG
					Color = MixFog( Color, IN.fogFactor );
				#endif

				#ifdef _WRITE_RENDERING_LAYERS
					uint renderingLayers = GetMeshRenderingLayer();
					outRenderingLayers = float4( EncodeMeshRenderingLayer( renderingLayers ), 0, 0, 0 );
				#endif

				return half4( Color, Alpha );
			}
			ENDHLSL
		}

		
		Pass
		{
			
			Name "DepthOnly"
			Tags { "LightMode"="DepthOnly" }

			ZWrite On
			ColorMask 0
			AlphaToMask Off

			HLSLPROGRAM

			#define _SURFACE_TYPE_TRANSPARENT 1
			#define _RECEIVE_SHADOWS_OFF 1
			#define ASE_SRP_VERSION 140011


			#pragma vertex vert
			#pragma fragment frag

			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"

			#pragma shader_feature_local _RORA_A _RORA_R
			#pragma shader_feature_local _AXIAL_DISSLOVE_OPEN_ON


			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct VertexOutput
			{
				float4 clipPos : SV_POSITION;
				#if defined(ASE_NEEDS_FRAG_WORLD_POSITION)
				float3 worldPos : TEXCOORD0;
				#endif
				#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR) && defined(ASE_NEEDS_FRAG_SHADOWCOORDS)
				float4 shadowCoord : TEXCOORD1;
				#endif
				float4 ase_texcoord2 : TEXCOORD2;
				float4 ase_texcoord3 : TEXCOORD3;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			CBUFFER_START(UnityPerMaterial)
			float4 _Main_Color;
			float4 _Back_Color;
			float4 _Main_Tex_ST;
			float4 _Mask_ST;
			float4 _Disslove_Color;
			float4 _Disslove_Tex_ST;
			float _Float65;
			float _Disslove_Liner;
			float _Disslove_Costom;
			float _Disslove_Int;
			float _Disslove_Axial;
			float _Float66;
			float _Disslove_U_speed;
			float _Mask_U_speed;
			float _Disslove_Hardness1;
			float Costom_UV;
			float Clamp1;
			float Maintex_ClanpU;
			float _Main_V_Speed;
			float _Main_U_speed;
			float _Float64;
			float _Disslove_V_Speed;
			float _Mask_V_Speed;
			#ifdef ASE_TESSELLATION
				float _TessPhongStrength;
				float _TessValue;
				float _TessMin;
				float _TessMax;
				float _TessEdgeLength;
				float _TessMaxDisp;
			#endif
			CBUFFER_END

			sampler2D _Main_Tex;
			sampler2D _Disslove_Tex;
			sampler2D _Mask;
			
			#ifdef UNITY_DOTS_INSTANCING_ENABLED
			UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
				UNITY_DOTS_INSTANCED_PROP(float, _Cull)
			UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
			#define _Cull UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float , _Cull)
			#endif


			
			VertexOutput VertexFunction( VertexInput v  )
			{
				VertexOutput o = (VertexOutput)0;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

				o.ase_texcoord2.xy = v.ase_texcoord.xy;
				o.ase_texcoord3 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord2.zw = 0;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					float3 defaultVertexValue = v.vertex.xyz;
				#else
					float3 defaultVertexValue = float3(0, 0, 0);
				#endif

				float3 vertexValue = defaultVertexValue;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					v.vertex.xyz = vertexValue;
				#else
					v.vertex.xyz += vertexValue;
				#endif

				v.ase_normal = v.ase_normal;

				float3 positionWS = TransformObjectToWorld( v.vertex.xyz );

				#if defined(ASE_NEEDS_FRAG_WORLD_POSITION)
					o.worldPos = positionWS;
				#endif

				o.clipPos = TransformWorldToHClip( positionWS );
				#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR) && defined(ASE_NEEDS_FRAG_SHADOWCOORDS)
					VertexPositionInputs vertexInput = (VertexPositionInputs)0;
					vertexInput.positionWS = positionWS;
					vertexInput.positionCS = o.clipPos;
					o.shadowCoord = GetShadowCoord( vertexInput );
				#endif

				return o;
			}

			#if defined(ASE_TESSELLATION)
			struct VertexControl
			{
				float4 vertex : INTERNALTESSPOS;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;

				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct TessellationFactors
			{
				float edge[3] : SV_TessFactor;
				float inside : SV_InsideTessFactor;
			};

			VertexControl vert ( VertexInput v )
			{
				VertexControl o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				o.vertex = v.vertex;
				o.ase_normal = v.ase_normal;
				o.ase_texcoord = v.ase_texcoord;
				o.ase_texcoord1 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				return o;
			}

			TessellationFactors TessellationFunction (InputPatch<VertexControl,3> v)
			{
				TessellationFactors o;
				float4 tf = 1;
				float tessValue = _TessValue; float tessMin = _TessMin; float tessMax = _TessMax;
				float edgeLength = _TessEdgeLength; float tessMaxDisp = _TessMaxDisp;
				#if defined(ASE_FIXED_TESSELLATION)
				tf = FixedTess( tessValue );
				#elif defined(ASE_DISTANCE_TESSELLATION)
				tf = DistanceBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, tessValue, tessMin, tessMax, GetObjectToWorldMatrix(), _WorldSpaceCameraPos );
				#elif defined(ASE_LENGTH_TESSELLATION)
				tf = EdgeLengthBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams );
				#elif defined(ASE_LENGTH_CULL_TESSELLATION)
				tf = EdgeLengthBasedTessCull(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, tessMaxDisp, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams, unity_CameraWorldClipPlanes );
				#endif
				o.edge[0] = tf.x; o.edge[1] = tf.y; o.edge[2] = tf.z; o.inside = tf.w;
				return o;
			}

			[domain("tri")]
			[partitioning("fractional_odd")]
			[outputtopology("triangle_cw")]
			[patchconstantfunc("TessellationFunction")]
			[outputcontrolpoints(3)]
			VertexControl HullFunction(InputPatch<VertexControl, 3> patch, uint id : SV_OutputControlPointID)
			{
			   return patch[id];
			}

			[domain("tri")]
			VertexOutput DomainFunction(TessellationFactors factors, OutputPatch<VertexControl, 3> patch, float3 bary : SV_DomainLocation)
			{
				VertexInput o = (VertexInput) 0;
				o.vertex = patch[0].vertex * bary.x + patch[1].vertex * bary.y + patch[2].vertex * bary.z;
				o.ase_normal = patch[0].ase_normal * bary.x + patch[1].ase_normal * bary.y + patch[2].ase_normal * bary.z;
				o.ase_texcoord = patch[0].ase_texcoord * bary.x + patch[1].ase_texcoord * bary.y + patch[2].ase_texcoord * bary.z;
				o.ase_texcoord1 = patch[0].ase_texcoord1 * bary.x + patch[1].ase_texcoord1 * bary.y + patch[2].ase_texcoord1 * bary.z;
				o.ase_color = patch[0].ase_color * bary.x + patch[1].ase_color * bary.y + patch[2].ase_color * bary.z;
				#if defined(ASE_PHONG_TESSELLATION)
				float3 pp[3];
				for (int i = 0; i < 3; ++i)
					pp[i] = o.vertex.xyz - patch[i].ase_normal * (dot(o.vertex.xyz, patch[i].ase_normal) - dot(patch[i].vertex.xyz, patch[i].ase_normal));
				float phongStrength = _TessPhongStrength;
				o.vertex.xyz = phongStrength * (pp[0]*bary.x + pp[1]*bary.y + pp[2]*bary.z) + (1.0f-phongStrength) * o.vertex.xyz;
				#endif
				UNITY_TRANSFER_INSTANCE_ID(patch[0], o);
				return VertexFunction(o);
			}
			#else
			VertexOutput vert ( VertexInput v )
			{
				return VertexFunction( v );
			}
			#endif

			half4 frag(VertexOutput IN  ) : SV_TARGET
			{
				UNITY_SETUP_INSTANCE_ID(IN);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX( IN );

				#if defined(ASE_NEEDS_FRAG_WORLD_POSITION)
					float3 WorldPosition = IN.worldPos;
				#endif

				float4 ShadowCoords = float4( 0, 0, 0, 0 );

				#if defined(ASE_NEEDS_FRAG_SHADOWCOORDS)
					#if defined(REQUIRES_VERTEX_SHADOW_COORD_INTERPOLATOR)
						ShadowCoords = IN.shadowCoord;
					#elif defined(MAIN_LIGHT_CALCULATE_SHADOWS)
						ShadowCoords = TransformWorldToShadowCoord( WorldPosition );
					#endif
				#endif

				float2 uv_Main_Tex = IN.ase_texcoord2.xy * _Main_Tex_ST.xy + _Main_Tex_ST.zw;
				float cos117 = cos( ( ( _Float64 / 180.0 ) * PI ) );
				float sin117 = sin( ( ( _Float64 / 180.0 ) * PI ) );
				float2 rotator117 = mul( uv_Main_Tex - float2( 0.5,0.5 ) , float2x2( cos117 , -sin117 , sin117 , cos117 )) + float2( 0.5,0.5 );
				float2 appendResult15_g15 = (float2(_Main_U_speed , _Main_V_Speed));
				float4 texCoord122 = IN.ase_texcoord3;
				texCoord122.xy = IN.ase_texcoord3.xy * float2( 1,1 ) + float2( 0,0 );
				float2 break116 = rotator117;
				float temp_output_112_0 = ( texCoord122.x + break116.x );
				float lerpResult108 = lerp( temp_output_112_0 , saturate( temp_output_112_0 ) , Maintex_ClanpU);
				float temp_output_113_0 = ( texCoord122.y + break116.y );
				float lerpResult109 = lerp( temp_output_113_0 , saturate( temp_output_113_0 ) , Clamp1);
				float2 appendResult107 = (float2(lerpResult108 , lerpResult109));
				float2 lerpResult106 = lerp( ( rotator117 + ( ( frac( ( appendResult15_g15 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) , appendResult107 , Costom_UV);
				float4 tex2DNode29 = tex2D( _Main_Tex, lerpResult106 );
				#if defined(_RORA_A)
				float staticSwitch134 = tex2DNode29.a;
				#elif defined(_RORA_R)
				float staticSwitch134 = tex2DNode29.r;
				#else
				float staticSwitch134 = tex2DNode29.a;
				#endif
				float2 uv_Disslove_Tex = IN.ase_texcoord2.xy * _Disslove_Tex_ST.xy + _Disslove_Tex_ST.zw;
				float2 appendResult15_g9 = (float2(_Disslove_U_speed , _Disslove_V_Speed));
				float4 tex2DNode12 = tex2D( _Disslove_Tex, ( uv_Disslove_Tex + ( ( frac( ( appendResult15_g9 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) );
				float2 texCoord222 = IN.ase_texcoord2.xy * float2( 1,1 ) + float2( 0,0 );
				float cos180 = cos( ( ( _Float66 / 180.0 ) * PI ) );
				float sin180 = sin( ( ( _Float66 / 180.0 ) * PI ) );
				float2 rotator180 = mul( texCoord222 - float2( 0.5,0.5 ) , float2x2( cos180 , -sin180 , sin180 , cos180 )) + float2( 0.5,0.5 );
				float lerpResult174 = lerp( tex2DNode12.r , ( rotator180.x + 0.0 ) , _Disslove_Axial);
				#ifdef _AXIAL_DISSLOVE_OPEN_ON
				float staticSwitch175 = lerpResult174;
				#else
				float staticSwitch175 = tex2DNode12.r;
				#endif
				float Costom_1_W123 = texCoord122.z;
				float lerpResult125 = lerp( _Disslove_Int , Costom_1_W123 , _Disslove_Costom);
				float smoothstepResult18_g13 = smoothstep( _Disslove_Hardness1 , 1.0 , ( ( 1.0 + staticSwitch175 ) - ( lerpResult125 * 2.0 ) ));
				float Dissslove152 = saturate( smoothstepResult18_g13 );
				float2 uv_Mask = IN.ase_texcoord2.xy * _Mask_ST.xy + _Mask_ST.zw;
				float cos140 = cos( ( ( _Float65 / 180.0 ) * PI ) );
				float sin140 = sin( ( ( _Float65 / 180.0 ) * PI ) );
				float2 rotator140 = mul( uv_Mask - float2( 0.5,0.5 ) , float2x2( cos140 , -sin140 , sin140 , cos140 )) + float2( 0.5,0.5 );
				float2 appendResult15_g12 = (float2(_Mask_U_speed , _Mask_V_Speed));
				float Mask150 = tex2D( _Mask, ( rotator140 + ( ( frac( ( appendResult15_g12 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) ).r;
				

				float Alpha = saturate( ( staticSwitch134 * Dissslove152 * IN.ase_color.a * Mask150 * _Main_Color.a ) );
				float AlphaClipThreshold = 0.5;

				#ifdef _ALPHATEST_ON
					clip(Alpha - AlphaClipThreshold);
				#endif

				#ifdef LOD_FADE_CROSSFADE
					LODDitheringTransition( IN.clipPos.xyz, unity_LODFade.x );
				#endif
				return 0;
			}
			ENDHLSL
		}

		
		Pass
		{
			
            Name "SceneSelectionPass"
            Tags { "LightMode"="SceneSelectionPass" }

			Cull Off

			HLSLPROGRAM

			#define _SURFACE_TYPE_TRANSPARENT 1
			#define _RECEIVE_SHADOWS_OFF 1
			#define ASE_SRP_VERSION 140011


			#pragma vertex vert
			#pragma fragment frag

			#define ATTRIBUTES_NEED_NORMAL
			#define ATTRIBUTES_NEED_TANGENT
			#define SHADERPASS SHADERPASS_DEPTHONLY

			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Texture.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/TextureStack.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/Editor/ShaderGraph/Includes/ShaderPass.hlsl"

			#pragma shader_feature_local _RORA_A _RORA_R
			#pragma shader_feature_local _AXIAL_DISSLOVE_OPEN_ON


			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct VertexOutput
			{
				float4 clipPos : SV_POSITION;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			CBUFFER_START(UnityPerMaterial)
			float4 _Main_Color;
			float4 _Back_Color;
			float4 _Main_Tex_ST;
			float4 _Mask_ST;
			float4 _Disslove_Color;
			float4 _Disslove_Tex_ST;
			float _Float65;
			float _Disslove_Liner;
			float _Disslove_Costom;
			float _Disslove_Int;
			float _Disslove_Axial;
			float _Float66;
			float _Disslove_U_speed;
			float _Mask_U_speed;
			float _Disslove_Hardness1;
			float Costom_UV;
			float Clamp1;
			float Maintex_ClanpU;
			float _Main_V_Speed;
			float _Main_U_speed;
			float _Float64;
			float _Disslove_V_Speed;
			float _Mask_V_Speed;
			#ifdef ASE_TESSELLATION
				float _TessPhongStrength;
				float _TessValue;
				float _TessMin;
				float _TessMax;
				float _TessEdgeLength;
				float _TessMaxDisp;
			#endif
			CBUFFER_END

			sampler2D _Main_Tex;
			sampler2D _Disslove_Tex;
			sampler2D _Mask;
			
			#ifdef UNITY_DOTS_INSTANCING_ENABLED
			UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
				UNITY_DOTS_INSTANCED_PROP(float, _Cull)
			UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
			#define _Cull UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float , _Cull)
			#endif


			
			int _ObjectId;
			int _PassValue;

			struct SurfaceDescription
			{
				float Alpha;
				float AlphaClipThreshold;
			};

			VertexOutput VertexFunction(VertexInput v  )
			{
				VertexOutput o;
				ZERO_INITIALIZE(VertexOutput, o);

				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

				o.ase_texcoord.xy = v.ase_texcoord.xy;
				o.ase_texcoord1 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord.zw = 0;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					float3 defaultVertexValue = v.vertex.xyz;
				#else
					float3 defaultVertexValue = float3(0, 0, 0);
				#endif

				float3 vertexValue = defaultVertexValue;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					v.vertex.xyz = vertexValue;
				#else
					v.vertex.xyz += vertexValue;
				#endif

				v.ase_normal = v.ase_normal;

				float3 positionWS = TransformObjectToWorld( v.vertex.xyz );
				o.clipPos = TransformWorldToHClip(positionWS);

				return o;
			}

			#if defined(ASE_TESSELLATION)
			struct VertexControl
			{
				float4 vertex : INTERNALTESSPOS;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;

				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct TessellationFactors
			{
				float edge[3] : SV_TessFactor;
				float inside : SV_InsideTessFactor;
			};

			VertexControl vert ( VertexInput v )
			{
				VertexControl o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				o.vertex = v.vertex;
				o.ase_normal = v.ase_normal;
				o.ase_texcoord = v.ase_texcoord;
				o.ase_texcoord1 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				return o;
			}

			TessellationFactors TessellationFunction (InputPatch<VertexControl,3> v)
			{
				TessellationFactors o;
				float4 tf = 1;
				float tessValue = _TessValue; float tessMin = _TessMin; float tessMax = _TessMax;
				float edgeLength = _TessEdgeLength; float tessMaxDisp = _TessMaxDisp;
				#if defined(ASE_FIXED_TESSELLATION)
				tf = FixedTess( tessValue );
				#elif defined(ASE_DISTANCE_TESSELLATION)
				tf = DistanceBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, tessValue, tessMin, tessMax, GetObjectToWorldMatrix(), _WorldSpaceCameraPos );
				#elif defined(ASE_LENGTH_TESSELLATION)
				tf = EdgeLengthBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams );
				#elif defined(ASE_LENGTH_CULL_TESSELLATION)
				tf = EdgeLengthBasedTessCull(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, tessMaxDisp, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams, unity_CameraWorldClipPlanes );
				#endif
				o.edge[0] = tf.x; o.edge[1] = tf.y; o.edge[2] = tf.z; o.inside = tf.w;
				return o;
			}

			[domain("tri")]
			[partitioning("fractional_odd")]
			[outputtopology("triangle_cw")]
			[patchconstantfunc("TessellationFunction")]
			[outputcontrolpoints(3)]
			VertexControl HullFunction(InputPatch<VertexControl, 3> patch, uint id : SV_OutputControlPointID)
			{
				return patch[id];
			}

			[domain("tri")]
			VertexOutput DomainFunction(TessellationFactors factors, OutputPatch<VertexControl, 3> patch, float3 bary : SV_DomainLocation)
			{
				VertexInput o = (VertexInput) 0;
				o.vertex = patch[0].vertex * bary.x + patch[1].vertex * bary.y + patch[2].vertex * bary.z;
				o.ase_normal = patch[0].ase_normal * bary.x + patch[1].ase_normal * bary.y + patch[2].ase_normal * bary.z;
				o.ase_texcoord = patch[0].ase_texcoord * bary.x + patch[1].ase_texcoord * bary.y + patch[2].ase_texcoord * bary.z;
				o.ase_texcoord1 = patch[0].ase_texcoord1 * bary.x + patch[1].ase_texcoord1 * bary.y + patch[2].ase_texcoord1 * bary.z;
				o.ase_color = patch[0].ase_color * bary.x + patch[1].ase_color * bary.y + patch[2].ase_color * bary.z;
				#if defined(ASE_PHONG_TESSELLATION)
				float3 pp[3];
				for (int i = 0; i < 3; ++i)
					pp[i] = o.vertex.xyz - patch[i].ase_normal * (dot(o.vertex.xyz, patch[i].ase_normal) - dot(patch[i].vertex.xyz, patch[i].ase_normal));
				float phongStrength = _TessPhongStrength;
				o.vertex.xyz = phongStrength * (pp[0]*bary.x + pp[1]*bary.y + pp[2]*bary.z) + (1.0f-phongStrength) * o.vertex.xyz;
				#endif
				UNITY_TRANSFER_INSTANCE_ID(patch[0], o);
				return VertexFunction(o);
			}
			#else
			VertexOutput vert ( VertexInput v )
			{
				return VertexFunction( v );
			}
			#endif

			half4 frag(VertexOutput IN ) : SV_TARGET
			{
				SurfaceDescription surfaceDescription = (SurfaceDescription)0;

				float2 uv_Main_Tex = IN.ase_texcoord.xy * _Main_Tex_ST.xy + _Main_Tex_ST.zw;
				float cos117 = cos( ( ( _Float64 / 180.0 ) * PI ) );
				float sin117 = sin( ( ( _Float64 / 180.0 ) * PI ) );
				float2 rotator117 = mul( uv_Main_Tex - float2( 0.5,0.5 ) , float2x2( cos117 , -sin117 , sin117 , cos117 )) + float2( 0.5,0.5 );
				float2 appendResult15_g15 = (float2(_Main_U_speed , _Main_V_Speed));
				float4 texCoord122 = IN.ase_texcoord1;
				texCoord122.xy = IN.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				float2 break116 = rotator117;
				float temp_output_112_0 = ( texCoord122.x + break116.x );
				float lerpResult108 = lerp( temp_output_112_0 , saturate( temp_output_112_0 ) , Maintex_ClanpU);
				float temp_output_113_0 = ( texCoord122.y + break116.y );
				float lerpResult109 = lerp( temp_output_113_0 , saturate( temp_output_113_0 ) , Clamp1);
				float2 appendResult107 = (float2(lerpResult108 , lerpResult109));
				float2 lerpResult106 = lerp( ( rotator117 + ( ( frac( ( appendResult15_g15 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) , appendResult107 , Costom_UV);
				float4 tex2DNode29 = tex2D( _Main_Tex, lerpResult106 );
				#if defined(_RORA_A)
				float staticSwitch134 = tex2DNode29.a;
				#elif defined(_RORA_R)
				float staticSwitch134 = tex2DNode29.r;
				#else
				float staticSwitch134 = tex2DNode29.a;
				#endif
				float2 uv_Disslove_Tex = IN.ase_texcoord.xy * _Disslove_Tex_ST.xy + _Disslove_Tex_ST.zw;
				float2 appendResult15_g9 = (float2(_Disslove_U_speed , _Disslove_V_Speed));
				float4 tex2DNode12 = tex2D( _Disslove_Tex, ( uv_Disslove_Tex + ( ( frac( ( appendResult15_g9 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) );
				float2 texCoord222 = IN.ase_texcoord.xy * float2( 1,1 ) + float2( 0,0 );
				float cos180 = cos( ( ( _Float66 / 180.0 ) * PI ) );
				float sin180 = sin( ( ( _Float66 / 180.0 ) * PI ) );
				float2 rotator180 = mul( texCoord222 - float2( 0.5,0.5 ) , float2x2( cos180 , -sin180 , sin180 , cos180 )) + float2( 0.5,0.5 );
				float lerpResult174 = lerp( tex2DNode12.r , ( rotator180.x + 0.0 ) , _Disslove_Axial);
				#ifdef _AXIAL_DISSLOVE_OPEN_ON
				float staticSwitch175 = lerpResult174;
				#else
				float staticSwitch175 = tex2DNode12.r;
				#endif
				float Costom_1_W123 = texCoord122.z;
				float lerpResult125 = lerp( _Disslove_Int , Costom_1_W123 , _Disslove_Costom);
				float smoothstepResult18_g13 = smoothstep( _Disslove_Hardness1 , 1.0 , ( ( 1.0 + staticSwitch175 ) - ( lerpResult125 * 2.0 ) ));
				float Dissslove152 = saturate( smoothstepResult18_g13 );
				float2 uv_Mask = IN.ase_texcoord.xy * _Mask_ST.xy + _Mask_ST.zw;
				float cos140 = cos( ( ( _Float65 / 180.0 ) * PI ) );
				float sin140 = sin( ( ( _Float65 / 180.0 ) * PI ) );
				float2 rotator140 = mul( uv_Mask - float2( 0.5,0.5 ) , float2x2( cos140 , -sin140 , sin140 , cos140 )) + float2( 0.5,0.5 );
				float2 appendResult15_g12 = (float2(_Mask_U_speed , _Mask_V_Speed));
				float Mask150 = tex2D( _Mask, ( rotator140 + ( ( frac( ( appendResult15_g12 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) ).r;
				

				surfaceDescription.Alpha = saturate( ( staticSwitch134 * Dissslove152 * IN.ase_color.a * Mask150 * _Main_Color.a ) );
				surfaceDescription.AlphaClipThreshold = 0.5;

				#if _ALPHATEST_ON
					float alphaClipThreshold = 0.01f;
					#if ALPHA_CLIP_THRESHOLD
						alphaClipThreshold = surfaceDescription.AlphaClipThreshold;
					#endif
					clip(surfaceDescription.Alpha - alphaClipThreshold);
				#endif

				half4 outColor = half4(_ObjectId, _PassValue, 1.0, 1.0);
				return outColor;
			}
			ENDHLSL
		}

		
		Pass
		{
			
            Name "ScenePickingPass"
            Tags { "LightMode"="Picking" }

			HLSLPROGRAM

			#define _SURFACE_TYPE_TRANSPARENT 1
			#define _RECEIVE_SHADOWS_OFF 1
			#define ASE_SRP_VERSION 140011


			#pragma vertex vert
			#pragma fragment frag

			#define ATTRIBUTES_NEED_NORMAL
			#define ATTRIBUTES_NEED_TANGENT
			#define SHADERPASS SHADERPASS_DEPTHONLY

			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Texture.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/TextureStack.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/Editor/ShaderGraph/Includes/ShaderPass.hlsl"

			#pragma shader_feature_local _RORA_A _RORA_R
			#pragma shader_feature_local _AXIAL_DISSLOVE_OPEN_ON


			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct VertexOutput
			{
				float4 clipPos : SV_POSITION;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			CBUFFER_START(UnityPerMaterial)
			float4 _Main_Color;
			float4 _Back_Color;
			float4 _Main_Tex_ST;
			float4 _Mask_ST;
			float4 _Disslove_Color;
			float4 _Disslove_Tex_ST;
			float _Float65;
			float _Disslove_Liner;
			float _Disslove_Costom;
			float _Disslove_Int;
			float _Disslove_Axial;
			float _Float66;
			float _Disslove_U_speed;
			float _Mask_U_speed;
			float _Disslove_Hardness1;
			float Costom_UV;
			float Clamp1;
			float Maintex_ClanpU;
			float _Main_V_Speed;
			float _Main_U_speed;
			float _Float64;
			float _Disslove_V_Speed;
			float _Mask_V_Speed;
			#ifdef ASE_TESSELLATION
				float _TessPhongStrength;
				float _TessValue;
				float _TessMin;
				float _TessMax;
				float _TessEdgeLength;
				float _TessMaxDisp;
			#endif
			CBUFFER_END

			sampler2D _Main_Tex;
			sampler2D _Disslove_Tex;
			sampler2D _Mask;
			
			#ifdef UNITY_DOTS_INSTANCING_ENABLED
			UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
				UNITY_DOTS_INSTANCED_PROP(float, _Cull)
			UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
			#define _Cull UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float , _Cull)
			#endif


			
			float4 _SelectionID;


			struct SurfaceDescription
			{
				float Alpha;
				float AlphaClipThreshold;
			};

			VertexOutput VertexFunction(VertexInput v  )
			{
				VertexOutput o;
				ZERO_INITIALIZE(VertexOutput, o);

				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

				o.ase_texcoord.xy = v.ase_texcoord.xy;
				o.ase_texcoord1 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord.zw = 0;
				#ifdef ASE_ABSOLUTE_VERTEX_POS
					float3 defaultVertexValue = v.vertex.xyz;
				#else
					float3 defaultVertexValue = float3(0, 0, 0);
				#endif
				float3 vertexValue = defaultVertexValue;
				#ifdef ASE_ABSOLUTE_VERTEX_POS
					v.vertex.xyz = vertexValue;
				#else
					v.vertex.xyz += vertexValue;
				#endif
				v.ase_normal = v.ase_normal;

				float3 positionWS = TransformObjectToWorld( v.vertex.xyz );
				o.clipPos = TransformWorldToHClip(positionWS);
				return o;
			}

			#if defined(ASE_TESSELLATION)
			struct VertexControl
			{
				float4 vertex : INTERNALTESSPOS;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;

				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct TessellationFactors
			{
				float edge[3] : SV_TessFactor;
				float inside : SV_InsideTessFactor;
			};

			VertexControl vert ( VertexInput v )
			{
				VertexControl o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				o.vertex = v.vertex;
				o.ase_normal = v.ase_normal;
				o.ase_texcoord = v.ase_texcoord;
				o.ase_texcoord1 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				return o;
			}

			TessellationFactors TessellationFunction (InputPatch<VertexControl,3> v)
			{
				TessellationFactors o;
				float4 tf = 1;
				float tessValue = _TessValue; float tessMin = _TessMin; float tessMax = _TessMax;
				float edgeLength = _TessEdgeLength; float tessMaxDisp = _TessMaxDisp;
				#if defined(ASE_FIXED_TESSELLATION)
				tf = FixedTess( tessValue );
				#elif defined(ASE_DISTANCE_TESSELLATION)
				tf = DistanceBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, tessValue, tessMin, tessMax, GetObjectToWorldMatrix(), _WorldSpaceCameraPos );
				#elif defined(ASE_LENGTH_TESSELLATION)
				tf = EdgeLengthBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams );
				#elif defined(ASE_LENGTH_CULL_TESSELLATION)
				tf = EdgeLengthBasedTessCull(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, tessMaxDisp, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams, unity_CameraWorldClipPlanes );
				#endif
				o.edge[0] = tf.x; o.edge[1] = tf.y; o.edge[2] = tf.z; o.inside = tf.w;
				return o;
			}

			[domain("tri")]
			[partitioning("fractional_odd")]
			[outputtopology("triangle_cw")]
			[patchconstantfunc("TessellationFunction")]
			[outputcontrolpoints(3)]
			VertexControl HullFunction(InputPatch<VertexControl, 3> patch, uint id : SV_OutputControlPointID)
			{
				return patch[id];
			}

			[domain("tri")]
			VertexOutput DomainFunction(TessellationFactors factors, OutputPatch<VertexControl, 3> patch, float3 bary : SV_DomainLocation)
			{
				VertexInput o = (VertexInput) 0;
				o.vertex = patch[0].vertex * bary.x + patch[1].vertex * bary.y + patch[2].vertex * bary.z;
				o.ase_normal = patch[0].ase_normal * bary.x + patch[1].ase_normal * bary.y + patch[2].ase_normal * bary.z;
				o.ase_texcoord = patch[0].ase_texcoord * bary.x + patch[1].ase_texcoord * bary.y + patch[2].ase_texcoord * bary.z;
				o.ase_texcoord1 = patch[0].ase_texcoord1 * bary.x + patch[1].ase_texcoord1 * bary.y + patch[2].ase_texcoord1 * bary.z;
				o.ase_color = patch[0].ase_color * bary.x + patch[1].ase_color * bary.y + patch[2].ase_color * bary.z;
				#if defined(ASE_PHONG_TESSELLATION)
				float3 pp[3];
				for (int i = 0; i < 3; ++i)
					pp[i] = o.vertex.xyz - patch[i].ase_normal * (dot(o.vertex.xyz, patch[i].ase_normal) - dot(patch[i].vertex.xyz, patch[i].ase_normal));
				float phongStrength = _TessPhongStrength;
				o.vertex.xyz = phongStrength * (pp[0]*bary.x + pp[1]*bary.y + pp[2]*bary.z) + (1.0f-phongStrength) * o.vertex.xyz;
				#endif
				UNITY_TRANSFER_INSTANCE_ID(patch[0], o);
				return VertexFunction(o);
			}
			#else
			VertexOutput vert ( VertexInput v )
			{
				return VertexFunction( v );
			}
			#endif

			half4 frag(VertexOutput IN ) : SV_TARGET
			{
				SurfaceDescription surfaceDescription = (SurfaceDescription)0;

				float2 uv_Main_Tex = IN.ase_texcoord.xy * _Main_Tex_ST.xy + _Main_Tex_ST.zw;
				float cos117 = cos( ( ( _Float64 / 180.0 ) * PI ) );
				float sin117 = sin( ( ( _Float64 / 180.0 ) * PI ) );
				float2 rotator117 = mul( uv_Main_Tex - float2( 0.5,0.5 ) , float2x2( cos117 , -sin117 , sin117 , cos117 )) + float2( 0.5,0.5 );
				float2 appendResult15_g15 = (float2(_Main_U_speed , _Main_V_Speed));
				float4 texCoord122 = IN.ase_texcoord1;
				texCoord122.xy = IN.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				float2 break116 = rotator117;
				float temp_output_112_0 = ( texCoord122.x + break116.x );
				float lerpResult108 = lerp( temp_output_112_0 , saturate( temp_output_112_0 ) , Maintex_ClanpU);
				float temp_output_113_0 = ( texCoord122.y + break116.y );
				float lerpResult109 = lerp( temp_output_113_0 , saturate( temp_output_113_0 ) , Clamp1);
				float2 appendResult107 = (float2(lerpResult108 , lerpResult109));
				float2 lerpResult106 = lerp( ( rotator117 + ( ( frac( ( appendResult15_g15 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) , appendResult107 , Costom_UV);
				float4 tex2DNode29 = tex2D( _Main_Tex, lerpResult106 );
				#if defined(_RORA_A)
				float staticSwitch134 = tex2DNode29.a;
				#elif defined(_RORA_R)
				float staticSwitch134 = tex2DNode29.r;
				#else
				float staticSwitch134 = tex2DNode29.a;
				#endif
				float2 uv_Disslove_Tex = IN.ase_texcoord.xy * _Disslove_Tex_ST.xy + _Disslove_Tex_ST.zw;
				float2 appendResult15_g9 = (float2(_Disslove_U_speed , _Disslove_V_Speed));
				float4 tex2DNode12 = tex2D( _Disslove_Tex, ( uv_Disslove_Tex + ( ( frac( ( appendResult15_g9 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) );
				float2 texCoord222 = IN.ase_texcoord.xy * float2( 1,1 ) + float2( 0,0 );
				float cos180 = cos( ( ( _Float66 / 180.0 ) * PI ) );
				float sin180 = sin( ( ( _Float66 / 180.0 ) * PI ) );
				float2 rotator180 = mul( texCoord222 - float2( 0.5,0.5 ) , float2x2( cos180 , -sin180 , sin180 , cos180 )) + float2( 0.5,0.5 );
				float lerpResult174 = lerp( tex2DNode12.r , ( rotator180.x + 0.0 ) , _Disslove_Axial);
				#ifdef _AXIAL_DISSLOVE_OPEN_ON
				float staticSwitch175 = lerpResult174;
				#else
				float staticSwitch175 = tex2DNode12.r;
				#endif
				float Costom_1_W123 = texCoord122.z;
				float lerpResult125 = lerp( _Disslove_Int , Costom_1_W123 , _Disslove_Costom);
				float smoothstepResult18_g13 = smoothstep( _Disslove_Hardness1 , 1.0 , ( ( 1.0 + staticSwitch175 ) - ( lerpResult125 * 2.0 ) ));
				float Dissslove152 = saturate( smoothstepResult18_g13 );
				float2 uv_Mask = IN.ase_texcoord.xy * _Mask_ST.xy + _Mask_ST.zw;
				float cos140 = cos( ( ( _Float65 / 180.0 ) * PI ) );
				float sin140 = sin( ( ( _Float65 / 180.0 ) * PI ) );
				float2 rotator140 = mul( uv_Mask - float2( 0.5,0.5 ) , float2x2( cos140 , -sin140 , sin140 , cos140 )) + float2( 0.5,0.5 );
				float2 appendResult15_g12 = (float2(_Mask_U_speed , _Mask_V_Speed));
				float Mask150 = tex2D( _Mask, ( rotator140 + ( ( frac( ( appendResult15_g12 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) ).r;
				

				surfaceDescription.Alpha = saturate( ( staticSwitch134 * Dissslove152 * IN.ase_color.a * Mask150 * _Main_Color.a ) );
				surfaceDescription.AlphaClipThreshold = 0.5;

				#if _ALPHATEST_ON
					float alphaClipThreshold = 0.01f;
					#if ALPHA_CLIP_THRESHOLD
						alphaClipThreshold = surfaceDescription.AlphaClipThreshold;
					#endif
					clip(surfaceDescription.Alpha - alphaClipThreshold);
				#endif

				half4 outColor = 0;
				outColor = _SelectionID;

				return outColor;
			}

			ENDHLSL
		}

		
		Pass
		{
			
            Name "DepthNormals"
            Tags { "LightMode"="DepthNormalsOnly" }

			ZTest LEqual
			ZWrite On


			HLSLPROGRAM

			#define _SURFACE_TYPE_TRANSPARENT 1
			#define _RECEIVE_SHADOWS_OFF 1
			#define ASE_SRP_VERSION 140011


			#pragma vertex vert
			#pragma fragment frag

			#pragma multi_compile_fragment _ _WRITE_RENDERING_LAYERS
        	#pragma multi_compile_fragment _ _GBUFFER_NORMALS_OCT

			#define ATTRIBUTES_NEED_NORMAL
			#define ATTRIBUTES_NEED_TANGENT
			#define VARYINGS_NEED_NORMAL_WS

			#define SHADERPASS SHADERPASS_DEPTHNORMALSONLY

			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Texture.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
			#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/TextureStack.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderGraphFunctions.hlsl"
			#include "Packages/com.unity.render-pipelines.universal/Editor/ShaderGraph/Includes/ShaderPass.hlsl"

			#pragma shader_feature_local _RORA_A _RORA_R
			#pragma shader_feature_local _AXIAL_DISSLOVE_OPEN_ON


			struct VertexInput
			{
				float4 vertex : POSITION;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct VertexOutput
			{
				float4 clipPos : SV_POSITION;
				float3 normalWS : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				float4 ase_color : COLOR;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			CBUFFER_START(UnityPerMaterial)
			float4 _Main_Color;
			float4 _Back_Color;
			float4 _Main_Tex_ST;
			float4 _Mask_ST;
			float4 _Disslove_Color;
			float4 _Disslove_Tex_ST;
			float _Float65;
			float _Disslove_Liner;
			float _Disslove_Costom;
			float _Disslove_Int;
			float _Disslove_Axial;
			float _Float66;
			float _Disslove_U_speed;
			float _Mask_U_speed;
			float _Disslove_Hardness1;
			float Costom_UV;
			float Clamp1;
			float Maintex_ClanpU;
			float _Main_V_Speed;
			float _Main_U_speed;
			float _Float64;
			float _Disslove_V_Speed;
			float _Mask_V_Speed;
			#ifdef ASE_TESSELLATION
				float _TessPhongStrength;
				float _TessValue;
				float _TessMin;
				float _TessMax;
				float _TessEdgeLength;
				float _TessMaxDisp;
			#endif
			CBUFFER_END

			sampler2D _Main_Tex;
			sampler2D _Disslove_Tex;
			sampler2D _Mask;
			
			#ifdef UNITY_DOTS_INSTANCING_ENABLED
			UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
				UNITY_DOTS_INSTANCED_PROP(float, _Cull)
			UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
			#define _Cull UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float , _Cull)
			#endif


			
			struct SurfaceDescription
			{
				float Alpha;
				float AlphaClipThreshold;
			};

			VertexOutput VertexFunction(VertexInput v  )
			{
				VertexOutput o;
				ZERO_INITIALIZE(VertexOutput, o);

				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				o.ase_texcoord2 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				#ifdef ASE_ABSOLUTE_VERTEX_POS
					float3 defaultVertexValue = v.vertex.xyz;
				#else
					float3 defaultVertexValue = float3(0, 0, 0);
				#endif

				float3 vertexValue = defaultVertexValue;

				#ifdef ASE_ABSOLUTE_VERTEX_POS
					v.vertex.xyz = vertexValue;
				#else
					v.vertex.xyz += vertexValue;
				#endif

				v.ase_normal = v.ase_normal;

				float3 positionWS = TransformObjectToWorld( v.vertex.xyz );
				float3 normalWS = TransformObjectToWorldNormal(v.ase_normal);

				o.clipPos = TransformWorldToHClip(positionWS);
				o.normalWS.xyz =  normalWS;

				return o;
			}

			#if defined(ASE_TESSELLATION)
			struct VertexControl
			{
				float4 vertex : INTERNALTESSPOS;
				float3 ase_normal : NORMAL;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_color : COLOR;

				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			struct TessellationFactors
			{
				float edge[3] : SV_TessFactor;
				float inside : SV_InsideTessFactor;
			};

			VertexControl vert ( VertexInput v )
			{
				VertexControl o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_TRANSFER_INSTANCE_ID(v, o);
				o.vertex = v.vertex;
				o.ase_normal = v.ase_normal;
				o.ase_texcoord = v.ase_texcoord;
				o.ase_texcoord1 = v.ase_texcoord1;
				o.ase_color = v.ase_color;
				return o;
			}

			TessellationFactors TessellationFunction (InputPatch<VertexControl,3> v)
			{
				TessellationFactors o;
				float4 tf = 1;
				float tessValue = _TessValue; float tessMin = _TessMin; float tessMax = _TessMax;
				float edgeLength = _TessEdgeLength; float tessMaxDisp = _TessMaxDisp;
				#if defined(ASE_FIXED_TESSELLATION)
				tf = FixedTess( tessValue );
				#elif defined(ASE_DISTANCE_TESSELLATION)
				tf = DistanceBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, tessValue, tessMin, tessMax, GetObjectToWorldMatrix(), _WorldSpaceCameraPos );
				#elif defined(ASE_LENGTH_TESSELLATION)
				tf = EdgeLengthBasedTess(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams );
				#elif defined(ASE_LENGTH_CULL_TESSELLATION)
				tf = EdgeLengthBasedTessCull(v[0].vertex, v[1].vertex, v[2].vertex, edgeLength, tessMaxDisp, GetObjectToWorldMatrix(), _WorldSpaceCameraPos, _ScreenParams, unity_CameraWorldClipPlanes );
				#endif
				o.edge[0] = tf.x; o.edge[1] = tf.y; o.edge[2] = tf.z; o.inside = tf.w;
				return o;
			}

			[domain("tri")]
			[partitioning("fractional_odd")]
			[outputtopology("triangle_cw")]
			[patchconstantfunc("TessellationFunction")]
			[outputcontrolpoints(3)]
			VertexControl HullFunction(InputPatch<VertexControl, 3> patch, uint id : SV_OutputControlPointID)
			{
				return patch[id];
			}

			[domain("tri")]
			VertexOutput DomainFunction(TessellationFactors factors, OutputPatch<VertexControl, 3> patch, float3 bary : SV_DomainLocation)
			{
				VertexInput o = (VertexInput) 0;
				o.vertex = patch[0].vertex * bary.x + patch[1].vertex * bary.y + patch[2].vertex * bary.z;
				o.ase_normal = patch[0].ase_normal * bary.x + patch[1].ase_normal * bary.y + patch[2].ase_normal * bary.z;
				o.ase_texcoord = patch[0].ase_texcoord * bary.x + patch[1].ase_texcoord * bary.y + patch[2].ase_texcoord * bary.z;
				o.ase_texcoord1 = patch[0].ase_texcoord1 * bary.x + patch[1].ase_texcoord1 * bary.y + patch[2].ase_texcoord1 * bary.z;
				o.ase_color = patch[0].ase_color * bary.x + patch[1].ase_color * bary.y + patch[2].ase_color * bary.z;
				#if defined(ASE_PHONG_TESSELLATION)
				float3 pp[3];
				for (int i = 0; i < 3; ++i)
					pp[i] = o.vertex.xyz - patch[i].ase_normal * (dot(o.vertex.xyz, patch[i].ase_normal) - dot(patch[i].vertex.xyz, patch[i].ase_normal));
				float phongStrength = _TessPhongStrength;
				o.vertex.xyz = phongStrength * (pp[0]*bary.x + pp[1]*bary.y + pp[2]*bary.z) + (1.0f-phongStrength) * o.vertex.xyz;
				#endif
				UNITY_TRANSFER_INSTANCE_ID(patch[0], o);
				return VertexFunction(o);
			}
			#else
			VertexOutput vert ( VertexInput v )
			{
				return VertexFunction( v );
			}
			#endif

			void frag( VertexOutput IN
				, out half4 outNormalWS : SV_Target0
			#ifdef _WRITE_RENDERING_LAYERS
				, out float4 outRenderingLayers : SV_Target1
			#endif
				 )
			{
				SurfaceDescription surfaceDescription = (SurfaceDescription)0;

				float2 uv_Main_Tex = IN.ase_texcoord1.xy * _Main_Tex_ST.xy + _Main_Tex_ST.zw;
				float cos117 = cos( ( ( _Float64 / 180.0 ) * PI ) );
				float sin117 = sin( ( ( _Float64 / 180.0 ) * PI ) );
				float2 rotator117 = mul( uv_Main_Tex - float2( 0.5,0.5 ) , float2x2( cos117 , -sin117 , sin117 , cos117 )) + float2( 0.5,0.5 );
				float2 appendResult15_g15 = (float2(_Main_U_speed , _Main_V_Speed));
				float4 texCoord122 = IN.ase_texcoord2;
				texCoord122.xy = IN.ase_texcoord2.xy * float2( 1,1 ) + float2( 0,0 );
				float2 break116 = rotator117;
				float temp_output_112_0 = ( texCoord122.x + break116.x );
				float lerpResult108 = lerp( temp_output_112_0 , saturate( temp_output_112_0 ) , Maintex_ClanpU);
				float temp_output_113_0 = ( texCoord122.y + break116.y );
				float lerpResult109 = lerp( temp_output_113_0 , saturate( temp_output_113_0 ) , Clamp1);
				float2 appendResult107 = (float2(lerpResult108 , lerpResult109));
				float2 lerpResult106 = lerp( ( rotator117 + ( ( frac( ( appendResult15_g15 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) , appendResult107 , Costom_UV);
				float4 tex2DNode29 = tex2D( _Main_Tex, lerpResult106 );
				#if defined(_RORA_A)
				float staticSwitch134 = tex2DNode29.a;
				#elif defined(_RORA_R)
				float staticSwitch134 = tex2DNode29.r;
				#else
				float staticSwitch134 = tex2DNode29.a;
				#endif
				float2 uv_Disslove_Tex = IN.ase_texcoord1.xy * _Disslove_Tex_ST.xy + _Disslove_Tex_ST.zw;
				float2 appendResult15_g9 = (float2(_Disslove_U_speed , _Disslove_V_Speed));
				float4 tex2DNode12 = tex2D( _Disslove_Tex, ( uv_Disslove_Tex + ( ( frac( ( appendResult15_g9 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) );
				float2 texCoord222 = IN.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				float cos180 = cos( ( ( _Float66 / 180.0 ) * PI ) );
				float sin180 = sin( ( ( _Float66 / 180.0 ) * PI ) );
				float2 rotator180 = mul( texCoord222 - float2( 0.5,0.5 ) , float2x2( cos180 , -sin180 , sin180 , cos180 )) + float2( 0.5,0.5 );
				float lerpResult174 = lerp( tex2DNode12.r , ( rotator180.x + 0.0 ) , _Disslove_Axial);
				#ifdef _AXIAL_DISSLOVE_OPEN_ON
				float staticSwitch175 = lerpResult174;
				#else
				float staticSwitch175 = tex2DNode12.r;
				#endif
				float Costom_1_W123 = texCoord122.z;
				float lerpResult125 = lerp( _Disslove_Int , Costom_1_W123 , _Disslove_Costom);
				float smoothstepResult18_g13 = smoothstep( _Disslove_Hardness1 , 1.0 , ( ( 1.0 + staticSwitch175 ) - ( lerpResult125 * 2.0 ) ));
				float Dissslove152 = saturate( smoothstepResult18_g13 );
				float2 uv_Mask = IN.ase_texcoord1.xy * _Mask_ST.xy + _Mask_ST.zw;
				float cos140 = cos( ( ( _Float65 / 180.0 ) * PI ) );
				float sin140 = sin( ( ( _Float65 / 180.0 ) * PI ) );
				float2 rotator140 = mul( uv_Mask - float2( 0.5,0.5 ) , float2x2( cos140 , -sin140 , sin140 , cos140 )) + float2( 0.5,0.5 );
				float2 appendResult15_g12 = (float2(_Mask_U_speed , _Mask_V_Speed));
				float Mask150 = tex2D( _Mask, ( rotator140 + ( ( frac( ( appendResult15_g12 * _TimeParameters.x ) ) * 2.0 ) + -1.0 ) ) ).r;
				

				surfaceDescription.Alpha = saturate( ( staticSwitch134 * Dissslove152 * IN.ase_color.a * Mask150 * _Main_Color.a ) );
				surfaceDescription.AlphaClipThreshold = 0.5;

				#if _ALPHATEST_ON
					clip(surfaceDescription.Alpha - surfaceDescription.AlphaClipThreshold);
				#endif

				#ifdef LOD_FADE_CROSSFADE
					LODDitheringTransition( IN.clipPos.xyz, unity_LODFade.x );
				#endif

				#if defined(_GBUFFER_NORMALS_OCT)
					float3 normalWS = normalize(IN.normalWS);
					float2 octNormalWS = PackNormalOctQuadEncode(normalWS);           // values between [-1, +1], must use fp32 on some platforms
					float2 remappedOctNormalWS = saturate(octNormalWS * 0.5 + 0.5);   // values between [ 0,  1]
					half3 packedNormalWS = PackFloat2To888(remappedOctNormalWS);      // values between [ 0,  1]
					outNormalWS = half4(packedNormalWS, 0.0);
				#else
					float3 normalWS = IN.normalWS;
					outNormalWS = half4(NormalizeNormalPerPixel(normalWS), 0.0);
				#endif

				#ifdef _WRITE_RENDERING_LAYERS
					uint renderingLayers = GetMeshRenderingLayer();
					outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
				#endif
			}

			ENDHLSL
		}

	
	}
	
	CustomEditor "UnityEditor.ShaderGraphUnlitGUI"
	FallBack "Hidden/Shader Graph/FallbackError"
	
	Fallback Off
}
/*ASEBEGIN
Version=19100
Node;AmplifyShaderEditor.CommentaryNode;149;-1134.733,-472.8722;Inherit;False;2489.937;624.6502;;50;220;221;210;202;36;200;199;156;151;134;219;218;31;105;110;113;155;153;154;116;217;216;117;118;109;107;37;111;112;108;115;123;60;106;215;34;33;133;211;209;207;204;195;121;119;114;32;35;29;122;Main_Color;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;148;-1141.471,231.0591;Inherit;False;2552.228;646.3735;;32;198;196;197;152;192;201;193;194;191;175;23;187;125;176;25;174;188;180;182;181;183;184;185;26;126;124;28;27;15;59;12;222;Disslove;1,1,1,1;0;0
Node;AmplifyShaderEditor.CommentaryNode;146;-1137.167,965.824;Inherit;False;1806.944;383.097;;10;144;142;143;141;140;138;139;136;150;169;Mask;1,1,1,1;0;0
Node;AmplifyShaderEditor.FunctionNode;59;-617.9609,320.6395;Inherit;False;UV_Speed;-1;;9;0c1ea31811e6e9143a086c3e67661d7b;0;3;6;FLOAT2;0,0;False;16;FLOAT;0;False;17;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;15;-866.0248,316.7298;Inherit;False;0;12;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;136;46.60508,1078.268;Inherit;True;Property;_Mask;Mask;12;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TextureCoordinatesNode;141;-690.5267,1061.945;Inherit;False;0;136;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleDivideOpNode;143;-766.4894,1201.803;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;180;False;1;FLOAT;0
Node;AmplifyShaderEditor.PiNode;142;-648.9463,1210.333;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;144;-1019.463,1072.992;Inherit;False;Property;_Float65;Mask_Rotator;13;0;Create;False;0;0;0;False;0;False;0;90;0;360;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;27;-1088.247,299.1507;Inherit;False;Property;_Disslove_U_speed;Disslove_U_speed;17;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;28;-1087.247,379.7387;Inherit;False;Property;_Disslove_V_Speed;Disslove_V_Speed;18;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;29;560.7575,-162.3016;Inherit;True;Property;_Main_Tex;Main_Tex;2;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;35;1133.047,-35.80608;Inherit;False;5;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;32;-890.8669,-214.5427;Inherit;False;0;29;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;114;-100.2318,-190.0969;Inherit;False;Property;Clamp1;MainTex_Clamp_V;4;1;[Toggle];Create;False;0;0;0;False;0;False;0;1;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;159;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;ExtraPrePass;0;0;ExtraPrePass;5;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;True;1;1;False;;0;False;;0;1;False;;0;False;;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;True;True;True;True;0;False;;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;True;1;False;;True;3;False;;True;True;0;False;;0;False;;True;0;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;161;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;ShadowCaster;0;2;ShadowCaster;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;False;False;True;False;False;False;False;0;False;;False;False;False;False;False;False;False;False;False;True;1;False;;True;3;False;;False;True;1;LightMode=ShadowCaster;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;162;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;DepthOnly;0;3;DepthOnly;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;False;False;True;False;False;False;False;0;False;;False;False;False;False;False;False;False;False;False;True;1;False;;False;False;True;1;LightMode=DepthOnly;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;163;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;Meta;0;4;Meta;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;2;False;;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=Meta;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;164;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;Universal2D;0;5;Universal2D;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;True;1;1;False;;0;False;;0;1;False;;0;False;;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;True;True;True;True;0;False;;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;True;1;False;;True;3;False;;True;True;0;False;;0;False;;True;1;LightMode=Universal2D;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;165;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;SceneSelectionPass;0;6;SceneSelectionPass;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;2;False;;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=SceneSelectionPass;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;166;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;ScenePickingPass;0;7;ScenePickingPass;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=Picking;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;167;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;DepthNormals;0;8;DepthNormals;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;False;;True;3;False;;False;True;1;LightMode=DepthNormalsOnly;False;False;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;168;1629.508,-165.4752;Float;False;False;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;New Amplify Shader;2992e84f91cbeb14eab234972e07ea9d;True;DepthNormalsOnly;0;9;DepthNormalsOnly;0;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Opaque=RenderType;Queue=Geometry=Queue=0;True;5;True;12;all;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;False;;True;3;False;;False;True;1;LightMode=DepthNormalsOnly;False;True;9;d3d11;metal;vulkan;xboxone;xboxseries;playstation;ps4;ps5;switch;0;;0;0;Standard;0;False;0
Node;AmplifyShaderEditor.RangedFloatNode;139;-440.4617,1265.955;Inherit;False;Property;_Mask_V_Speed;Mask_V_Speed;15;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;138;-437.7801,1186.456;Inherit;False;Property;_Mask_U_speed;Mask_U_speed;14;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RotatorNode;140;-429.3029,1038.556;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0.5,0.5;False;2;FLOAT;1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.FunctionNode;169;-208.2735,1081.265;Inherit;False;UV_Speed;-1;;12;0c1ea31811e6e9143a086c3e67661d7b;0;3;6;FLOAT2;0,0;False;16;FLOAT;0;False;17;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.GetLocalVarNode;124;-422.0467,571.2149;Inherit;False;123;Costom_1_W;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;126;-627.23,600.2289;Inherit;False;Property;_Disslove_Costom;Disslove_Costom;19;1;[Toggle];Create;True;0;0;0;False;0;False;0;1;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;185;-151.8632,689.8221;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.BreakToComponentsNode;184;-283.3998,690.3953;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15
Node;AmplifyShaderEditor.PiNode;183;-708.6365,751.8198;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleDivideOpNode;181;-837.6686,738.4642;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;180;False;1;FLOAT;0
Node;AmplifyShaderEditor.RotatorNode;180;-477.5175,692.2117;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0.5,0.5;False;2;FLOAT;1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;188;308.6932,503.5818;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;174;-20.67719,366.6213;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;25;-635.0855,502.6102;Inherit;False;Property;_Disslove_Int;Disslove_Int;20;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;176;10.51952,633.4267;Inherit;False;Property;_Disslove_Hardness1;Disslove_Hardness;21;0;Create;True;0;0;0;False;0;False;0;0.917;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;125;-165.684,551.6389;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;23;400.412,539.6179;Inherit;True;Disslove;-1;;13;a899dafa84bc0a146acdbb897442bae5;0;3;24;FLOAT;0;False;22;FLOAT;0;False;16;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;191;223.1164,429.548;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;201;561.0522,349.2838;Inherit;False;Disslove;-1;;14;a899dafa84bc0a146acdbb897442bae5;0;3;24;FLOAT;0;False;22;FLOAT;0;False;16;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;152;807.2435,520.2601;Inherit;False;Dissslove;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;197;807.9444,642.1771;Inherit;False;Property;_Disslove_Color;Disslove_Color;24;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.5759162,1.273923,2,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;196;1018.004,529.5648;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;198;1190.307,640.9667;Inherit;False;Disslove_Color;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.OneMinusNode;193;834.0348,390.4361;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;194;842.0402,278.6506;Inherit;False;Disslove_Subject;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;204;1204.888,-444.0966;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SwitchByFaceNode;207;774.5767,-440.4597;Inherit;False;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;209;570.4652,-335.2932;Inherit;False;Property;_Back_Color;Back_Color;8;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;4.047201,4.047201,4.047201,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;211;532.4752,71.43304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;133;894.9265,-180.7153;Inherit;False;Property;_RorA;R or A;1;0;Create;True;0;0;0;False;0;False;0;0;0;True;;KeywordEnum;2;A;R;Create;True;True;All;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;33;-194.0943,-77.74641;Inherit;False;Property;_Main_U_speed;Main_U_speed;10;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;34;-194.0943,2.253188;Inherit;False;Property;_Main_V_Speed;Main_V_Speed;11;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;187;394.283,433.6452;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;12;-328.8239,286.3828;Inherit;True;Property;_Disslove_Tex;Disslove_Tex;16;0;Create;True;0;0;0;False;0;False;-1;None;6b586f2106fc1d840a3e2528f9293d22;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WireNode;215;545.114,-360.8241;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.LerpOp;106;383.7695,-41.255;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.VertexColorNode;60;398.4144,-217.3825;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RegisterLocalVarNode;123;-804.8321,-318.8983;Inherit;False;Costom_1_W;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;115;-582.6592,-353.317;Inherit;False;Property;Maintex_ClanpU;MainTex_Clamp_U;3;1;[Toggle];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.LerpOp;108;26.0791,-401.16;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;112;-285.7588,-399.7745;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SaturateNode;111;-134.9208,-378.1599;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;37;233.7207,-409.6286;Inherit;False;Property;_Main_Color;Main_Color;7;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.310098,0.6976873,1.685661,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.DynamicAppendNode;107;255.338,-199.9472;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.LerpOp;109;94.25842,-242.3701;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;216;-658.8211,-136.5848;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;217;-493.0515,-77.76357;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.BreakToComponentsNode;116;-415.0804,-185.5235;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15
Node;AmplifyShaderEditor.WireNode;154;-307.8731,-254.3196;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;153;-444.9333,-103.1641;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;155;-273.4258,-149.3212;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;113;-217.7463,-256.416;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SaturateNode;110;-104.4996,-265.1599;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;105;147.4665,30.06079;Inherit;False;Property;Costom_UV;MainTex_CostomUV;9;1;[Toggle];Create;False;0;0;0;False;0;False;0;1;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.FunctionNode;31;31.15886,-86.97787;Inherit;False;UV_Speed;-1;;15;0c1ea31811e6e9143a086c3e67661d7b;0;3;6;FLOAT2;0,0;False;16;FLOAT;0;False;17;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.WireNode;218;668.6696,94.68972;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.WireNode;219;917.3221,11.80505;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;134;885.4106,-82.52676;Inherit;False;Property;_Keyword1;Keyword 0;1;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Reference;133;True;True;All;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;151;958.34,27.31431;Inherit;False;150;Mask;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;156;765.4111,45.43805;Inherit;False;152;Dissslove;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;199;1050.806,-119.5313;Inherit;False;198;Disslove_Color;1;0;OBJECT;;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;200;1232.8,-253.4564;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;36;1091.694,-343.7232;Inherit;False;4;4;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.WireNode;202;989.8022,-209.2488;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;210;846.5766,-341.4597;Inherit;False;Property;_Color_Two;Color_Two;6;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;R;A;Create;True;True;All;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.WireNode;221;783.256,-318.2601;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.WireNode;220;982.256,-380.2601;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;122;-1062.711,-386.7411;Inherit;False;1;-1;4;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;192;-156.5825,785.1714;Inherit;False;Property;_Disslove_Liner;Disslove_Liner;23;0;Create;True;0;0;0;False;0;False;0;0.018;0;0.2;0;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;195;563.1533,32.95455;Inherit;False;194;Disslove_Subject;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;182;-1124.613,733.402;Inherit;False;Property;_Float66;AxialDisslove_Rotator;26;0;Create;False;0;0;0;False;0;False;0;0;0;360;0;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;175;116.0274,321.1485;Inherit;False;Property;_Axial_Disslove_Open;Axial_Disslove_Open;25;0;Create;True;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Off;Open;Create;True;True;All;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;206;1414.76,-350.916;Inherit;False;Property;_Keyword0;Disslove_Liner_Open;22;0;Create;False;0;0;0;False;0;False;0;0;0;True;;Toggle;2;Key0;Key1;Create;True;False;All;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;160;1734.173,-254.3251;Float;False;True;-1;2;UnityEditor.ShaderGraphUnlitGUI;0;13;VFX/AlphaBlend_Disslove;2992e84f91cbeb14eab234972e07ea9d;True;Forward;0;1;Forward;8;False;False;False;False;False;False;False;False;False;False;False;False;True;0;False;;True;True;2;True;_Cull;False;False;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;False;False;False;True;3;RenderPipeline=UniversalPipeline;RenderType=Transparent=RenderType;Queue=Transparent=Queue=0;True;5;True;6;d3d11;glcore;gles;gles3;metal;vulkan;0;True;True;2;5;False;;10;False;;1;1;False;;10;False;;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;True;True;True;True;0;False;;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;True;2;False;;True;3;False;;True;True;0;False;;0;False;;True;1;LightMode=UniversalForwardOnly;False;False;0;;0;0;Standard;23;Surface;1;638541998231330214;  Blend;0;0;Two Sided;0;638542005263684485;Forward Only;0;0;Cast Shadows;0;638541998250260534;  Use Shadow Threshold;0;0;Receive Shadows;0;638541998255351629;GPU Instancing;0;638541998260876861;LOD CrossFade;0;0;Built-in Fog;0;0;DOTS Instancing;0;0;Meta Pass;0;0;Extra Pre Pass;0;0;Tessellation;0;0;  Phong;0;0;  Strength;0.5,False,;0;  Type;0;0;  Tess;16,False,;0;  Min;10,False,;0;  Max;25,False,;0;  Edge Length;16,False,;0;  Max Displacement;25,False,;0;Vertex Position,InvertActionOnDeselection;1;0;0;10;False;True;False;True;False;False;True;True;True;False;False;;False;0
Node;AmplifyShaderEditor.RangedFloatNode;173;1739.614,-71.29654;Inherit;False;Property;_Cull;Cull;0;1;[Enum];Fetch;False;0;0;1;UnityEngine.Rendering.CullMode;True;0;True;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SaturateNode;127;1405.564,-51.54745;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;150;426.6743,1126.627;Inherit;False;Mask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleDivideOpNode;119;-817.3761,-75.82581;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;180;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;121;-1085.635,-76.3988;Inherit;False;Property;_Float64;MainTex_Rotator;5;0;Create;False;0;0;0;False;0;False;0;0;0;360;0;1;FLOAT;0
Node;AmplifyShaderEditor.PiNode;118;-700.4859,-71.03371;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.RotatorNode;117;-632.6344,-257.7149;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0.5,0.5;False;2;FLOAT;1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;26;-316.7654,476.9626;Inherit;False;Property;_Disslove_Axial;Disslove_Axial;27;0;Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.TextureCoordinatesNode;222;-1019.11,551.6365;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
WireConnection;59;6;15;0
WireConnection;59;16;27;0
WireConnection;59;17;28;0
WireConnection;136;1;169;0
WireConnection;143;0;144;0
WireConnection;142;0;143;0
WireConnection;29;1;106;0
WireConnection;35;0;134;0
WireConnection;35;1;156;0
WireConnection;35;2;218;0
WireConnection;35;3;151;0
WireConnection;35;4;211;0
WireConnection;140;0;141;0
WireConnection;140;2;142;0
WireConnection;169;6;140;0
WireConnection;169;16;138;0
WireConnection;169;17;139;0
WireConnection;185;0;184;0
WireConnection;184;0;180;0
WireConnection;183;0;181;0
WireConnection;181;0;182;0
WireConnection;180;0;222;0
WireConnection;180;2;183;0
WireConnection;188;0;187;0
WireConnection;174;0;12;1
WireConnection;174;1;185;0
WireConnection;174;2;26;0
WireConnection;125;0;25;0
WireConnection;125;1;124;0
WireConnection;125;2;126;0
WireConnection;23;24;188;0
WireConnection;23;22;125;0
WireConnection;23;16;176;0
WireConnection;191;0;125;0
WireConnection;191;1;192;0
WireConnection;201;24;175;0
WireConnection;201;22;191;0
WireConnection;201;16;176;0
WireConnection;152;0;23;0
WireConnection;196;0;193;0
WireConnection;196;1;197;0
WireConnection;198;0;196;0
WireConnection;193;0;201;0
WireConnection;194;0;201;0
WireConnection;204;0;210;0
WireConnection;204;1;215;0
WireConnection;204;2;133;0
WireConnection;207;0;37;0
WireConnection;207;1;209;0
WireConnection;211;0;37;4
WireConnection;133;1;29;0
WireConnection;133;0;29;1
WireConnection;187;0;175;0
WireConnection;12;1;59;0
WireConnection;215;0;60;0
WireConnection;106;0;31;0
WireConnection;106;1;107;0
WireConnection;106;2;105;0
WireConnection;123;0;122;3
WireConnection;108;0;112;0
WireConnection;108;1;111;0
WireConnection;108;2;115;0
WireConnection;112;0;122;1
WireConnection;112;1;154;0
WireConnection;111;0;112;0
WireConnection;107;0;108;0
WireConnection;107;1;109;0
WireConnection;109;0;113;0
WireConnection;109;1;110;0
WireConnection;109;2;114;0
WireConnection;216;0;217;0
WireConnection;217;0;118;0
WireConnection;116;0;117;0
WireConnection;154;0;116;0
WireConnection;153;0;117;0
WireConnection;155;0;116;1
WireConnection;113;0;122;2
WireConnection;113;1;155;0
WireConnection;110;0;113;0
WireConnection;31;6;153;0
WireConnection;31;16;33;0
WireConnection;31;17;34;0
WireConnection;218;0;60;4
WireConnection;219;0;195;0
WireConnection;134;1;29;4
WireConnection;134;0;29;1
WireConnection;200;0;36;0
WireConnection;200;1;199;0
WireConnection;36;0;210;0
WireConnection;36;1;133;0
WireConnection;36;2;60;0
WireConnection;36;3;202;0
WireConnection;202;0;219;0
WireConnection;210;1;37;0
WireConnection;210;0;221;0
WireConnection;221;0;220;0
WireConnection;220;0;207;0
WireConnection;175;1;12;1
WireConnection;175;0;174;0
WireConnection;206;1;204;0
WireConnection;206;0;200;0
WireConnection;160;2;206;0
WireConnection;160;3;127;0
WireConnection;127;0;35;0
WireConnection;150;0;136;1
WireConnection;119;0;121;0
WireConnection;118;0;119;0
WireConnection;117;0;32;0
WireConnection;117;2;216;0
ASEEND*/
//CHKSM=37C431120A13C89086D8D8FA18F0A9C36DAC6A61