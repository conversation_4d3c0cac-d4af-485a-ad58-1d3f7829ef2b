// Stylized Water 3 by Staggart Creations (http://staggart.xyz)
// COPYRIGHT PROTECTED UNDER THE UNITY ASSET STORE EULA (https://unity.com/legal/as-terms)
//    • Copying or referencing source code for the production of new asset store, or public, content is strictly prohibited!
//    • Uploading this file to a public repository will subject it to an automated DMCA takedown request.

using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEditor.Build;
using UnityEngine;
using UnityEngine.Rendering;
#if URP
using UnityEngine.Rendering.Universal;
#endif

namespace StylizedWater3
{
    public class HelpWindow : EditorWindow
    {
        //Window properties
        private static readonly int width = 600;
        private static readonly int height = 640;

        [SerializeField]
        private Changelog changelog;
        
        private bool underwaterExtensionInstalled;
        private bool dynamicEffectsInstalled;
        [NonSerialized] private string projectDetails;
        private Vector2 projectDetailsScrollPos;
        
		[MenuItem("Window/Stylized Water 3/Hub Window", false, 1001)]
        public static void ShowWindow()
        {
            ShowWindow(false);
        }

        public static void ShowWindow(bool installation)
        {
            HelpWindow editorWindow = EditorWindow.GetWindow<HelpWindow>(true, AssetInfo.ASSET_NAME, true);

            //Open somewhat in the center of the screen
            editorWindow.position = new Rect((Screen.currentResolution.width / 2f) - (width * 0.5f), (Screen.currentResolution.height / 2f) - (height * 0.5f), (width * 2), height);

            //Fixed size
            editorWindow.maxSize = new Vector2(width, height);
            editorWindow.minSize = new Vector2(width, height);

            if (installation) editorWindow.selectedSectionIndex = 1;
            
            editorWindow.Show();
        }

        [MenuItem("Window/Stylized Water 3/Forum", false, 1002)]
        public static void OpenForum()
        {
            AssetInfo.OpenForumPage();
        }

        private class Constants
        {
            public readonly GUIStyle sectionScrollView = "PreferencesSectionBox";
            public readonly GUIStyle sectionElement = "PreferencesSection";
            public readonly GUIStyle selected = "OL SelectedRow";
            public readonly GUIStyle sectionHeader = new GUIStyle(EditorStyles.largeLabel);

            public Constants()
            {
                sectionScrollView = new GUIStyle(sectionScrollView);
                sectionScrollView.overflow.bottom += 1;

                sectionHeader.fontStyle = FontStyle.Bold;
                sectionHeader.fontSize = 18;
                sectionHeader.margin.top = 10;
                sectionHeader.margin.left += 1;
                if (!EditorGUIUtility.isProSkin)
                    sectionHeader.normal.textColor = new Color(0.4f, 0.4f, 0.4f, 1.0f);
                else
                    sectionHeader.normal.textColor = new Color(0.7f, 0.7f, 0.7f, 1.0f);
            }
        }
        
        static Constants constants = null;
        private Section installationSection;
        private void OnEnable()
        {
            AssetInfo.VersionChecking.CheckForUpdate(false);
            AssetInfo.VersionChecking.CheckUnityVersion();
 
            underwaterExtensionInstalled = StylizedWaterEditor.UnderwaterRenderingInstalled();
            dynamicEffectsInstalled = StylizedWaterEditor.DynamicEffectsInstalled();
            
            Installer.Initialize();
            
            sections = new List<Section>();
            sections.Add(new Section("Home", DrawHome));
            installationSection = new Section("Installation", DrawInstallation);
            sections.Add(installationSection);
            sections.Add(new Section("Changelog", DrawChangelog));
            sections.Add(new Section("Integrations", DrawIntegrations));
            //sections.Add(new Section("Extensions", DrawExtensions));
            sections.Add(new Section("Support", DrawSupport));
            
        }

        private void OnFocus()
        {
            Installer.Initialize();
        }

        private Texture m_HeaderImg;
        private Texture HeaderImg 
        { 
            get 
            { 
                if (m_HeaderImg == null)
                    m_HeaderImg = UI.CreateIcon("iVBORw0KGgoAAAANSUhEUgAAAgAAAABACAIAAADj3VjoAAAgAElEQVR4ATS9d5AkaXrel64yy3vT3k13j5+dnZ31Z/YOe4e7A3AACEA6BCApDlSIIBQKifpDpP6RJyQGGaEgRAlSUEGKBwUJAocQgDM4i7td3N7u7czu7Oz4dtO+y/uqrMqszNTvrQn2zM62KZP5fa953ud93q/Vf/rWz+q9vjeZZGPxQjZnqNr+6ZFhaL7dr9TOFM9fLxWSkWh77LSHPUNTz8/MnF9caHbalWazPRwOx+52uTqaTN547lq50z2s1n/99Zc/2N7581t3I2HrpN1pDYZh04gbocvF7G+/8cnDavXHD588qDVL8XgmYr2wtjS0R93ROBkJ1/qD2mDkTNwc3z+39qmr1xTP++c//NHJ0NE1zVL8+UTsE+fPXVhcmCvORkzzw63H3773OBGNpqPhl1aXHpyeffP+1mmzzUVybTziM89drfT6d4/PYmErapkr2ezV5aX+2HlxdXGn2nhr90jV9NlkQlWDB+Va33HnErG8ZVgTJ2zqqqp5XhA2lKHjhgxzPHF+8Gj3v/nlL15cWbm3t7dfbz1oDy6W8jlDO2s2PnP1sjpx3tne/dM79wNFuVtpXk4nfuelawvFgq/oyYhpGaFau1FuNiqdoafprm6c9YY/PT51VfU3r53/8UnteDCMhkITd5IwzWg4Yukar2Ppuu5PuB17oiQTqatzM17g6zoXMxmOhp1+b+xNAt93Az+kaZVBz3acqBU2DNWY+MHE9/3AD9SApTOMvuP4ShAowcQwxhNvMHE1VZ14/mjiuBMe63316oVPri7+xZOdHz09CZmhiRfommqqKu+uB4GpqCFVMTV15E6Gnp9PZtZn5wrh8DDwJ4GSCRnH7Q4bpxs80U2F9Ew0ytpy/bYzOe33Gv2Br2lcWdQ02v2h69i5cMgPPM3zDZ2lVuRKVMX2g4gZGtjj85mEp6hbjc5EVVtjZ+T6ru8PHccLFNcZjUZj1VdMrsfiHzUaMkKm6XJvQZCLxbKxaOB7f/vFqz/e3jttdy/N5COq6nnucDzaqzeOuwOXGzJMV9MCVU1H45MgaA2HrqKspVLhsOWo+i9sbIR07aDZun10+PHpiaWoyVh8Ph5NRCKqZlydnXlhJp+JhJfSqYhpvF9u7bS7MUPznPHRYPzO/tNGp83mcXeeovi+N2ZTPVbeVxQ1F08qutGZTK7NzMgu8xhnvNvtH3W7bHTX9ZJmSNX1l+fmNnOZxtjbzKbj8UTUNAcjm1fBhoe2Hda0e9X6g0ZjIZGIGDp3H9b11qBftu2ori8l4svptBGJDQOWSMUIBt5kMR6bjZoPax3WytcN1jypKZ3h4HG9fuvo5EmnY4/H/fEobJoRA/Ph8gNNVVhz1+MOPE3xNbbT0HRVH7uu7fsxKzLDzavq2PPZlZBpNcbjrBm6UCg4nr+WShQilmZYD5vNxVj0XCZ1v1b/1tbO/YNDFzucOIlkkjfqsaGqGg6FeEdeZOJNfMdWvYkyNQnuF6sIKQoGqMViN+cXvrCyEIvEjJAxCVTH98yQ6bvObqPJdWL/x+32d/f2MdGQrtojpxQNp8KR1+dL8/lCLBbfzKfS0WjP9nfaLRz7YaX2UaV+2G5gFTx3OLLH7jhwJyw+l4PrLMZi6+kUBjzUzDSfJ+OOHipFrF6g1kdOOsBt3LuVcrXbi4VCqqZpmHqgJIwQO3IuEcOuuoo+0bSZRCIfi8VMkwXv+z6GXh6NjECJG1qj17l9fPrg5NR1xo5tj3TDM4xENBxS1c7I5t5dzxs6Y01RdFXLRSJ4+lm7y9bgzqVwmO9YhhYKhcaqGrUsP5ZYyWV/cXN9LpPsOUHI0Eaewq2FDdw3YE0UrDFQVEWzdF7fGY6dgYPvu+q/+GiLC+uNBrVmc//0uNFupA0tbbIrgaYEhP5UmCWIX1tZikVj0WjktFZfnZ0tprOhkFnrNM8aDUXV2QzVMONhq9vrNoaj97d3D+pNHCAcDscTvEAIx+XdX1pdbgwGpISzdvuwXFHc8e99/lO43Wmj/vzmJl7y1kcf/dHb7/5Hn//cYi63e3Lc6PXeevh4NPGvryyu5DJnrXpIwop6dWW5kMpUW429ejNsRX66c1Ab2Gvp+Gub5+r2+O0Hj2+cW700VyL8XVpa+MHde9X+8Plzq8OJctRo9IbD9WIuHok8OKt/cHD4712/NBj2ts9OPy43Ku3ep1fmP3nl4nKhiANgTNl0BkNsD+2DRusPvvujtUJ+rZDbPju7c9YgWExcx3dd3IydiEUiFduNWVZgGIqqmbo+k07NmoZpSDRwhv0g8Caa3vcVPjvBoQM/Eo9hcR22Xj407JiAwXbxdDXweXdc1Q8CXVHxNBf38LywpsbNMEEQn+Rh/NThZRXStILnRnS2wU8aZlhREpiGrnWccX08ycSsX758+bDd/L/v3FMVw/YmEnT5EE9TSRNqoEQM7Xw2k7XMtw5P+YkaBARZwgdGo2NzCg9UC7H4XDq13+knTCNmmc2xwwWMfbKIh32fy2WPuiSacTak5axQLhZVJ+5wMun7agNPD5lE53Q0kotGR2M7pnmK53JHlqGbhk7yO2p3Twf2YW/IrnVtG9tdy6YBCscd4IHj+gFhDpiCOc2mU6oeipmhhWRcV/yn1SrQoTvxbCXQWVVVIRxzhYSOkdxFkAmHMhZ+barhaC6W9FV14Pl4UZOg2e9v1WuEuYVMphSLjTy/GItzzaZhvDFf9A1Li0QjihLVVSNkZU39sDdwfeV8MlyIR7msjysNjBwAUekNdqqV/W4/YVmWpjeGA5tAObZDLJ1pxcOxXDSMd3RclisgHOiBt1erkNLwwJHnccu+qoQM2Q5ybkQymrmeSZO6HtbrjUZtvz8MQqE8exqNxy3zcj43m0xWh3a52+0Nej3PJ/SYlpULR9LcmBEilaZNcymXxSxddxI19IxlVh2P5dqtVu+cno5HdnNIjggmGJuq5ZOphGmxdBgM99MY2pVeL6RhYpjiNLUSRYATBBGFFeVSQ8SCq4XiHAEG+9QNO1A3krHycNR23dP+oBixzudyS9kcNtwjZw/7u412azwmij0FJrZbBFDeF8MjxUwt0NfYLHYQy1SVgUOC1njZBFsWjZwNRobidR2HNde4KpWoqOBXPH7sjsRHVJ2MwibzvYmPeSsEDawlGI0VopgRwtQl5YYsJRxNWSF7MKiPx4TXkeMIRmL95eYCj9QymRBai2AZRy7XsyI2Dq6HzhUKby4vnUvGSSfJePys26kPhlznVqOJzUte5AVd92QwUFzS3Jh1HAW8uZoFTuoG/5ElEoY+EwljAPl4TNFDPT/A+FkBVqE3GvVHdnUwZE+bQ7sUDhHi+yA5VbBD3wtIP4CW3thpDrBQJ64bZLhkJELaYEFmspmlVLznBSyaqRH98V6XB9fHRAjgjUq4FtNQFLfflz0P/GqrBXZUP/k//mPXHY1chxWL6Xo+Gs1Hw0lD/fIrL28sLD2tnIXD0eVSMRmND8fjaEifTCZ3d57M5PKn9Vo0HMXCQJ1H1crH+wefv/HC2PP6zngmkx85Y1/jwf6Do+Pdegvgc3GmNLCH75+UM7F4PBwmiUZCobVcht3CGnpjl6iIEYfC8b49GI9YfHfsAlj1eqv12sa5z1y52B/2yJS75VOWLJdIjSbuwJm882TvSaNVbrYzIf3nrl1eyOeenJYdX51oBGKVRJYMh7fqLYCJ744Gg2F/aL+4Mncuny53+ketHi/CaoBPwTKqO76QTV2YK/HEerfXt4fjQMWqmgO74fi+YezXGtQGmhVpOeP2aOCTTg09EtLZ3VDISkajXWesGkbcClMSEdRjGlEOS1AIPxgDi2OzdewNO6mpBLUJ7oTLm4YP7MIBBHz5quezJvjGs3yg+wHoO6LpgnklLUkotMDvAVcNKjMwB2oCK2R0xy4bvBQxf/HK5WIiqWsK8eUb9+5Vu/2wFdtvkX0wIPeoWu1OHN6P3EbWwS5BYRKKnHHYDBcTMc+feJ7HbUkpAAQMgAKqRnzCuzAmIoGv2Ip2ZW7m9bWV9sjVLTMfiTieFzdUjG8tlzT9yXhsR80QsfW7d+9Uh+Mze3zct1nYUiT0Oy89f2VxGXOkyMBriI2Y0MOjw6//9N3yOOi6kzYLrwRLydjHtU4pl8ul0kDQK7Oz5NdYOLwYMakqDludWVNZSIa/+XjvZ6e1VCxme54poQE8yBIqLEh7NIqZFnn3fC4zGwGlTkw1qPZ7O7V6Z2h7ukFcxrBnMtnzmSwlDzuiBD4IZjYRJSJkwpFYLEoAlYzIBhNiKHEmbq1R36qcnjQavj+hQKnZo0hIXLoUjRQSuGTMIrLpoXw4DB7ss7yKIL6+OwGxUlG9f3L27v5+MWrhrl175HsuAQIsPHJdLoA9YjfZF5yH3IABX88Vrs3P65ZV7vaoV7jCjusV4vHrMzPk1Nl4tNrr1+0hDky18e7JmeM6rPNpr9fu98Osh6aDT32MSrbVGVLaElAmngG2VtVMLEauTVrh1+dnRqpe7g8GY7tm2+z6wJGygPhMTJaIIWWNlkslP7Uw/2KpMJtJx8OUHCGPskjxI5pWtceEMIq22Vio2uz8P/cfEyJYOnzWDry4ac0kE4uJOPtbH43APQRoYA3mRLrlSjwAEihJUfsjp5BMXJubp3I+bLXwUIyTsrFugzQmxHPcgRUj7hOyyRNYqKVLjetNME7+sPiEbIM4i20TCKnAxIwpyfhCUBN34okP+ipljElk1kLgEr7PahEoga0s74VMJhcJf29v7ztPtqlasCtqLzEJyyL7zqUzGdlpKxWNJqwwIZVVHQ4HHxwd3Tk8pjYlkw0nrj1x89NoQLbWCAWOE8bdwQSphKeFspbFvsdDBtmXxYlrasd1a45U0mvpFK/fV7R4SJ+LxbSQCaSgDMML2VPukgjCIhBkCGxcObfaGTusP7mHrQpYNM87GY6gLgISYRAkiA+Ou9vpDGybTJIwQ0AEwzTVL/z+P5IUjOnrmmmEkoZxc6n06oWNVy5cJOZQIpEk+8NBqwsJNKYinckXq416q9d7/8kW9AjRYKVUwJUb3eFJs13p9M7NFi4uLZsaRqNsrqyPBr0//dntrdbwjUsbPWdy2LMjFojEyEGvCJoIwiFdIYSNhvFYlFUA2RE1eN/ucJhOpgqZHPbx5OkuRU0ylkiEccPQca2xVz7bKddIYviVo1AK6XHDcJyRrE3IdJTglaX59ogwPQaUvbi6VG4291rtTCzxsNE+6/bMICCNpxJJMuFKKqFFE1LGeQ6RCxTJmla6PVI6LzY1K6UDR4HFhEySPMuJlSctKx2OgAczLKMVSYTMs157u9chVFKzmewhsJEKnW3ipUkCQlEID4AFcnmwGQA/LJVv8QO+jTnzGY97FvqxRT1QdVUwrKEEMfgKQytYhBXsL56KRNbyhaNm/WdPD3ivUixKXvn+08PVTCZj6q+vLHcGg61yBQbmsNOv9UckV1Alb4oRrJVKL21uxsKRgT0AJtUofzEAIMzQphZ1HGe9kLu2OP/4+Pi7D59wZcQArow6D/+A8MFXuU4pVyRz+iwIZTmJEHywkYqEvcnf+eynlmcpfqgc/L5tY7RUu49OTqsjDzcut9tL8VgxFrHHo8252U9de84e9Zs9m0eyp/dPyo9q3crYwRPwg6V0+sW1tRJoo9XEKnDaw1bb8kblVpMSPhqJUNeTh0qJRMMe14d2NPBIaNQH5AD4sYimsnRYiA1v5FJSey13AhO1kErFwhhbcimTnstkwUyuOz7s9hqjUSlszsdi1X5/OPEilpUUBsYiCBKqKt0uCRhuZ69W26tW4iHzXKlEpPAhDqDmhC5TJCGbYUnqZH1VrQ4Gh/UG2RAKhVLJ8D3ccuh5MUtK+LHjaFYYyHnU7UCD4KIEsGlpFgBr0+HwSiYbDScW4SI0hf2lVGABw2qwXav/cH+/3Ww4/b4SiUA1dEdOhKrQdeswRVhdoPQg1oS9C4Yu2MYvRCKA9d5kAipMhiMXc7kFEmssvmaF9tud243mYYe04p9PJ6k89vrDg053LhLmkgBARJmBHywkkyvJJPUHZMTjdq/e7QD+wmy9LkQEIZxrjlMAqUF30Pvu1s5OoyEEDj8V85+CfUAfiwBsClFBRlgxnJlI3B0T5KmkFSpKtvJGIbeSy0FHJyJhsQJ5rr9dr5f7w/po3B3ZtV6/ORywFeBjyzAL6fR8PPZCLjkkeyhKbcRWdqH1akMbjhpsbmGIgT+iUmGJJh75MGSFlzKZ1xYXr83NUSUNXY9CjescjIbYNxX/2/sH5UEf+xmA6FlJ34eWIdwRZBWudTgEDCVJnOFwIpEYa3o8mbyQz62k0wS9nVp1r9PpDu3OcIjV+YRkWF/Tujk3cyWbxo6p/ADmeD+MaJjURcJWtGIintA1QP0MMCJiOYr+cbNFAo6FjHPJFBcsgA/jcd3GaAx8bvYHVQdj1rEoMil7mozGYLmTIYP8j7+6gELDYIMEVxJwfI90ASYj2VshkwJKai0Czm/8/v+SikZYug4IQjXypv7lK+u//IlPCWHdKMP21jqNs1r9xcvXM6lMb9CttJrZVO7xyenjw/3Nubl8IrGxuISrUA+BKH9w587Xf3YbRi9KGg2FYOeFF1a0eqfTg9YK1JhpFHD+4fAf/OoXf3L/4b9+98Oa7fz8pbXfev2VjaWVezuP33tw/+Urz11YXNo7OSQsXVrbgOT84x98T9eVX3z9jafl8sixV0ulP37n3bf2jvsK+RPLUQ6EHVOAxuJpgU+h/drK3Fwy8ahc/ZXLm+dKs3/95PHflOsnA/ulAlyl8e3jMnEZqMW/U5pbPI9AHdM1sAymSaALiNUSkxWPSln+VSilyLv8AbBLgcrPFMG8Pd8DyMG0wdKysBgBsVGwF1GTl+G1JPCzC/J44jywB4skHWAEEvb5T96Jd5CHyK4Au7gMkjIvJcyGMhOxrqTjX1hbuTwzV+/3iWWH9Vql3ZpJp02N9w2/v/WEvPve9t7bj3ex9IiuEVMohLF7NhebG5LXuJZQ6NriQjYapT5LREPFeDRtme892W2DyHzvS5tL947P3t8/efPSxk6l9mGlHsZcxMlh850+XhoE7OPIo37HfAjx/MHCseAgZhimEszEYyldSJiZVAK/HYwdcNJg7BbT6Z+7euXRycn37z9icYDG6UiYi5xJJ+fTyeWZWUqjSCRBDvja+x/1tFA6kXCtyEoycQpmGfbFtihfuAAjBPAMhSWcAXWLIcpanSIrGw5fhFg3uVzCi/Cy4AsoLlfWkqDt0iRgjUl1fBOjDIcMvkcJ3xmPY6rSHPa32z2KSKLsBGgWjQBtYoZ+0usetdv4Ehhlu16l/F3M5EK68cbS3FdfufHd7X3D82AF39neu396dn22sJjJVDvtOyeVOjFDDy2kU/NJ2DgzDtM13WCul4UDirNiiXDY1yjgPNhwsjj5FXqdIpvL4KZ4hA6PPAnIhQNgB5HdlPsiw2EloCKBt+TvHozBkG2gBMG2Gs6kSFzW9ZM+5L9UJ9gaJERG03kRnAdicSUeA/QY1Dpjl65SxtBB5Ye9/tmg33FcwhzLfjGfT8UTLC7GSRMCSo2SbqfZOWp39svl/X7/oNUCWsLFk5NIERFaBKqGjbljm+eAI62wCYAZw3tLaatwg7gV1STXTQ7mfQmBF2bnQdMJQ+MpNPPmE3HFCM1GI9hGZTCQakTRKp22Qw6TJcOptJN267DTrcNfCZ7CnCFZta49hP+MmwYxFJgAv8qbwnCtJlOvLszlEknSQM9z5Rp8b6taOxm5lzMp4qPAGd2gy7CRSviGiT82ez0K+lI81oUu8D0CKy4JzCeLQosRkWGBeqNxyxOykT+9QOkMej/c2sUszs8TD1NXi+lELA7pScFChAk8mlZjcBh5hVqhN6UB8lHZI3gekq5rmBB3Nhvt0PDToHmThGidOkduERAPeQtXSdxZTCSx4REFZYiQAJuvS7IXS9CGoBx3krRM+gEYiYBOAOuUYcNceBahEdPeb7aOep0eHgXcG0l+Un/w3k9mS/O4/dtbu6wFdrmWTswlIpV2k8LnVz/x6ZlcjsWnHDuunNzf3wfmd4ejRr9fTER/4cUXVucX273eh7u7neGoTEGrhfCfeNg8bLZ2z2qqPynGwtCUv/byzUzE/ODJ42989PDv/eLPY+V/+v6HXWdS6YMQBSMkTXMxk6SxuV7If/Xzn9s+PhKw5Xv1Hj1k88HBISgjFYse1Bowx5eXlhqD0fe39xqTQGyf26UkJHwSeiG5eUXKMccBl+Dwc/EoqWcA40F3ZTz+8rkVQhjJIBY2JeJilIqSJZr4wC5f2p4sgUTiaagG40omkDgt9ABYhLAs4FwH/8o7Th9AdULUl7QBxgYbT2tJgT1cGBY2dYDp89lRAc+kBP7PkwXv8QmPEgqSgkj+xzOefcm7EcTxGzqqBV1Nus6r8zOf3tyo93qDfv/9xw8vzs+Fo4nTdo+m92m3F47G4W0fnFWhDrllXh641xq7oDaMAOJbWkvy+vKmlC9A4GwyRYmDHcAFA+UgfSJWmNgKprAgUgOVIAUjBN4hth61mvgG7XfcmIhM9DyoVbgFbG48HnFfAMCJ4/DEhGViM9wHCzOgkAo016MmpfBWI5YJVOF1fJrGgU8hhbnPJlMDj2Ck5tLZcCyRjSeIGTb15cSpww/yfFZGaiqFPCTgC4GBqhQjUTMcsYOgM3J5I4mJqo8bNLuUmi6Im0CDRXBhbXsAGCQEDwS7BRA14WmkEPoiZHxwfMLW43i0ZF9enE0I4gzyieQ//sFbLZqWGp3Pya9srjxotJ92h3QXVchSTZUmB4wBlf7Q/ruffmUmX0hYRjYSubX15Dv3H2+1B9wCm42vriSiF4qFxXyGpQAVwluS3qmQGv3h00Zr4HkDT20JeyuL/KjVIexKyahCOMBiQGrTw4a8UqIhEw/PJhNayKKEB1oBnyGceZe9VrfT62JUHumBv+AZcLvkEWM+k/7UwlwsHDdAf6YJLBuPx82R0Ck4NSQnUS8O/cVzWWfeTixHi9OoUDzVc989PH7/6Hg0Gux3unvNFgYm6ZNEGzLgOWEalxKpHlwOtaAXwLNvZDJzqdRmOkoTmwoaGA4DpEycw85gq9sPBR6xvBgO1+whbeKC8GZhoAOJ8GIxT7MQK210u0f1Rrvbpihs0asYDIlweBJhVFqThgGTyIWKg2JFlOMTjx2T6+cL/gPoCAnuZeKpTywvUcfopgmw30wnQSdruSw9gJ1qszHs77R62gRzDbgbCaKY/RgWjaerbR+0p8LpUZvwneVsOkxEhtHVQ4SUpKE5o+G3t/c+PjmZBG4+nowp6nG3fWTTdQr4vJRMSMMUil9VqTZB94VYItBMglLRNBoTrxi2sE/LipDtoIUbYztiRjKmJVqCifegCets4xr4Wj9Qcoa+kIhbEZIKGVQxiKrj8VajVbcHR/2hpE9VYxEwGrg18grNEuAOzkhcxUSzIb3JfeEFwyEt0lPoHNumKUtmw3fUmpR7w+NGg7/tQf+s0bSHfXUynk/KdW/Mzcxm05QM2NlZo/b4uNwZT9rOJBqJ5RNQvkExlcykcx1avIYJHxqS1osDx2p4k89sLL37ZOcH9x8VwqG/9coLMCof7Oy2hmNiTSIWRRnSRFMSED7MsTMhbtLdxazhoOnItXpoItDLCABhByjqMVicn9qZcE+6g8Blabgr2BjsD+TiU4DTBSISowSAKhMTkdQgSXgKV2mK0gprU5H5CqYmHUIWSLC6PBITE05aAztLJY4tyepJKSC5gDBHoOab8h35/pTh4snTb0g4l9DPX0IKLyIVgORceQDhfppBJPaSO4DN3Ku4Ha82fXF5SUIzfguWh3AjfclPIMdUxQOy2eOkO54JhRZi4c1s8ks3b9DNArDfPzlrDse1dqtj2wOPq9UROIEW8XlMSi6VW0DvIa0vsIJByOayLmVS8HhmyCqlYhSPPu+pqbgieQHlkdBbNJyxDOkrGtgW8pIHB/uA13wszkqy7AQesDsKjDL6HA+z09OxBJEFmgIAUZsW3dh9p9+VLdAMaBA0S5lIFPoenRhkPR1OoBBFAPcLQQ++g0SCO2etiClYGnwuKwBYkzXkHRUf/IXYxnaQagit96TdfXVxkQQG/oJIfb6YASE+rNbePzqBBnxzbfVx375/dhbWBWvjDtAsUfhuVfnU6jzdnVq3u5hJcW2UqhC175zWaSpQ70c87++8epO9+96T3WQiSe+KeDRlvciXZn8EJ+5CLkGhsrXke9IY3Bcud3G2REqAdafLmo4nMIKDdl/SEUsysb9weeOFzYvxSJTGGGhp9+SwmM4szszVWs0WuUpRSRhY6p2zWikWqTvevXr7UjbB9096Q1IpioxSxCxQixHYxa4IgiaWczgYn9g2NBGZj2BNFICuAaS37MFpf3g2GGBp5AbqM3RRKnk3Hl+IxSNWhBIJmRa43ppaIMwAngMggFfkdljx/mi4RWibjNkSHr/d6d4tlyHZ6GNbuklCwgGITfRdMbuL6TSeRdT4qNG6lEqupaFG9Auzc2AL4c11nQ4PaHY4mnDvWUN5+7jy06PjxRjrrT+o12Fuz+ez/fEYjRYkuO6OjMmIZAzyIBE24Ae9wPaVUtRay+UOWu3jTq8R6MA+cJPtCnmE7+JMkumnFXM6EoWZWchkSZYf1RpgIHjjeChU7XaShk7vlJIBxh+CEasLKWgHpPcDLXlzftYPWfTPcZaLmSgyp4GnnEKfkqGdieWO7h4e/XBvr93uDCgmoQmoPkJGNpbACwrRKPuDr63C5JOuVcl8gTOuDPo7RFTpwxsUZtQHL87OnMvnMEjIya4ftG0bMod1QAUAjUzpSRpACJG1QlQM9cGg7LqLyRS0ZM/3qWNyYTOFIVHwWRa029lwSIjPIB7TNL4DjkGYud3ufFSHLbOhBOn2Q8vuqwsAACAASURBVFTg6SNfHaGbIC2FozeKhflEIhuLSbPoH37rh+4Isso+bNTrvQE6PCOgG6zNpxLrxczG3Oz6wgqNbzjlWqf7wcExWhd8nuYG9Oh6LgvQgHslXx2eHl+dzb95ZfMPvvUDVJX5WKSUiA2gXpWg2Rm8tLb42sULJJKtk9PtszLtrMNWv413EjNVJIbIZqbxlwvyPUqXfCaDtKYFxUn3RlEhzVly2nTENcGwmCylG70b00pZYSA8rWg8s9zrIsai6CWMAjd5BP07FAK8OFmHp+DGyOAQMsLkiSyJDpLgbUI6YRDT5yHCBFlYLt+d8vJsjOQPcsDU+57VBfzLl9Nv4AtSefAPX4K9pl8Lb8NL8RXobRrN+SlIVNSZ9J7wB4G008aUNLjs8cvFLMDz/mnlpdkSwX0aT7WfndTyprEai8Jofen6ldc3N24dHhO0YQOrXXoyLi6cjidZBO6Xwohe3HA06va7NLpt11nOZBKYEVbjB2PDQk2xnIjXHRjnHt3p/Xr93uHhSjJ+OZOUvBX4LV9w6GRsdwb9nqfQ4IIih5MtpZK0OWg1U7ZTIQxJZyw+oqBoiAZ4Y+jBJHB38j6u87c2FuhU3js+AUM8LlfLvQHviIeQBTNhfDjTnlIWWCR9SOAnSYUnkuMRWdLdGQHJjRBACY0ar4CihjyEtImM4puIBigsaJxrQBi6aj1nTDGOcHY1GgYSQHC3SKOe++nF2dpoQuKB1ZL0P20SxjQtrvi/cnHt937+c90RQrUt2H+gwX/99W+okahnQqGrIXQCE79Ago1GD1odDIxWZ4ZIhCbSFS0yn+N77DVqmWZvqExom5u8Tto05lIJIJEzljZ/udXp2aPeyNb8yZWFmecWF7DnUq4Ei3Jn61EmkTq/cq7SamwfH2cTScz7W3fvA6hvn9XJN+lI5FG7hzqZ/IE8k9p0NUVDMr6cTRK8uc12v/f45Gy32e14gaMajSnWp2cYjcbXMqm5ePgb2wcsODZ/iB1MfLrffHk6gIIYdnu9zrBPeahAdCFSIBqQg+EYSSrAJpI7FkyjgljjoI80IgGtacczTboFlGvxaHQ2ngIkkMkqozGoAbVGu9shA7EOEG0w7sG0L4iaA4wFEMJO0LZGdJ3cAX1XGwyocnBMcMdcIgHuQTMCan+umKv0h+Dy9Zj593/hTYiNb9/6GXvXcbzFdPK0b/9ga++/fPNTLNERvTk/2O72B86IgqMYT4JXUK9QZlpYQiaVtCIghqNW6ylazxbSo1GI+sCjHAxhCQAM/HQqnAONBaRqrgo9OnCJTgB+LO7ueysRK49qAGdVlJ12m2iSjsYQNV3PZ/DojxstUDY2zPeBAmgNkO48K9HCQFJykWYkrdBiMrlZKl0plTJxQUiUU2Qj+KwoZRaMnOgsYHLAE/DYoab0NrrU8QfNBpAOpNtw3IVk6pVSbuRjb66lBQjVSLvP9AJ4HPGTlvJMLEr5hQoOeIAoAAcCKNA4wUmJMGMhw9htD5uv2EPycQ5dDMkjJOwThY/6q//zP+L51Mx4CzLhDJ0dM7ySzyJ6u7qy+JnnAUTawdnB/b2nO7XmYd+lsmONoNWiYW5psletHZbP3lhfAnm9eG7tuNV96/EWWRS8C3dLKfS5a5fOmu3X1pcfHJ9dWZr/7LVr/9cPf/z19+/q0ZhonHSd0AysE3QNJiZSTKVgwsgTQIkzmCagRxKAIFNCNU8ROlFRCIKI4pCkdZGTEyYMgwQo0XgKoSWu80zRLkuUJ7QTlUkVAvgxc0HuErYl9Ms2yM/5HpmG2py3lN6tqCRhjamEBYnyGEkGPJDvCraS8oL/y1PlvWB+5CcUJcR24jtkCB8m7UFpmAaieRc9pU7B++ZskVRG57Y6GPG6SAsppKacnS+9QZZFDYAMaihM8S+Nr4mnOiNKP8dTuqi4PAepgtAEsPO0h+g5I+dxHansqBsMven4yZAKlNDhHB0XK9EjUXTf6tjet10asC/OFVv2CLENUkiyP3ibS28DtVwXxoDNRUQwJVt8RP3o2JBCIxehNicsUrfBywNOkZPiu46GBJBAPuIVeMcr+fQvPHfl+eUlAsnD46O3tvcPesPtVpe8mzRCv/vq8+dzKRQ46AUfVBq3TivoBW8uzj1odGrS2EB4idphQgMpGrIylg7NmtbhoC3aOZAcf3b7zk6nv14svDBf/NqdRySE+SwR0kDdC5PO82FgYBqfNtvoSikZkYUIThdRUMBdLCViuMLV2RxgbbfWIMfAWuDccPzkSDYdXXUhnTyXzQBoHjVaYm7kQ1fSG8Qf9AMgQ/NQXBArDaoq6B1cNxLSGAtIhUML2czm0gqS1L3Tk1QcZax/WK8jYZrLZsuNWiGVWiyKFvGwcrpfrSMhRwlT7yO5IX+qVdtlEsKwhFZCB3HQ6Ri+zzaReYKx/aVL5z7/wk20BlMUMam1WgfV2q3dvZ8dlU9tp0o8hvXGyibuly+e2+0OHzY6G4UcvU7QEuWeRqdUxC46D55yOJinctZpP67V6AQg5aFWoGdHjwcPArCIfWs6IHQuap0NhrCjhCpAQHPsQj6yyBRzlHokbmIo6yZYRkyIt8AKMN4AhOsIYBA6VEhR/seHVCr8MCCd3Jgt0SJGAEKSQiaKypP1pCL8zVeuQxNdW10NZJqFbrn5nVu3//LBTnvi0/5F54xpwOdQiZKn2anZBI4j4wiEEccV5cjd49PPr83/9dbu7eMzVh67dAQ0iquSsbAHrojFIPNRs/KOfDnwJ9jPWFr00wDiUwoLqQUQAJfgknQ6yRkdiDDXhXanNw6zSkcKoRQJgAYPMAW8SJSHspB7x2Ysyg6jycwKGo1oeLNAGz7KqhYixGdp9sYso2uPaYADrcjrlIvIcuKQPCL1MdCVYb2wICg1y6S8qYJAQp/vtUakWfTfCn4HA0k0z1jhrKhjDOycyAhpSWDnD1BpOgEgn7MzVK4EQBiytj0EHrFRYC718//9Pyyl0wuZJI6H3vra4hwh7LhSASx/+fXXzs0Ua+3OX7737sjTthtdEAoaBrRfUUNFZHZQrRdi4Uw0AqRNQKlPvGa/B48cjcYy8dhWtYUwkVmwl1YWPnP+3Nd/9gEYMMkkQW8AucyPpqwJTxIz4R8uiNXkD1WaxFa6PmQAOouAaGSLPu/LcImJGol7yMSjv/v6za/fffhBtclbo+pt2k4Wgjmkj2j8If+gPA+hlpxG6KldCnIjXD+rIQTxy6JAaBKb8AoGVJD20xFFotz2JraQKhLU8QdMnEvlqeBQVlJM2UcuptNU4NUkM/AjySkoOIMUiy7zSm5yKtZcTMRmopS2dL+BlVEKW/gTOErcBZ8BceEk8NUQU7wMrAhcHdfVIC67TlTXICvY/pSm9of91hBDpRXK9JdapONHdvRdmsOEtlk4NQxOWGkQjVnMpB6elCETV7PJoT3cbzSR1hGIG2PnxtwMBLpphj6xvo7O7KTfe+e4AR+DtcE7LmTS8MZAkmyUIiGwPBvePB+Pou4hBBAWbz++jxYW2dW9s9r9apMt4UaIGsJ1UIQi9reMhWTsN168sTgzSyvirG/vnFZ2Wm0YCWeEuHdUTKVeWJgPRWJczFouDQYcjqSLmI1Hs1Gr3hPtBOxNSgsYyms26zdXly8vLf/47t3jVuvOcaU2nTsaA359rzceQWWwGSBWohvqdZDgTCJ62JZ5hC+cW3zCVAttLsZ5mE7KpiBej9o9NkPciYE46gORGpmw0pQ3bK0AQ7TH4BCGCagv2W5DT4WjdA5AxIQM8ko2ScHkG8EkruP2lO3aSj4jsm4luLi8WsoVDsonqVgSsCztZN3YPj78aG8Py2XjkEFHwuGbmxdRW/3owfZZf4grncLMEnjANxRqhF/+upOb80XqejgZ9MoL6ST3iNhtsVCkG4GnLJVmudq/vP3Btx/ukMOIqyfAZyYudZ0mMPgGADgkdEzhlLBpTJAIFRPBneaT6ZVMhtqdMYvTZuvHu/u/89KlO0+P/uTD++lEXLcifd8jFGD5vBfOCGlOP0DKWEQyXKbr9MZjBi/Q71KrYfYAoCl6oj4X0YT4Cd8iH4iQQT6d4qVnj5EwLD5OmkGjomt5M7QYCxcZ1IiEqBi4KSY3CX+XFmbRbW3MzGwur0Ld/Hd//lfdqfIYdSyaLrqgXB7LRegkLPCa0HT8AetC5YcUFSHNJxdnf1qp0zVH99yfOBL3eS6YAPLK0MHJdMzj/CWlkyF8BRBAEwahJbGVuyAIcel4KJdPPCSgSykpaUx2iOuEXmeZuZLNfOHXL1x8ZWEG0ctIhnawIW8w9pqOG6Mit4ffPDj68Ox03O+T8EaDPskeuiqRiJdSaUydpCsrwgikFbmUlVJyfyRa4TkrTF1y2Ol1pP1PbSJW2+h0fIIATkcipOSi1KBzaY8JlzwFPicWCRF/oD6YNoBylDhDapYdklQnH1y6P/nw+ORxrQqYoOZT//Cb3yCMwR6etDpHjVYypF9bmk/Hovl0+sHTp9QDTK8cdYapaGw5l/zRk71al7LMG7jeUjr5y9ev/PWDJ9V265dvPh/RlYVchuDPqv3Zu++Bht7fP3pUa/PuULdk5vlMChqFpWSnWVz2j4UGRRD5posJEQmyR0GLJt0l0kGfc7kw9kjhUrr22sbamxc3/oc///bD1lC20Ax9cmPl46MzmkuzxTxEEPcKj0wPDX6Zmh0u9KDVxMmleJhiGqkeeGNMlP+zEFPEEkE1JVwz7QELIo9mGSnbMgUNsXAkatoStLyQuVCWksQxJAARKmNal9PHyOvwmnQO+AzRJ6JvykNRQMXjbIu8CsvOFhBQCTyYPkiN9kbgkklIJwwHgAsAkjg2BTobg2ZmMZWMgRfEd5hlGaGlIdIRIwhU4CWUePRUYVeyhvrF1dkry0tw+ugUKSGz8ST1JtdDCYyIBR4WAwAe/fjJLhoSJFh7Z5WlbAoxLha1EI0gYvva+3f6rgcy5YLpu3IN8wgylMn1pYWbG5uQy3IZmsFliAFpRrvbenK4/537T2YLxSfVJsw1mAWehd2ks4rEag4r5IKYpPF8pHvhRJL7rMJ3D8ckHoChlOTAQ0XJR8KgLuTVIJ25dOL8TKE/CRi2+vHOAcEdAoobXs5mQWEiWoCLYPTU1+qO0/FctIdUkNLYIEpP0zOBg9WlrySE73j8D157/rl85l88ePKoOxD+TuIgLVmwJhyW+PbUAKdl4rNmktgjIFGCllBwU2E+d4Srb5aKF0ozQJl6R+ZIIUOoBkzFf3llgXKEee9kDL47QNZ5Uj/bPyvT1povzrBqiKdzqSyxstruxKJQONZHu9tkfIRMZI3TVvtff3BfC0d7jPbhGlNbJXQyIUzf+IsXzqF/fffRI9Lta+eWmSljrSBFUWFgXdXeoDMhbPnlXu83blydSaW2yuV/dfdJjaYR64luVTZkWuBSYYsL4ONIuVxsCThFlz+TTIAG6BZdyKVe21iHgt+rVN8/LjNZ6wj0EafhFqDUqYmnjGVA5UTvhALVmM4I0xXH5DBRHED+4hvylhJS5Wv+TwCVlee7QqHyNeQEzgBFQxUFHZ/WlLWE9Us3rlxbWUXaW+vQznfr/eGDkzIvgZGQETOJ5H5nwCgciiDhAFhnfEheWPAZbyTfE/ZVxRiIHG8ul0iH51Lx+80OytTBxGMBEUd+Yn5mrJDOrWwkygOjmvbqQmHijKBleMeT0fhvjs5OGPRjnSkmqACmi4bJSdSXG5QMJ5FWtMVS1RATAApkVsRF8D85FXW4TEoTrJeLpfVMBjwR17RSMskwMI0u8DUzYzR4maCkjADfQCshyuROcQEM/mGrW7fteYaBpyNEjKgNfcqCUDEs+i/+471I4TyFB8iuwsW5HnUJJQ6TZ9uVyl8fHXZbbebQKA6IMiQIoDk3EObySc9UAJpO9Ua1BOaDKqDNon7//XdZ+u1a897RWSqk/r1f+tLl9fP1bpd+1+Pjk0en1VQsQg6sNpv5ZOKk0+fciMHEB5DOxiOvnVv5zkf36Db86ss3OTHi0cH+crF40mz95OHWcbcvNi3qSaaxrClwll2ij0Sd8anVOeL7hyd1M5rgYZBoCLpBuIjEJe/inQAObhIGZuIxbH1jrvjZi5vxWOLR0QkU8xn0gzOmFLI4vgIWiE53VMq0kGVydVGZr1KQmd8tV1kySha6vsAxQB8oRjpgZnjsOYdMa9K9scKMa+eSycV0lvoIHoyS2URIjueD/iI0upX2kFMXYBKBLhIdCOL8i+nhGCIvRIcgSUaFkeDCBQ4L9hFLEZgwzTQ0JJDJEsLI+a3RKKFDoDM5FaBmo4sVQ65DswvIEGECJZGMJ+kuUvh3+9DQwDsqeAQ+SQpn1oQrZEfBaLx7EuxPXNGUlKkB0vEf0D3Xc1o7o8V3DP+QTM3lC8REMHK93bl+bv297Z0fPd5KJ9PL6QRqKMOKsThojfuc7dFmTBoxDorsYC4aeu3CBm1Sxr+5EVpE+BESHfYOFv6njx4vFmcXCvn/6f/7Dp4JDgKSsyS4PGCEchjqRuKVrxx0e//xazduHZ7+eOeQ6W5Zf5/VEplQypSh7nzUEvUeim9F2W/3n/ZtKoMJLgY36HskkhjSoLBBPbpZKoDxb5WrdUgYaY3EHvWQz5BuGQ6afsgGEWVwEuhW58VinjGID2vNZJiAI+13LkocF5eQ8MZeETv4icBJuV44PUkAUoEWkY4IklIsEpvrrGWyy9kUqLXTbfnOCKaUeSiMBwHozfVNGEfqg1JuxrbtJ4d7hIBSNgdy+tFHHxVSqPkjH+7svHH1KlPQCKkZcGFCOBGJIYz+/r37tmq+u3/cJgxPdfTA+7VElHbEXCL6lZvPPTg62as1Spn0/aNj2FIYxZdXFz537Wqt2zuq1x4enyH+ORqMWs6EZd/MpR81uqe2LTdATatpuK3If6fFJegawxAxwDRA88/IATN4jEkj3Mib1n/7S5+/c3zybz74eIxeAMzL4oAvmQLhQSwNZiyELBPpQpST3QFMSCoOen36hbNhEYBUGQkUrMBassTTP/wjYVQ+5zO2iTSAQ8lf0JWhvzKT2cil/oM33gBaUNnC8FB7/OnbP/qrhzuBbpHDeGteGU4DeAvFhzcQOmnqE4J4SbaRdRPLk/Q5WYpHNxIxguwT2s0jGzuELGLwjbXA6yOGebFURHVDhw990RfPLaEDRXn53PwM8bQ2GH5v9/Ctk6p0jA1dsDnlhaZjgbwF/bBlOZSD0X0RyxGRSAwylerCTPhwiQ/qDbvXpSikHLPCUQbmL+QyhCaacIQgTAIrhWUC5NHHgqEgwSDlocKjb9y0GdfihBUqWhnmAH3C/8jkCcwbkq0Rek0Zz4alQO9D9ODaKmN6MAjUgvmwxTB2c+KDpXJIYIKgLWWfctbvo56qjuyUFZmLRWHtWpxC0e0+qVUdexxYrG2QMg3YLfXtex8z1vvtB0+O2sMXVhfpSrkByhmnN3JoqvjD3huby7QeyvVqq13/YO/gmw9214tZEDro79eev/zm89foeNzffsxhCYuz8we12mwmu1VrxhggGE1EV4DOj4KHnkavzwtSKKxEdEoMFvHu0elf3H3ERd8tVyD0QZFgOvA4NipsO/GDGMegsRBfk5BAci2NPDls8VM5DIAePjo8GkqMXPE1Q3FBsJRKouHtBTp621w0xk1idQhXwCO8Ed4tzWGKfT6fkvgELQkBUtgJjcCP5D9QBoHQcT46OjxsNrTAQ5h4LZ9DXwC1J4cvALLwH89lfgR3k+gnH7yYVFk4G+/KrEqTeSJSM0Nt9pDSCiTS6VOGQsrzBIxSRH5RTSkmEvOZ7HyhUBuOCV1hpt7H4+cW5jAamCiOlIB8IwCDvBAt0kujgmGJQDv9QT8YD+lZhRX35hIYOlVu1o6r5dXZuVK2iP/f39t9dHTEc+cKJWph7EyPpkxm2S0LnQ+ezJyfN+xGFO/y/AwgnTMrFgqFJ8dHa3Pza3NLRKvt/R3WirtanVuYLcxIGhvZvOZPt3a4nYe17lhVWpMJOEtANUFDaDyJtBKNp1/QtGNhyaZcD65oagaqMhTQCT1Yz6U2ivnVmZl8KkNQ+MnW429tH9KIrtL2AKRN5xtB9wtR6xNzhX//xRd3a7V/eeejB51h16fJYbRoU3BtsrPP3pF3lziPdRAQkfwiEkyJL0mtLJf0bJvkIcLb8egpkJyagnB8fILSAE4vQDgBk4A3Mrs/HwmnaahYEP0RDhoCQAHH6AwBm7DRz157vkAzmTIuYACqi1YCiQS4+Nzcwndu3SpBtszN7ZcrHELFu379p+/AG3z6yhWupQcP2x+g4v1/b3+MZh80IORD4NOoSKj+V24+/+r6Gnrf+sA+7fXf2T/pjyefW5+/UMxvzhZ5Opze1997/6eHZxVPkRljz28yvivtXM6BAH/oZB7uCbBAMoYCoj+EUTHly2NBhaA/al/ZFyZReY68oA1+YjSUhpMDemR9JDGymKyXrN6/Wz8J55gEwWyKhwH0Avq5cimmBCvLB3ci9iqQj5eQV+E/dkGWF/oUfTNu63m/98IlKsvVfA4nOWs16XwSmBhs3mn2OXHEYYNkQzV2WUAuQZGrhwoG2PKOZEQYmykm4/1o4Raj5q+cW4Ja3mu2f25z9WjofmvvuBA2aZAcIadHqcyE0GRCmKQRCghDgAT0gQd34Q8dD4UCUzUsHtBkjIXoZh9ydjJ5bXaWobkGWHuImFwwNbUaa5ULiywLjgG17mLcGo6HH5w16Oe9Usz2xsP/89ZHF1LxL59fY7nOFYvcBTbpIBCy7bOefbdce+esttvHeX3K/dVUgv4tJwXtdboHts3kCFqA5WwuHo4wnIhghd3soS2S0od2lFDHihy1oBFkGiKIGHPcGWK8tKGmmN1D0wXLounNaSsDKvjM5v7t8mAUxVcIbLSjHVR8InpV/9dv/yASiby0urBXb3PsGuBiJkyDS3lxaQ4WCfhg2/1SJt9C1cfhFcMBFomnPK53mT1/8/ImM1n7tRqPvFtuwP2zf1gN7o4lJC2YDYMOJJzH00rljeXSO1u73/jww1cXSLna2tzcw6Oz72/tRqLxtAhJOXNNn08mc0lqAkRg4mwES5QVQG/hMSQ9YFNsItMm0k8nAKcgjMgW03JBsBoVoMB8ULOQceIX0psS1xazpdQSlomwgGQUOINtyk+wZcTgpNlGv4flc6oaY8C8Cc2GGFMRhXwmlSMFMmMig5o01mnykJzFnIVw12AL/p3V0/klyaPEKrdbx/0B74VuBD+giwCxLiJ6vuW5UTS9kicUIAI7Dcm7ViqeNJq3j8ucPgQuZuiXJASMxLI5KAm+j1ymmeHb21vMysLylluMOg5wHogI0PH1xdlOr//ahfX1eQKNziwSvTXaLR/vH25V6gjpmkyKmiYpoEX+V1QGVVBlRILJjYXZG0uz0JEARkpYqEVgH3EbGmr7cO/88hqt9e5wAFfIOmEMJG5oqGa3/W/ffb/r+igRj6nNAYysqQRYHiULLR/cgQQPwddcEgGAL1kHSJVESN9Ixf72yzcQonGkB+wTw30n9erbT4/eOi3X3AB14/OF7CeLuX/2YJtShhP30Pkgdd7rDaiGqgw3oB0iAEmOkXQDrCfwSMXIBx4isYbtEUaWL+Va2Dgug/gzvULZdD6EQRWoIVcs0U5ugAvm6SALNjWiqEXLWAwb12byr55f5xSpXCrDODogbu/s5O17dz97/aYlSnQFArdcLxPTOeiFXQFR4brfuH17q9L4xIULqYhV67TOFfI/fLxLWmLH59OJL15/7mc7u//0rXczyXQVyRDLyltjolJC6cuxCM783PLiVrv/k70jqoKY6v/C5spKqYSJ0olFBIzwGsh/r96Szq2m9R2p8zhziSYkl2SK9lWOUgFnQKIiuQHrY1Y0cs563S7JisNn8H8Zy2KKnqDK/YN+EGVMI7nAf/FlPmQVJaRLEJfFggOR6Rn5GVswLRD4Yvpg2Y1nVIkEK/IiqywxS9hReTh7ATYCzIo70LF03b/76g3NRQAzfGH93KPDw7e3n6LKbyA0GNjwFVwDsipUTFQD0NycG7aezTCnhtiP9z7u9wHCcvwOxY0QgJTGNuS+akUK+Szom1PPuGLemRdpy6iqS2jgZoWh5SwEJPMcvYD6gGdCxjquCcUqijgR0RvR6EYhf21mNpdI8F6ILwjELy7MUY4Hk1Gj27vXbM+GQ2NOWDLUca//F4+28KRr+Szj+r90+cLHze7XHu4etTp00V6dLS4n4zRkkYEu5zPARBY/i0ag03urUud8CLSztq8+7g4/sTTDZCtnH0FCgj9kQIfTw2jQSeVqkN4ROwlXDAtq21SrnOpI0qUeioDdxu6DvgzDU6NhY6R/WBAqdnI8BAnDQGQssjvXw+LR+SfI4Fzqf/HP/+V2tUYNDl+G8pT3AhfALVyaLT2/vg452LE5K1G2E4XoXCZDuP/m3YeSbBiQoR/J1EMiznTWbDbD4T3oE7jolXT8eilzd/+QHlEmGQeCfe292xyHwiB12QkulkpfunAuboWI8ulUqgV9AEHBKUi2cwAUgpaDTPV8kjOYnyRLJ70xHHJYEmQ4Y4qc41ZKpgSNE5RhS0zGmpHxSAxgn8TvSRPEGhnsIOXSbZKjQuCRuQdCGEinN+zTLcDWx9iiVKbBvVp9ZPc3s1nG4XkWj4dswdYp9mcSyaNOl6FKFGMYNM0SflSKRvFbOcyMMkWEIT4CcHaCIp0kRK+MYMMBZKRiAR6DXmsoB7NAKxOkqEwvzpWQr1AP0QCiKIeUx3yJuYzX/dXdjx3YL38yPZFJuba6ctbq7FaqsDgvbZ6np/8nP3mPI9IU6kTBp2Qgg0w4k4x//rmLCP7r/cFps8lZe0xdUFgc90acf0loIMyB/bhgaixqR66SUbKv3Ljy25967aRRZ9EYh6G1GA7HdK9SMAAAIABJREFUiPtkVrIREwYb83Mz2Xwpmz2u1R4fHmQTifX5hZ3jY8i39w7Kj5HJ+wo0JUZA6MG3WXfCh0R63G765bO4z+d8m8DK2iL/Tuj6ctz6reuX14tFsilJmWj88OTo+3vHW/1h2/W6nlekq62p290BShuRtklCkbEysg7zmbzdFGk8C9q8G/4rAUZse8qPofZBdMT3+E+oW8kHU/6HH0PZTTVbvARsFNcsKYIPTJnrpKdKNJliTK5zLmxeSId//eWXF0uz8hCUjgirOdFzMDitVWIWLQ05+SuXRvSQ5AVBZkSxjw9PuNn12Zl39o7f2TuIQ7ca2kIqTe5lo794aR0I88Hh2dNqDaalOp7IiV1E0ikmAe5wihNmCR/FPnE/1HzsyHROzY0xZU2zlFQFUeMpnGxBwVRIpjk3FBqTAhFGGE4G7RkWQhtJjnJxXVQApI2ZRGw+k18t5FhKggh6UPS+x53+k2r9VChfQLZXGSI2gy8Vt6FuZpE5wIiFYXdZRxaTSIRD6fx8CufJGxxSxMqjj+BhEvEVAcg4z9XZ0mImi8PyUqg/ywNG8J5lYlZZzpelLU6bap6TGPp95CH/yadf3alU/ubxTg2OdzBAgItsAVdiKrYYT0CmcWLdWia9LjKt9Pl8sjIY3jmt//TwBLRqWlHO3sJtSZ/INzHw4wH9DofZXZTy2Ofs9HBQ4hXaMNIPBpyWI3o4O0uO0oSjB4sgNGdluBkWB54T1XufA0omzoNyBWD3u9evbOQzlU7/u4fHbPSrhezjdmc+k4Gy++AY3W8XCvo3P/2JuDdBN7U+UyQGSE4Pgqec9togX/T/ZO8YaHulmG/TDpi4N7IJdApo4d7e27+Uz31vZz88DdMQHjeLWaIYrQLOCWXHYfA5JPEBmnp68MiRORGL40kUBUXpYjb7mbW1pWyWxSd0o1hjqRkJhCHnEFLSL0MGp60WWg/qNWb04kiPAHg+85IOpy9hP+pX/7c/RD+AYINkS+hk3hJOAw0iVxAG/2ZT6IKY+CVsokGcL5Teun//2w+2OVAX76KJQQDjpUfDAdPMEcX/+t0Hi8koJIvL+TxM/Y3BIV7YMkv5wmJh9hpVBf1FiH9Dq9RrD05Pdspn9IdJaDRwGP0SiQzYP5EkOnJkK+s3G0Y5qUOkcn4LQTwcshAFQrxAh8jkKszpxAGiAgLJBGGRD1GUyWw9+If7TNPzicaYZtriBDT+bbXqDEwKsykjEpg2IQmIQl0ADuJSKe2Rz9Mm4kWWU4mrc/M2hopZUP4YNH7h+UOeMyI4njGozmlxolASBQn4miUcT72IgwfQG6JPYGSPgyTp8Mj4jRnJprOXF+eLVDmJBMojmBniGvW5MFSqQqLYPT783r2H1d4QKv/XXnk5jUiciE1jc+J/69b7oIYvv/jifvnsx1tPOX4ICQpn9okEywhVesO12ZmnnMvtjFgG3oLjPf749j2i2/WFmXvl2kl3APritYQNofAPUYto9MEg+GOGmjWt1Xx2OoCun9CkGnLukc6BXClTfenChbl86daTh0Srjbn5vWrjo+MTBFcQdiRLmB9qPppydF1YBEkCBOEpKn8GH/nGNKwKMSQRfLpr5ID5sMnoPNM9DFViQuNJgK58rzuga88YB2JcUBkD/TFZGcId4GcqsBDBBrGN5ptIMSRwC5DnEdQWgu4J9FEjdC7JccTRnWZ9WuAJxAc10aYjoxCwUAQhU4YnIf2LKm7aq5BWAGahoLBA/8FAgE/Jk6SlPxn/559+6ebaqhVNHp4dcqRoKVf8ePtBJpGmrcczFktzXIUwgcJFqHe2nyCgZaz/j352J5tOM9XIjb97XKVERuh5LhX9T197nmm1P7l158Zs8eLCwjcebR31BrANDpsC8UjgQdDIBUzP4gaacI+EG2yAapb4S9hCI4uxoeJtOgQ9Db54pGjwvEhRiVzcHQK1Nc7LG4622h2eS5cF/RIEAvQLZ8YBWEA/sRDtUBPVw2tLy1j7w0qFfcSP7lYq0pdC0y21kFK0QpxYB3m9EAkTqWsTH20b7wVwJpOBLmu9HmpIchWNUJnSmDhoh1huvg/4NWxkTkTlMF0i1CzcDVw8BBpSN4bmeQXgFLqXUjjC0QicWVsZjH7r+SvMFf14//TCbIFuPbpgjoWASRaUMHGgHy2Fs+Q46VHQId+EWyZGMxvIStX6PaTuxXBkPZPt2H3GKWg1kbfyEXYByivEusHIp4gfFAKS6xEjsIk6wEinaz5x92rVm7ksx0My6wqndNjvI9WlcCeesg0MId8vVzgyEoSWSsQ/vzh3OZ/hXIo3zq3ALn393haw4CtXz3McCNeDy3Pu1km9cXlxjv4tQrKjZkeNRI5OTv6P2/djqcyFmRmuH+p7u9W8W0Xg0/3Prl/4tY3lWzJyZ28120+6wzYnD/aGDJd1GdNxGc8yWBxCFoYBUUe+ov0AXLg2v8B8J/cIS8KxrIvJxGqKCX+6NxMYMIpmnIXAi/cDpufSaVoCRHvgqzOy1b//r/7o1vEZTktuxwioHnl1VgevwQo5rYUBJbAD60j5z5kt8WQ+mkyTmemfLEdDS5byz/7m1pMaTZQ+tDJRUqSysNtoFH0f1SPeK2SD6PMStJt2W82hGHOAyo1H0YwmsTMYyQkEz0b4uDB+LE7NENC0RS4QThEsL44A6UZsHw0ob+HLOCIKidFMJHJzYZ65mPqgT/8Wn0Gux8sT75zx8Em5/KBaKff6OD92xksRNIhZ0isTMZmPNRA8MIn5RDIzFbDOwsyQQMNRAgSOSazkknaq1e3TY/aYQgclHLEPYo5ReMorjA9pPGIPgCdRBhoOhnqRWrVYoFg+N8MoSIbo8P7uPj52rpgnKJDJl0XMpzY6LUoT7nGvXK7SInCcV86fR3PFoBCT+jtnp2gzOIkSxpP6gD4K20klD2X+hevXLi8vA3MSkeje2XFrwDlZ47lclrMt3753D3z03tMTVgzhDQFdJsGnZbtoyHh7wV/CZpJByeGgSwIYuYolokih8UA6pLH8+vrqZ268xEW6zvDP3v3prcPygFPGwC+eKOpoR4OthDyHhaN5A7wS0nmKwyXY83LwvZjTM/ZXcgCrQUEKdqARh46VE/hYK94UzEg4a1LwahC+aAfkXCThO6fAnQ0lylPJT9PLFORjn1Lq8Q3e3ifz8LZsItexkc1eyOYGEwfydIHxwMA7aHfwhRSHCYakkEVHwSz+8XDEJLlUEqBWif4Ec0kkhD2AKhQQOQAKCC38ZjaGVPH8whyUKeXc+aVlmuEHJ09z6dx8aZF9pDjjTHKOSmz1Ond2tomEu+3BhycVzvOazRcQQB90+qT3zVzyoHqGhuKbWwco4j63eQ6M8scfPwQVElmQc0gE4pZJZnJeEFhP1C5cFdEfiEBDK8dRjIkYHjrgjrl4ZrVUReij0ejUJp3hHvQYYV9RcHrsZn3Y4/kcZQwT24UXkpksBvpE30IhyFuhBUKMsJDNQw4QFyjIOAyO6TwpnbzJXqt12mNS2SnLKbMES+eE8z45iDcc5lgFfJdroxGFolw0xK7oVZ7L55BQcPyiiC/GY5zTFQNh61hUtBgBwRecAZqSQoxzKxRkXT4GzDQflgy8E3UDCvDAh7GRDZ9OsaIVBDQg7kIYA+TCpOn5TWk+OfWT6XHmkCmSQH8AlzTeFwMBADu5Dzk0dHoCB41tlhGlpg+hRF3C2dSwC+w+FFCl1+ZxsyHthTTSpBAgiZ5qBezsSPNvBjLBd5/b2DisNb5z9y7GTwb95nHNmvi/enH1Yj7bQIBrmR/UGrSLcvwykkH/7aOyDCvp+sNmR8S1eIHj3Kk0vvL8ta9c3HjabnMoGfqIjypVti1NOjatT2biK5b6ta39TCh0FakV81zMdddrtyt1Boah6lgWwBK9XPIWN8XWU+48OwIWbkTE4RzZFTIX4om5WITKIB6Xk885kIv8zWwp6kGwEe4Gc9r21KYt8/lyfPZX/+B/51gJxO8oBwjHYBAKyam8KURdxiBMPpm+xiFHMQ7xjHHzlU6HE9h36pStI0oEzlOtD0eEG5m2kD0R9QuOSUuWjCTnTVPSknFZeubQVHV5fnG9wDFCZG6L9AD/y+OxXK6EvuuUvZVXETAmdsxkpgt+r/a6zBxWOy3OqyDBzMZjVNwwNguJGN3mUipDeqCGZQiA8StKit1G7e7JyVmv08FLaAYK0SkqHtwLLMEmc4gFqZJDPIoxELAU5nPZHMMasO1gLKxKyEf6xUQOmieNKriY0w25BZ5OjYJaNcp53BzJRCsG6pUDSSVpSQ+YbQN3vHHx4sv8hgNvNPZVDhwGTAEYbz/6uNxq44csFRkFk+WumeDEFZBOicVIxlU5Hy2MEEmTE9xgxAYjl7PymZPkHA8CHp7LvSzErY1i7s3rz9GVpNLEA9++f/+YX4aja9B+D8v1pj3GDZFgybEZIpQWmh6czn1No4pEUAl2rDWqvilFC9LBUiWSchyVaWRC2i8+d/n8/Byo7azVevvx9oNKizZ1ZWCjx0AswQvKifIComlg8o+cqsSX2CnBXpgx6ddpoD/iOIORvDVUHX/p65IGgBey3RIdZKoFm/ntC6vfPK48bnU5VIdSDHsSjTeZixflL+FAbl8+J6tMPyQNsKnP2B/O1EYQxesQ0WnxrSZizPdH0RNJl4jB5tAbGxu8J7vzR7du/+CoTJEqHCtvzbXzsjQ+qQUFAAl/iIMxaw9HAaecFuJYhSVgV55fnn/92o1phmKdgla7SkXH82utGguNcO5+pTkyGKaM0LY/7nR2mi0pzv0JrUK+g4aaypVFPu4i/pyUckwj+xzhwv1iPngN/Xn0YLC3NCe5VKIe8+6IUK8VCzS3+AM4q3XaSEE4JYkT05j95iBCGFTSMPCrNRqmIDxB2e6EOW2KV2IcwgqJa7KWRI8AYQ/EEQOJguI5qYlRQWgljjxLoEq0kJFwJgE3vhiNcXIm1lCA5JFj6Dh6DLDE8XciBJrmefolDFXh6NJNpe3I3lBwoV/hD0e+c34O2yTZdfoxFQLQg6SVhrmwJ3J+Co18uAhqUsoXLg9cz0/FDVE28ywMcprl2SIunc/x1indp0nWGfQrnIWAitoM5cT1dHwKIIJ7cteHvR5rGpkeV6eGUMtYfU66Ho0aTBqqCsCZA1RQ4BB85tMpjsEnKoQ1Yz6ig66qPXJnAI202+i8ujTH75P5m/3jtWTsubU1fg3QX3/40R/eutfrj/6rN15Gcvpv7m8t5LNfvXGJozX4fQFnne5+tfb1+1sMXban0jI4TJne8rz1TOo/vLzZHo/LuKKub1ernynlH44n69lsihM17B4HJdyud55yRIjjEH5R90KwM5jPAag2otKpphGuXlTXvLJ041kdCTz4AVwbRsRPQP7MgFCVckssIbsCzIVvwE9IwBg2vSE2kiAvA0o/9/v/JIQWIRLlBPPKyN7MZjgqi7TMKxLX2EWmPSrdDtkGzmOX2X5OnX5WEMkJNj6zGHAFHLOMhfGiYA3aULwfW8WhhsLYQHVzmHWgFeg8xhJLqRR7WpNfkCJqa3G7qZqbME1Vhum3hiPOBubMv9NOGxn1aERBzUEW/CKH6JWZmcvFYgI2BWubHkQOHpGBe4blOBKd41vlHP8OB75zXj+hm5WhQKaMoMjlzkFWokAQCSaII8T8FwJEhlfj0VghkUpTQxkGXZCzdmti93v2gH44v5nAgedxQNki2EJOyNmHjI1A7jOlRRn76rkVSm94YbAeguBHnAKpGeu5NB0UagKmdTl4j98IBudD4xevGAy6ODm/9IhTAcDpEF8kDE5VQ6kpfGgufXFpBX+SkofEGZPSFcnahztP/u3799LJBDrILr/gJWzOREKvn1t+7fIVfi1TpdkwI0mg6I8+vrt1ViODYRwkDzK/XLoAaQVfotbl7YB+2MwzA3oWUjEjIhn/Ei0I3CwS4Y9p9WI0spqKcVgKNsQBUBM1PFS0Y6RchgYS5sBbGo+gUYRxEvdx6Slanzo8QFyiDc7MTW1mkicjpzyQKS2iDMsmzYCp0UoC4DPeW/oocuLpcW+IR3LvvCZ3IalSkpbkGAkEfC7ZQFZniv+fGT8P5+BjmEKhzgE1mPVsiEN0I1+6tLHASLsRYrBup1pn8PL6wsJ3Hzykvj4buugF2UqJOkJ/yi1Q+0pm4be4TQ98wlVAvugOGfKArOAEQ0SBG5kYTOZzm5eQZUvQZ6QokiC7E6yJWY1uh1R96+j0ca31uFqBbwQByYojpaUXJSleMKzcMHw6RylMJ9rmksnnZ0rMJNJ7nP5SMMFiHDYHpYl5PLt9wVD0MnAWfo0JvwnDljMViAg0A5lHQZTGr99666yKImAuGr1WQCwXQbNCxQboy/NroURL41Pn4WjEXM5xYj2RHqKugAnUzTCIjPQJChH9NadzThW9BHcWXaYWebRUGGyf5OKpEYn5yOfTWE7ypLLAwrk17hKcipE8C0wsreQIoACxnokkZlwJVSLE0FkBFNrgQpaD18SGMVdYG6SDuPizTZ9a1LO34lP5YA35i0IHtApEBJUOXM64jlIowNWQeimaPqxW+LUqsxx/lEpxUiyqXoZdHBs0NbnT6jBWQj64USotpP5/qu48RvL0vA97n9XdVdXV1Xf39Dn3sTN7kkvu8liaokUJuijJspQ4iYUoiQMEAQIjRv4xkCBAghiwrMSyJMOSEluOYjq2FYgWIVGieGhFcsk9Z3ZnZueenr67q4+q6qquvvN53ppllJ6Znjp+x/t73+d97uf75OuRb3rS1gBxElnLnxkb+OkXnxck82i/8a3Xf7Cwamwfnxxf3SqPdrV98tw50whe5dHiMibwrZV1IOH/xaXTfDik8i9/6qVPX3tO/sWf/uCNO+tVGtL3Spvvb0sP7ZjK9vAKSE2MbEBwLDs1sYqz2Szwru/tNIh5mffgV39sZvJyf+9ibe/tFTliVTtic02dDSDA7OOdXdMV9GnaU+5ieDjTEtgGsTn8sxX48Nn6BGFUU3EnIh+rDzAhqk+pIBAELFuS3wAQ21v/09/7t5qogCWgOPN3SOFZqmyLImLB9UMFt3lqr2Qj15KzGCq2pYsxBGoC/j5T5K/uYTUoj5aXyT0m3xmsq2xYVswAi6PlWNk0suBztPDiDdxeQgMCywxykq3SqGtewSplM4qUcjd0kuHUYSsZfdyyw909s9ojqMmCdoSAEEp7x/zm5q3Sutxeps2CocLcxo+PTuhN3JFdPd1XBgaFViaHhgf7B9k+SttQJ35oo8MNx+thBvBhlNmcUjarFebF5ta2XJSN6ja+Q0uVb0pOKEaHk0X1Y9VSYcYHB56fmRHw+dqt+2eGi1enZ7E+K0GZwkm26rvvL61KENzYLH3y/Omxvv4/e/dt+0seCBXyhYvPaG9gEkQjSJ3v3bpJ87KGcyU9SOpjvfnL09OTo+O2H0M+rWir4vO/eO/NG/NrIFCUPi2VAbMfq8Dgu/+xZ58RebTsILK5hiR6ksFWJ8Pg4MaR41GrwbaFqih6RLIubFZsZDU+a3U6RFjmwfZsaZsTX0I8kQoR0VQM2VpjFn5rRIPX2mNcvdRIuv9rZ6fVEPyD199qbnXjD3YQRlswimDNXEatkeAvhP6gWkP6HEp3N8vagqLXuH6SAbGN403sZxSFqgzS5kfELhiUbTyx1eEnYf8cPE1HUJCv104L51HcmJwIxTDAw3x+fKwsvq/16FdeuPrC1KmuLkbw0QMust29dxaWby+vcKyRZwAYUgAjmjFhYXwOuHPck56ExgRZBbdSYNMDWX2atwPgKYlAnunvhXqiPBA94HohO5IniWhnjH/z1u0/v/doae8ApBcXE0ZC+iZfGXusjTpCvYXiwocJ2kXLkVxP/vn+qBXiFxbHsjTBUu3aYMHyLIhCbs89HREg1i1ubT2QAKYHQ6bTb3nclAX9W2S5xEjaOybzOXaikhppgoIF3Koczad7MriPZmdStHOFotmkLMeK4xeWnDIa/IDmnvQD+ytdzPVMvCUw28YTvruUaW1xEDzLl+JiAdI4QyyEmHC5xI2oNISLnhNRkZDEhyNsVTqqhCYzTrj6GJMCS4TnpOUO71xiaiFp3Dy4TPrnSP4Ud7cKgkKs56aXWFab5eMEluTqNpw2QKLHLTl3f74AlFTGC+fEghD09vZhrQoU8159//Pjw5+YnLi7w7Jfv1HaON/be3poGOYVxzID95nBvNxxeTEMC41M7ywvaxRBQjw/OsxTrYyuSEjne7U5++N7D29VamMnh7Iv9plfR4e/8umXsm0nb95/tFjZBQcrlZkTankvcvwVPaBs3UCZx10cQieHurV4al4Ej8o6kJpJf1f2SIr+xy9de+3C7FfevQlo5z//7Cvf+fDOv3jzOmduRYAoLCdT5yyQD/HS6fFByID0EwpHLIUJZmI1P/NJOi/mU/qWFTEcNpWf1t99/QcfrJXCFj46gvF9b2MDafBOYtxmPMQIi8+yJkUMtfnBn7gjAaNrkzaShetZlV+M8TqO04bcw0Vo9nzt+DKtk8UQOWf0aFhikikDTKId9Wzs8DAeyMZVLCDv0r4fy/deGh2Z6CsiBZEK4KgycETnWcssR6OSkkS3tZA2O/cl7H5cycRxK+vRyljWtoJf6KQzLBdPWNuvA6RYK1dUxpLVivfEZj2pVKLDhtahAWzEjxm1nSalpWUsH0UcYTT09iJD8GogUaeHh6VUylmCt4VancsXMF+uwZ5b3q6ub6wrKEc0Sxt8y4fCPj0drWdGVNBL5+uEgI/nySIlSPg+KYn7R/uix+EM7+h4+eIFS7JVqYwPB2KU03XPk3nLasKIJIveevTg0crq+u7BSqWmFwpXSfIBHkXrYO0jFBPme6eHBodyYuOdIwoucoXt6qYMJQkEpKlaU/5QEALcxD9Al+XKXz5eoKLqRgTOxpJT6MLdlpiq9cJ7wm8aNgh+F7oCXoQfIScsWGjrXH9WWseCLs2xklHWJ52AmRYBJa4odBaRzKigoREbIc9+Q2RVkXqH3PmUOY7dOyr2P/7T5Obu4dbBh1wz6LZ5jNehtQSFOzAI3+GJcUQdRAityOaxE7ySvkqRjLRuzoST45HujEAoV/VrV54xq+tbpf3WTp0GKBmYCDUQ4XGOmQEXzLIMyQDqD+dvd2aku4MTo6o9xkkL1yi2ZM8ItXsEUCMWFbptX4fenJ0DKk7gFe/UMEG1QoxjkU9EsLYvM+8A+xNGkqDtceTb6Tm8UIvg1VAXYogfqrcHNIcekJkrLUHGOqWfZSzIpNlDea8BicyYxe5U4NMWidL+Hi3bVA3s1+3JtrbZ7p7pgX7mKZTHsWz2Rmldlmd46NulgR/QWq4OD0sgLnR2jgeUGCEVddMW1XrZVKwKwdkDxVYt0IkhXQfQG+IXdUjk2mHnmTFLJX2OprVUD4ODE4kpnO/KCoNpOMyPijLxdGmaGAn/AX8oJWarXNZLKio9gQ9zK/HSgPCqKbE6WG401DRRGSeY451dbZnMWVSc6/VoyMN8JgYWsietenQM5hJ4DJC4VMLT+QCeGxw8k8vJzhK5Q6nW0gPZ4LJ1zef8zo6D8X1p8vMSgcKqC7eyKWM2ndM5OZ+F17HaOOQ6fkT77spcyWV1I5cw7TK8H2ubpV/90hdLe0f/2e//QbGvgJufljDU2vLBBlU9f358bKaviHGZGnbDKtduozYUcaYIzCrNU5yh94tSHrsJaVkLtq7gVijpYVTFY9E9qOQGBnOJYypAHyRf7e9jZZeH+xcrXBonmlmVD46/O7eE6cmMSEqeaoIIC2HNtgEYQSl/dkdMWfMn/rc5QoqGGEiTmXZO8PzmnrOvHRE77R/92bfeX9l4VFrTqNMIHG+j4rYEIHIXTWWNyjODYWJrnh8amhoeVsYlUMSHLVpN5w2/G6xm2Q9SRan8MCs0ugvXIwfKwZAaVuDt0TG4O1kPoT5s1DQ0P9JkgyNSaJ51bCeYUMl/mATceVkE5oVjUujKmsk6AOpt5ayx6O2p3gLYCcZyrGvolXjTrgQ4Ak3AR8kDtCNmGj6rzaGpljghtEK7dCnhQXk72geK1oZqD09UcWBq2TGa6xrP9fRnu+wN8gAZjRT7mMaQXk3UegVOQTjdajuVu0tL0vMlxiCUaPfK2x4ZZtFM4/zE5NUpyM1dyrJIewV0bqEW2gRqMfEXN65DHHLlSKBSAlYofLgwL3Rz7fSZhdUVnav5ebXcCa07FRlQMxkxq1tb3717l4KsFl8KBL1S2QjjjBjv7WjtaT16YXaal0kg3cJLkbfJ0To/8pO1te/fu28Vlss7P/Gx5zUsf/3RMm+XyTnZt/1CZn+4rrSjpjLDFsX9xU6FCG34IBQDDYR5HpHYk5wDjAX0RLBxw4m7cLtZbrUOaiZwK4cTpcIk1qLSkEwRijleLaaC8WCy/LNIhY5tUX2Cz0L0DWvPMXRFiVgOiDZPbZG6klxMSoG4hCxipFjGgCJwwXfn3LBJ0D1DIfRTmazHP3525qWpyd9+8x13OZvPTvZokxtNkfgqbywHxoZ+xVRFgX1pHlKm5DJgZxeGB3/xhav/9Dvfn9usfPHs5H/0yee7Onv+/INbby6ttfXk9bY2MxKueCfQlUbWUSIg1npIkDfQxnChD/liZ+SfbtKqOkQXaEmjvZHuRZAIy8rPo+fykWBvLDOGAi5p0jxvVB3TDBgG7dDB6statNdqXGfvlys/WF7WHhnq9fm+PsewgHneaLtMB8DO+wDRevOfnpjgOPJI+AsQ9Ye1uuXAqfBWOkekvrRHNgR1HtslfoBa82XpLM2TVG3Ulbzc3drEiXBwBckTPT38UQEq2XKyoKOWHJj9umwWu2jZvgLbWeydyMGml5XXL7bBx9wifivo0tm+UJXruWNHc/x3RCbHoUouUcrIvm85flBr2Fxc7SwSLhoKnDQBGEtFAAAgAElEQVQN+w6hCWAwYmSmhy4I7hgeUfIF0VqZnuJhUPuvP3kEiFnIzw1lwlBS+sPL1wc7wVIKe6oGINH5TdyYOgLdkrFPjyTUwy7E75wT/9O1wz8iX6PNLKENMTGEpRgoIBla5Y/NDhTXtrelYs/0F01LubH3uFw17cKVSqSE5nkv5Ap+8sxpLmzMV77mO48fAlUcKw6cHxmWvMqDYtJwLdOOnUZOS8ClJK4fmrQXYdoaF2lnqhXi0Qbgg7ISGPc0U3JXmA3M187ODoUbdAPDTvsz1fuIS18aMtzJdB2Bn1CfIi4QV/Us6Xc8s39xE5st3Jxu76bU+uh6b47iC//O//3/Ea9hAZhriqdsIQnFs4OjSFm7BmovHuBhhJoC+xeabCouB5rWn81z8nHmwB5CgtCBKNc0F2FTtQzMcAoFHA+xGrSu4wfHnkHL8Wcl4CSq/gyMmyoJ/LA5EIp0FVMWmTdhnUbHOHY9X5t4iJyTqNnjuzg+qmyuifE+kvDOp14Dbtyulg2UYsAWyxBNC8npb1RYr92oyzbJbNNCzgotU2pTCI+wcENIQp/OdX9+dlz0hrIzNVi0DJEnGzP31P9JNPIbqg+HWPvv370OtXV2eJgD8trEmMRqX5m381OnlzdLHy4sXTt7QbnN0UE9QSoN4Vqu9t1bNwmVFy9eJuWfrKyoYFjQYero+LnZme/fvf/B4hI5hLlG2DY6qFjSg2cnxl69+iwPm0j267c+/MuHTypceawc0QDNvi37wd5w+9HL588BE6VADg8Oo20mQoRI1CcfHfA1USBxn3/1xttzlZpL9ffm16VnVJVv+fjQnneK2TC9SASXl1NhYpKnN5R0pJsUwFRtF+9CLqqA53oOOsad+XzjGHMVMsJuGs33VPZEINSax+6iTMh6Ju9z7Z08jZLTnc4uJKpJ9C9cfcZK1auVvY4MuLeHpXUMUcBpZWvDpbpzuR04vZG/ZKEiHOGmFA7+ZvlLzOA4JqXwkjSjqh9NaCwoda+FNofRYEzIXBIUHKTZvp6f+vjHsObvffC+TkTSBDnWXJmjX19AnAOH/eT0+LMzpz977rSJ/ONbd99bWluEXRocm92kqTpucmLLudpAV8/UyKhMcN4VmH2VllbxLfVf1ENdQsHWy+3v7WqnE6jDCIAQPMba03sYBMiVnRR8QfZd7MtQtxMIoj5NELNZHhJVh7XKYKxoiLZ/cGenRsh9anKqTzwJEaQkNxMSxZyB7xAhdytly9Av8Q9vIQwtl6vvwqNvaJgV6QAMd14jzvNFuZ5B/xHqk4slWj42MDDYk8MkzbEU7dcXl+4HkTRQo1A/FxNGReGgvIigBvvOZEZ7soBVHxNoh4cg4+39lfI2USP0frrYR5AfdXa9PDpybXSUhsTVGPpTWP8Rk8eJjVlyCnKDgKJl6db2xs219cf1hqSPnf0GKFOd7mlpJDR2db4f6meHNG5eHcqKdP4Id0ZZb6v5t+TYF75sMnEbXNCNKkq06EQCEig6GZCeFxlRP4J2gngQS8QnsP7wf2KHSRJ4gdWgaqlFwrDgnoLQT47Ba/P/iBE+3o5qp5NGA5MKjwoUMvK7o4MNggnPDA0pGn8YHW0qcb3Y1MGusZNwjdgkKDKmINF0MOEgbApXElThEY3xWBhji4CKoovookM3dw46Cnpp6kJxjbCnY1fG8eFgjcs1f1zZJ8FJTHUwe5vDKAie9NwGEoWHrf/0zfeFlUxRjKS1jcrJvcCBg1FTzLdqNajrKksp9HKH3QybpsywZLjIw7PZooVmrbctGiBw8vCWatBBaNOMyCs5QnLgzLJyHgTBFKXjN1shh+qegBC6uwK1Tpq2NqGRxBYPHs8UvjFRUzHnGmSP+r2SBkp1XG/vsMFZn5PP05PLptw1Hnmbp1TZjpIHxH54yOClnqh916U98hzS7qObUItoAEbPYeJeRkTmTRVyFuy1C2evTk2YBH43TXfdV+oLmaHVu40rP5OZeXdp+S/vz0lZ4N0+H+3sT37+1U/IbI6kps4OBsf1x4+//v4tiT2XxkfuL6+NDw4J2+7u1ScHBqk6jyR0JugIaNukKZQedQPakPr75uMF3gPKQhBmWCrhYTBXgznQSfq4ggs9nq/ukP7W2iJ7IBqW0JZ6MZAsA9lOeuLk0FCas0Ms1A7lArq3vLpc3aENqS2kNymK2WLhB/idGsvQnnHPpsOHxZf8tVHi43OjoCjRT3ntiAQDQJFBjiKi3FOpA5HlsQH8FgiNqIkPUWGLAOBhLtMtxYs+y61MAZ8t9vOnL1YqpBRoeFA/fBEgCtTTsRU4rJkURkUHlCF2eZC4z+gNt7m9dWd9cwlKErpFEva76gQKim8bDQUv7P2ZQp9tJWVCfEk3U7o2B5SUEroqUrEnkZSJks15rj//0vS4ZsiDxSEi6fUPbnz9/ryAiV7c5MWwriUAhtrb9WpXxvWZmTE7/Fv3HwBHYxdDZheM5WuAv02zpAArR+Ku1dpscXt7fnOD62k8RXcYZzuhl8UySdThtkRC2XAtkvj0k86szFTpjPuBqanqUoiF9XO8b2n27nIjpO7qWA/GdyaX3RbejKqr6C3OR4PvyhfG4rWIEsu1gVmBdq5GfDIRbDg+IsVjkiA0d/bWnioft3AxMR+kZthxPKjweinITE8PbCMIyQ5IyIOYQkM+aYla8WoFr9k80kZcdeORkCZX51KtrlaZbOnLdEmuWNnd3ditS3xGsbyvaJtOOZjNn6fnUXBb4facFNGAJ2EhdPdcKvadHx7B08XbLCM2Il2btsoEksVEmSBmgKBYx+sb23PVKucBarSL8T4/GKu1iYiIDZIqfrE82reiBI1lwPwTePJEoixu/3A9QvutXzo3Q2b87vUPtS+WEeqRkXSw+qRAhOpi+jDH9H9T3bGt7D7ewGDX+lN1dAL/UYKKqfMMcxTT6CmC0n5ThrPdj6hbypUatEgqlLhEumRc1L444KIPHPJg7t4m9R/XT3GyOA63pX3Gn/QmRuJNMPL4xLBiEz49xN7Dcs1ajDjONMTE0tOb5qWc4PB4HflFcWK8ia1sCZxKSvmGK5JxzCqyalR5EqH1v/+z12mFLG5OJcE5h0opJVojqQi7l2IM3gQmHywCa9veATXetUwH6uYc2FJawgt8yFKLhnN4On89rxBPAmcfwBrWjBzkpJLgEfFgNHCjNJUGqLQBcARXGo6lW43tV2/TJzrAvEr6ou3s4OCbUGFgzfOcdnYNF/svj48P9fUFcvrhYWmnAm/nwUYpTGs1kx2dsICmCwUqvApGKSXrtZ1UaHwovvaxsUECYL68gwpjnLt7xtGbJ0pa4F+ucbHu7U339+mvhYnIqbd1SQ/0F/h5sYxtKEupLfhfmIgTA0PD+R4J/iUwapsb3BcblZ1bK+tk1C+88knpy1998x0BZnxwYqD/C89dvb+8DDzj7YePuAsnBvtHC3mCzl0elrZEy3W7pBSzb9CxnWj6hcPsAb1QLBuHVag8tjHaQseo35tAQAodHRoR2azPUbhfwubplJkuCAxZkcWjAogjstHSrgIgtF3SK9OpLon/VyaiTkyclZaD5CbpUYorEnXcZdxNXLqlCPDsRS0+6AXfKlgNEkPHkAVbRfv7Mhl96Zx2ua8XU7u/XaYhQkphluGGpD5qk+p+OteNKp5UK7Y6jQ6vCaWVgcwpnzr4sriRI29IP1/H8cGX/+u/82Rt9e99+Q9qmew+8cDb0NY+0ddnimbyPcaPYFjxrkBZloQKNKPzoHGmv6Cx6HtPFh+oKVNYsCcKJ9kMjGv3x8cGf/m1z4Qqir6PDsCnvLNW3mvLyKy/0p8jOewHPX5BKh5p6nJ0uCBsiDK4Sjo6zw0NjeZ1gm2sKMNpOdGyo7Sr914bYx9LwpGFqcLJE46UiE+KCMrDM0uynGG1wxG71B+IF1R1k8deDE9ERDQ7wQaES71e3+CROQiAQnmN1kK2Pt2f18Y+1tzq0vCw1GfeVPX2XBpsaJY3VUfpU+T5WFSCAbtMW59FBYpOkF9ujEJlurA7idjYg2BLZKMFFwR7J6mpSxJ0aGaevaaZ7+GhFbeJeJQxCNRm87IwRFljxwZmbYf8yG/PzX2wuWlybGeM0vPSvvEoZgoXihpSboNTuRyHBgk0ns2+Kf/qYP/qIC0lULym4Jflc6WDI31ux3j9W49XBeSVOnd0ysxh/Gp6Tql/vLX14cqqBFzeeb4XtIFd8aFT79Ce3c7t9aOnp37xxWu+srlW1tfeXtt6VD/YPjigNAjzfOn0BAr//vp2hNYZEVHdLUmRkLOPMdngmMEhkyhA0xilB5JAQ3oHhatu9HWUDQVsOFYeiTQUnFYeGKRtL7X1Hh7+9KUz9ys7N9Y3iZlmoBUH5Y7BgvBT5OFIM8kL9JQnx12pUh8x6BiDu4RO7dJxQ0OJXw6PzR5fxrk+sKRxQLx2RLxIR8fbj9h/KGS+MriIzkfs/ukxLu/cCBsbEC4xke8B98mD0vry//wPK419mQkuT7b357KAFuSoUClElswdyxUtq9RCBFRCziz1dEPdXfaeEVMjyWfcyEVDzw5Ibp4J/nBerMPNMCMO6zW2hCR9Gj7LqpOyyTmT194CbwCyIZeAd/JIGXpdNkzmhL5DRrWDDJP5E4kRqS05hRHLoDpJrVuuVN2AD5drSttr2WCmnspMSmqwANTDOHkt+VVn+5SPa3KTvTY+ItuXkFfG897jJ7K5d3YbvO3fvPXhk43NmYF+2H7gdaLOPqzCEKG27OWJ8TMjQxpbqkCRy3F9YYVxcGV0cKNae+nc7OeffQ6nsGG+9tYPPniyuN0IvGKKMLC80b786bEJbhYBXrhgCyW1urtXpqZUZ5AudFIsngu0cXTywerWxaE+96Vxv/l47tt3dc3cZR8gNn/tQyRLM8Xr6R3xW9iwSwIrOxhGXjg0iQwUEHw5LbIliPKQ4xbKo/p70U66MA7e0w0UEBiIcq3W6Wy3nnDRtTQhIz6Qzb7bkD0JRRkok4VWnwlsQ4R5IpfVVUODXLOvJgjv5BKlFm3Vd6hgeMrcTp2rnfAWrOf8JXH/cnGZdkS08y9RmYglqDVSsMMaoL+x7VJoN3C9kh/ABrbHqKDsKqtQaGu9WMiqjglnI6rYP1qPXJGQeHgZ/w/54UmxuUhSSD70M9rpHB0oIn310kUGzVdu3AZSJB9mttC9cdi21DiSu62ropSB8UJuuCeDzB5RA3SXhUp/uFdsO7i3XrqxtEYHl6fh8QGrqU+8PCy1t4Pib3zbjTqq03aC24rPGqNHJ9gEEXhKXo3S5b09uxVb5GAUL40d2gamn/4REoIhMiz5R8/UtlYloP2wTGTc71TfW155WKnyXhRth5Ojx7Wdpb19u5SAMJk85qN9OiO0zhYKF4eHhbEg1VDw8TNLDeeOqOjN94o0o4JIaj05ZoXDP1A0w/RcrGy7HbtIkHZK8y/giSFsFSFGiE/d/nK1enNrWwQ/DOuj6FA0Kvc8nxvo7uHtNBWj0dw8AtSi1vBi2frlltZ5ISMrK427s6PY2blcr90xbScn5/qK5/sKBqHhMC6jjwfEgkSSEQS+Wy6rTdGA3l7Fv7vQpBdURnqb7PvBwWuDA3yvH5aYW/v0j7FC78Wh/oX1ks3CzaGuzVoDP4AFYuNHDK+9TYnsM+rxero+f/UKHegr77z3b289WN0/HuruFDK9uVkV3fPUCM1+Qmb0ORwgliZxzsQ2ccS0c2IThfPBGz/BgnFfCvJf4cvO81y0+p86M0EaPRYCre188cKZ7y6ufn95HRwvbt1kuUQIWsXPUbs9Huw5yZz0H+IV/A8JEMw5RpMGFFKnycfd21dxQPB73D8GFXZJSISnPz6LMTaH5AG9IXJcIGRUXNhL7JBmEochYGcangHio/g0xuV+4Wn/m//rbwLg1b7AZsNJ0Xd5v0GYm3fuAdvenV0Or+brUNUd+5kMIRhY2QEBENG8dFMcCqeKILvkn+Q0hJ1+xOAV/ScGAs01LJFIrHacoIffJ2rPkmLLv8k8sXvIIZagK1NU5V9jf+7ex5APl50paF2s6YMcQQLDZkSL7MHKvzYy8GC7gsJO9/fDwKEjC/ASRTawUBhhrHFdtuXoE9Nj6GZ5U9uMAxSG8j94slDZP5YjCJ8H2DqnuRAIKQj7erOyc2Fq0iN86+at22tlbmLNTbU54++TKdTVdvzKlat8w9TJm1ol63Wn3L9cET56dnr82tSk7W1aIBJrhLtU3tEZyhLMjAwDughfg314fPLmoycWL3wbqk72D1d3NT3u2tEg6UQxjgZJQUCmNHI8YvZDK1fhx4ngQ/4N/3HZpdVhY7qgS54wRASmxIqpwKaMC4ghwudi7jAIVIKxqrQ4UR+X6EVNLxSadTOijCXToXeuaLOSer4+9IiC6NrRmaRY9NRKEwRUJERqucBXhDvA3761vKyUSQG7OomRTOZepbrU2BsMR23o0IjWmtKgElGHAQhuBSViGQiUmRnxisgcP77cX7ww2P+ksiN7FUO3OQhUyZSpdWhMUyRRsH5QbVzL9ghLBaNUgamE72efvTI9OLS6dyQLy3Lc3wRf2K6r+Hy5irrArGvbyxVJjVAzCWCXn0T2XSlWZ9sYTAKKiu+7uifyeQopAcN3pOIEjxPhpONT8q0Xv5ks+7MDg88ND6FeMZUoiIkOlXvwF9VnyX4WIdtX/R6Fr92ChKYaL8amRXqwYI6jJ0Kv0pP2D25ubXLW0eJpT+oJhYMHu3uw7OdGx54ZHmI02H54n9ySw72GBoelKBnp4LgfKQ4cS2ltNAoSNw5Su/kQPK2K2DWfGlSjHpVrXPYn63uRjsLClBovHMWc4thRk39zfV1Z32E4Q/keTyQVBMQC/QSh5vOnsj0X+goTg5S9HgldLsDL+nirTP1PuFgHZB7/iG0rv0iIUk9aGr3BzMObK5ftV8UxL/X1frBdvgkMh6mXokccd2cGBl4ZHS30dLEDsJrRTAfh+mBzE1zYZD67vLWNS/Gzffb09CszkzQyEqheYzzW1NmUIBWftNzZqgK90O1yWieZlqNrp4Y/cf7C9+8//DfXP7zfOORIcIrkB0+GzEL7R3HUpdChschgisgpOGpwxuCLQVjBQIOn2qdeBOk6PL19+n967fi+jo7PjQ1B3/rH33n7rftPMnloT0+Ln5ySWLcTvcJ+g87TR8g5OH1TyqQbpKsGFaef4LJe+z4OdGi6Qhpb4ntp1DGkGFiMEGcOO0TMXBkBDA4hSi3b8O122iGGqckdYnZcUl5T4N3uj4Q3z+4b33rV+qV/8KvLu3v6SKhakqBmy2olFQPnpTEm40izErFsd2NGAVsXyz084r6MRGzwgYYExDgKBQX/iY32fD5MSZg9YeLR1OJFloK8tsM9SXgfRwqhBRGtCju6PlcqjUoWx5tOTk6JUgpd8pyTLvI14Z9kdBM0HryjY18cHBrBQWRtTvZmGYz4APcxTF6oZ/Q+WNA2KZI9PThAWV7f2JAC2NfeMq0jF6zUHij8R9Tn0KYVTOZyE0ND8uRfv333P/zcp+89mdvc2f2Fv/Yjqs++/O1veWKMI9CsWtuBq2FVY329wtS/9NpndDb/nT/5WjlcoGa5FdbCz7z03CcvXf7g0QOTg5cJA5gA3FmckFP1mzdvru3U5SnPrW+QvnxKeBP+iCB5fkyxdTadMrrsgbOc4NCy2lvHi32nBoaEBvmjpwdDAPHYeqhELx/9Io8T3SjMXt4ohTMtDN6TpXLlO3Mr4+CE8oXtQ/7IdtjR8hSLmfbhzrZTXFiFPElza23z1lZVk0hjubO5BUOcccLtRUJiT0qf2SvECS7jbSRUobmggwhY0WMMWnY/q5DKh8H5iEo4IFs20yVhBkcUeNRKAfuTSMPd7VzuReFjGQ4jrECxCki8jYZErq09rREPxZ+QbCi2CfJMdIqHxB5BKnz0ACQoEAwLG4VOTTKpTqBlACEgAKSIQWEiaLCGDvZoe2YrkkkOcTf5BqQfxzp9In6MJLxtxwjh7MCAAadSr4xeDGwUOokB6H9ZEvhpb1/lE9zX/7YNeMmw/swcd7sSqLh9Wt5eWl7a3FCpTzHHWMlpKTQvj4zoEjzQ1xexx2RSo7TQDBU86zS3f6DoBNTe3e3t97QO3m8MATIZHj5fLA5T5Kl5LSrLsihTJJ/GShiHJaQMvlaFLxvZcWJ6nBIyC4I+A9AxXPC63msmakNF44poYiVEGdAVKEseNmknqCO1IdKd1Xq0j2YyF/v6RvKaYp083N6iBIymGtH79UbAIh7o2BwtU4zb2vEane3NPjM+eQERKmpx1uHBTrVyv1LBOgSVVOcACCKT3IIdqSuRNAXgBdxK1WolGq83ou/KtI6Y+dxurbqyua3R6aUhsCWDSlAlfbEt9us7YlrXpqfeWlzVmWptY41j/9JQ/4vTEyxX8QOy+O17dwBK/usbdyXD2MjccZBiX5kc/Zuf+fRKaf3/+u4b87tHT+p787vaBzDwZB5j8jIGWB02MYVJqCjVS0QNEmoJsxl5mXYQMdh18OCwEYJcgtn5v0kwiek6wnRxvgvdcTHJWZLvTBnyJcXLpUJlb/Ln9Ds+i7+xX5oX8i5xU9IoSYW4eLpLOiJp2l41N3ji9I7j3oxyiq7aXt34xPbj3GDKeEaLB6AOygq7NDbymZlpGd6P1ksfbm5CKOKOYw66N4CI6UJeNUO5XA5NPfLxjhVSc6uF1Ptbv/brEH3tYY4/fj9Mn9OHRiN0i7NzJoTHloofzo0QIO7rKngE62ooQNNA0O0P56P9MSIY6mxd2NycL23UJL2he6j9ckklrWcyqI3DUf6X+/KOcQfhlZvlbZ1xPnX+9IPl1UzbiUjyH71/2+ryV1BYGIyzhRyYOV0G5R2ua9yzXXljaY1iiOwflWtiX0ZuQviSeHtxXlXvZF2olmCSujpYZaEmFLJXxoevzk6fmZyp7kDIOpYpH2Ezyezt7Tfu3PzKm++8eO4M6B6du6MJWnX7jZs3uG7Gh8dLW6XF9TXhINGO+0vLgauJCUZ2lySBiIIYGHuF0VDSiPBw/9Xz59UN+NCmxT7uLS2qbZYTcq+01dLeeW5kSALPY9BPjV35XuGoweAiLY1HCF6ViHTvTz57Bf54ME3O3O4eFMGiYvEwe6qNWuQw8TGzjUISBHEpcPUJW6RcqwJ2jrVqaYVdfn1unqv6j27d4yVDKGz20GFJ6JYTehYUORqu0+VOMM5cHGAsE5DXSP6fS3Ode0CGhcvJUcETpVjERIcWcQRX1t1tA88uISecz6RCJLx3KWGlRwPj5PBzcVpFklsg58JoYTlCagQfYwbkCEGFJFn4RtTl0g+op7Qago4aO2ohGEPAEYeHNIsXZwql4egA04kKg93dcK0aP0slmVBME5vB8tC4N9RBRBwlUphYSzatMKxKcg4BMkZdvrR6a0QC2Ubc9FGGpUuXbALg3kkdFNW3OVmuIQktEJZXrz0qlbhiJQ9gvsQ/F6r72VMeRBCe/TpT6AV2a62o0sQ5GSCCImN0s1bjX5JTg/akhMuCp9wA1ZEowcggqyKtoChrLCeBAj2D2wiodzbK/r7UO2FSu0kJGz1JdLc7081dg9FbIOt+t1SSno8LQywXmcAgLJKH0lJ8OMeTdmAJmDi8N67MbxNMh4XtUTMZ0G9kJn1toRyGlrT/28bJjmT01Il/KUxc5pEkikRHOjuGCwXJGsO5nuli/9XBfumsLJiuNtgk2VENf45P5jdKsczSlCDEaY2Ag5wcTqlp7+pRM7G2tZEa5rY8qQu1NYjwwaxiguy5oSK78IWxIUnnuqGOsK/p64F3dkxnOgADvLvDUHyyyYV5cLNUXiIVkztbotR4T8dPPXd1o7b7rQ/vLjeOJcJyfgZDC3MNJ2jy4WCa8dd6J3ngdejU1IKQcHrKylmNIxLPTr+CO8dUNf/FC9MXXLdZN3fCpEJaTrItmtp9EvMfnYaOvQy5EDf1KomPOPbpT/Or5uGOiYPTID867aPDqKpGKt+Sj9HpzYv63TzT3anjh0JbM4NDFyYmFD8Lvla4kVtb31nfUIBlL6vVONUn7N+FnGm3BAnKUXoVLqBf+d9+k8D3IA92dukyZE14gXATbhxYJdEPtutBWQ5MdEw1acDlHIwC+PHlluDjk2AC1foeHpzu6fwvP/vy82fOfuP6e7/z+hsPK7tona3teNUVtgqthidUFodgYECJmZ6TFj0Afu7ZK0phh/p6z41PvD+/8Ic379moiENK2t94/srL587woppp2uiT9dJ76+UHWxW52Ex7hgstk9KKXTIUlmu7yI6Wp6AZmUuf/ZnLZ7tajl44f/a5cxdT8JlADfUoAq4pGVmY65tvvXF3bWOzWvvcc8/i/o+Wl/AMRVWfe+FjsfMP9iSXLq0th7iSn3Z4MDs+KUfl7gJ45D6tP4QjRopFYR+WxLMz09CVqbVnxkaxQmGPpa2Nh+ubuMCpgf7by2uboE/B2Ver/BiU+c7W9oICns7MF65e1uJxrrz7qTOTT1aXbbPnZ6cqzPMgo0hcNfPJ3S9hMS/pENvVQ8acUCeflDZ4pW2XK6dGAfxCI0AuWOf1RSHp/XnFwKpSI0ku1g8NU4QRvQlFQgw+gWJGGb1Isg0bTnNzvyN7RNJURzsF3BgCSoxabb+kDCW6IRtToekUkZ/NEiRCMnKZ7TeQUjx4WqFyU0hZwRMnCr0RlsDZQUjaMZ0ZCruL83z3hIJzgi6JM9s7FHN4ljwqATAZy8c9jc0hHo9AplNAILjJaRnnyOvIKBza2qnRbdWpWDJ5d0L0y/o0AV0AQ0LYyCRRa02pOz4ZNN2KOfb2zLxMeVpqPlpZM0clDqprRRLHku7FgTgrTC9/IBPBDDDTBH4Ksl/Y/jqz5bRyaO0KlOZuCTakBdeEJQqcBvbiwf5CpSJDSWCXZcf/CUceEvQbg0UAACAASURBVC3d6AwofyvAywqYhHPaM7ccE7eShTwCXsm44YcRbwylNSXX8viBs1UygvUzI8gkNqV8DdtY4ToGQF5qGU0Zsu1Z1XQ90ZcIQuxjnUeL1fqttZIyTKkcVoSQ0EeBV2o620Nw8qeFJx1WilzbWu2xXIZ6Pd+pY2KPfsJUAdVeKMHMhClMTlsaG+3khNI0pnoGkQ/2v3z+crvqSJ8fH95aWeHZnO4DARM9sNSI3FhaVLhgKW2Etf29UQ23RboxFgD9EcAjWQ5VjUj35J0D2vPFc7MzfXmf64QxOzR0c3GZbb1R3vylV18uZguW0byp4v72zVvfu/9ode9gaz9Swzn3tGnDSshR8QkuuMhQjiISJU1GHfI8+HbQWvxD9k2/i91tbD95ZuLW+vb72zvcg4nn+tr//tkgwbjjxdO/SStvMl7XiSth2n+Fpze/ihO9Mog4Ox2VLhMvvfe3KRLS3eLgpzeJUYVZHReN79LVQoIlTknJo36lb5snxhF+wmPHpo3yxn19B7nwWoAm56DnhYJodM4hIVCy3hshSo6Prw4PaTc00D8A3IAF8FvLEUelSEUBG1jaSBUQtmqPxAak1peF2paTVzdX5kaPJNyNvQMHRDQBum96VNyh/WD/MzOnLg4PCA8wEr/54T31ohVspcFRfxROF2pprfbK9Ph/8qmP/bPX39IhlgcZPyKtS1tbTG8+Hzodoqe/WCgXJ4rGejITfTlQdFiHMOJidVfV8tbRMd2T2tLb1XZ9YZVzPKj0+ARIjrM0N/7br7z0pY89//e//AdDPZ2//NlPqoatVrd0bnq6tugPX4/mne3Af4D36rdCL8A9GYbWQJ3FWK77xdMzd548YsE8f/EZW48MQj/OsxXNk8TQIV0kj1vWKxDsj8qpAkU+6Pnx8e2a1Jsjvn7BhpuPH9A0VDmwNPmR3ni8cO3U2I9duwzVeb60JikF49M5fZPLPgE26Gvf1XJ8YWjgUxfPPlwROoZ15QL4W/uZkZGhQgFKokmTJk/AkKyPVlfKu3sAgijUZo9IRxbitOv1hvwQiM3uy6oDYOIss4qRkCXWwhbl5pGjwzKWySPFzbcCBspnUEkYHEyQjo6xnHwSkf2MdCbHCsXzgBMnuAlmOpToRJ2qRXQW/t1zdCSKIDFAf5/9TAYGoxv5SxLEESqDdFBKbZh8QnuwCVE7c4S3UKTD3NLZ1b+wCbwaCo9XBI33DgOaQkjOyG0jSTLMDnZ+iApPDb4QqctdEXwG2gMdPnySeq5h8eTOIaasXn1box6K55GmwdJRUGZjQwllqo2U3+ICTByVwKItxCmHPpxXSQue+pSM454eOf6kmsoje8t2sKm4aNR/yY3txaA1rbNI5ISUrYODbRqqsvCdHc2YAgQq5lneszTT7Ol8Hs6SZK0ojI70vkgckm3MRwGhzP0cqIQ4XDtQmBTOpKw8lkcDjHnKNYqAvERYmFcR5ZFSFaYYkY/dSQAToeVVMwwMhL4vSUw2kQWh4UcWRmsbC3tLaUmImRaOXI8jQ0znd5QR2RrYIhVKfM6mCn7ZQiSc6y2wnvAHzEbd4plivz5WzCXCb2WnRq2BU0IysVToCkf1+v2tLRk42oqc67cF28FXOPViIfesjn6RDttx3Kh//dF8VMnxkkuA2mtQ8mIJDw/ODRTGst0//eJzIC1/7ztvGPonZsZ/9KWXEeiDxTn9CiEkK9O7J8iGD0SL6chmNk62jCJjzgnik7ZqqHizRwjNPtVCJbaaxAA+6g1Kiu2iTOSIAHZUcG5KQTouDoifpHTHFRKbNh/N13GFxMp905QBMVVxWLq0TZbYeFzEhymQGi/iHNIprpHmNi4bP4kzGUzzJ5km6UQHG4CrNC/nceKBvLf3feFCXK8+sxFo2sx05omDOEL5d3wT5qxrYrOWO5JlPHKEdfOtbbMDoGSyrf/Vv/jXDmMyy/bd0AEBiCCYK0n8yh07Ohewm+MTfglvqWaEJDc6j63UneulkpWjs9CwIKdzrEdnBgXleFB7h0YiEDPUYSuKo0Ermh4t9nPXSoon+jnI7d4NcH1yeCNTLQhLJSUZkBaMVycmCekI7tM3YR5QJm05hLuuIlG6c3JMQVlSHm3xuDVs4NivNuc+cRf8y3OK/w5nu+FrjhRy9PTBfhu8i7qKy+iBTpi5Bd4twVXbkxt3PwT1LJv+W7dua9E32lc4d2psajC88PRPPXMsCYe+3hRUqhKout36Azg8mrLWgynzlnBtXzk1ppG6/UzlBwo0PTJazPcRAN98/wZDQDq2OwrEfGx2amxwMBzWPAnl7W98cOvRZhlMIo1URhB+hAZU7tsq5soS2hzWOVGn2Hv0BPa8FpUhJc1fR0F+zCCTJFMxWbkH+AF/ShTIoKJQgjjuDi2lBcMfz/T3nVboiH8cHalrWydW4Sf3F+1J+9CVuUem4Q/ngFi1PRZfqe/xutCqgD7h3drakIeArsQJADubf9SrAPDjQ8VR/Zmz2XdX198pbSq1xdEU5OEsXClcJdRqj8NfbHVKkeR+XOxUMNWD6iiwY93KqqOH33JjvxDwYq0g0WXykwd8HQIRaBmlh/lIOWlr6+3mQ6JONng2p/OGpsmP5uDhsRFWHcujyojl6iGlLUT4UpMshWnBMpOBaoIJXP40ewQfJywl3Ztu1+eDoHFj9BgQvR6oG1p1e94MXTzVKTHF5P25IDFjOaDuuIHtRd6oWTZF0nIMAzsf0Kw1yv4FvJgK+ka0m0m5y2QZgQcEX3cKFUaIIdgufS3O24f0SSsDu0sjJg2k5LP7ZO94nOgsaGBJAUQxFgu5kNmahRJYCu9z7KyuDLND2v6p1MyW4ys2l4xtyXmhAFEJAhP7yfa2jV/iylEcaxJchKqVAAZUyY6YncAjOrbvKqxMnEzwri2kuNh7ASxK1D+14xJMOoOkI9hxTApm1mBbu5C+WlFlcVDKNTDkltwul0lilPPZ82fODw8ubmz8n2+/N1Xs+5GzM199/9bSdnmykE/pTFhYRFDwdMA+rOnTgwo+WpYlOBydvHLhAoieP9Ve7eAIuQZNJHbvFCfZCDh4pJLbCzYFVpJYs4kNTvhDVi7jM05M+GihVviTeHvzuODIsZ/ikHgRXuam5h5XaXJ33LT5VXD2H/7E93FkYvfpm3RFR4Ydko40huDEXgdbTx81z0pDjI+xVRcXD3JAXC4dGZ9w1bo53ZucowByv2NlEreIgbVqTQqf3UvPSPI7JId9HyNJlycQmnIptCXIBbYJlvIz/8uvPizvHLV26jkOsHNxu6ydod1KbXQ0dYjDHW1JgLEgNjDeEew5MWjPQN+HGBFqSiuI6kjOo8uzViqNwIGgy/AzuggWzw8F+UiY2LrIzRjMFyYHioK9ZIlSQ76V9V0XCRgy1erhRI4mZZGGRdcz+caOyeKPEdBJK53+C4skZoz9R94JYKTRIh+fF3N5mci8q/yqIdLY3JAIGrVThfznrl06OzEpNfPJ0hPBjvHBkfHxmcpOZWF1uYNvo7vn1tIy0LqffOnF6/fvz68tk21jxX5iQNa/pROvJI0Ui4nXWSU2joaaMmT6CarDfe0DxQwWS+tXZ8/NjI2JeC2tr3X1FJz45e9934b5mReuTQwOW55qrTq/XnpU2niyuUUNUVvHccyqKDX2q0mdEc4P+yYcVqiadysoklvVnrcIWAY5EM6R5A+1g70wORbYX19ZQqpn7OhQdtrOFXsfbpaxtMGuzqtjw+eHBkgmzO7JViXSV/T96CuSFmiInivvBYimyCeODMuTbqUOi61jNXATuXdY74J64ONjyqMaLtRF2bw6UIRVd7bY+9ZK6a3VjeU9KOQMgxZFQ7aQSg5VXtmO8OpEMEsFgwRX/icttsOVT5dvp3FA0WbCQ16b6c3LLARtYAxB9ycneI2MNeAv6JNQEUThLWQByPpo22+QkQUecnpDVw/Oy3/F+DBVrAB5gSYQuCAAQc0Cs5luQjtcBGIYeJx80Pb20B9VhWFw+9KB1Ix3DiVvVXdPT18Pq8KeoRiEwsiThxQZCrLdCF/sz7lKpUR0zQS1FHw/guML0qKHNgpEHWa9T2AVUPxP9xVCZxe+NngBDI3FYfvwDYraRRD7yCQQ/6jeNKuHYqrLMnSYZ7dk9q8xkxM8uXKemzqvJYAuIlhipwxElnAbZY4hQhMKOOiTEwhXKtFwlIlsz3hAjXYO63OeyZhfG02Ugj9Tjw01X5iqJO5YiJYwQRQryKkDpByehExAb4nEosPEXQKC0aXczjZkdQnYXhkYIHvUH0MuJF4lNTGh5nf3RPJyglW7O39+5770/5+9cuaPbt+/DyyzS6la29+4ev7y5Pg6daxR/8u7D3k/ZbKedHRje4SNfa1SyTLZzl98/rm7C4tfvX2vcuIZo4wFawq1xbzQ9OMvxpY+TEzbK182OXSTEwbzdRSG8tFPcPLwz3jvOfz9q2/TYemIj9ixb5tc1ZHu1jzAOelFnBzT0/w/XrpgMGLXb17Zud5i47400BhbGmmwuOZF4pRg345wrn0dXmtGClZG4GmXG36Io2PyktW1VK5a0KliwWHUQZTG9W+z2K1EiGe3d+PSXKjy9kPwGEncNIJFf/df/suvPliguqpusOS06XCnxZwaY1Sd2ndA/0gWG8NrKw20BTgTWhdi6pI8V6+tAadVONcpISxyydDQlkRD+hRFL/j1YT9XXU9GTP+5Kc2gOil0tf3GJ8+cQ3+13erDjc3fffsWRD2DZDg/MwS/tvX66pqws2HaEyR0/CEPY96tdEygAca1KWvB933P9Z8EQHCiqE6M/CfMJeziiAeyE69Njo33QdHJ0eI9Pvg22/7FS1eG+gdcB/hzeaeCAmmefKzz62tyLm0zUPLTo6xSXjGNO/Y/nF+IFitHR5+6dFGLtG+8f1Pj+NvrW7aoouIvPnPp6syMHdtM4Vjb3nq4tNCXKwwV+wA7swnurKyOSFUShQcuTeyp8z9u4W144fS0MYtJ2EkfLCwt7DSEPeWnMu4IVKTAtksrF+vi1UcUz2K1uMHuQxXCvoMkE1WGyh9+W8SSD+dDt/tCtbP0f+387Ljey4rLInO/JnG2q63l9298+Cd3Hij5Vb3BiKMdhBclmrqw7wMlODEEZBTmNg8JPhV4PWgjViKUGXqL+RaGQR4JLqDTWZbM5/wnQ5Hzw//e9drYiJ4qx3zZx8dn8lkagPQthX6eALVGj1Yc9uhQhYF1BMaHm58rZK8Ooe+gQ/CKChGE+pDn9omOFUcMV9WrcnXCFDvahwoX1cA8EgBVALjmsxJsBEVJFxMlqQmL1OWiH+JNwKrsV4BH1SVQSja3JtTu8AOSoCY90qBD9Qgub7qA6+MvgCFoxqwrB/J+3NnY7IAKEl57RnNbpJ63cRIG3pz2xaQ3mpHlOg1hLZdbksW4H91NWcDA968yyFJvP74ZZtNUsZ/HX0rfw52q8rpxoOXJgRttiLUXFVNWi6s8EJxLClBbP/aKjcmOs0ymeqNaWdmpih6jZ8KMAjgYWD2HYqQkIV1ApSGxBx5BKhp32aACBaXC6hJ6ey8MDILy3VJ6bjlbDoGIaD2P6EWVwKWRbKxtgWxuJ91U1HvGqvvrHqGQ2ZX+RqjAFFLHfBt5JmGlddE5tNYywxTET44MCvIDzZXR9PLUpMAvA1TKCvfAa5fOfvzsOeluQNwAYOwft/7jP/0mpYiF/3hjO2ocU6QE8LIoMyNHyQCV1JqGUmgsQfaxF4L+g1fE6HwY7M5nMdz04z/sOHHw+D4+tg+efpmOSGd6lfSt5knpsOaXvk07zTsM3QaI29h8zWs0z/3h1ZpDiA37lPvHGI0tNDkqULD4OCOGlw5yU++TbIhrxPf0PCs+0ZuTaiETjAvF5OJ1wGpCFYPI0NpOsoK2pu0BbMfv53cqTZ8eT4AFAQl+cWT4+sqKNCG+7GASMRuYZmvrF37tN7ZgJwVubRC9ZwvVzAwlA8r2NVIPZ6eHAyINLz5LHihERieP7d0JwqmbcUkhCnrwPE4IrVBdGACGdjv5WjH3E+fP/OizL1ghistXb7z7jXtz/IZoml4DoPzliTH5SAJx/92rn3h3Ze2f3/hAdxtDkjGGPjC8dP8Yucu7RTxATFwsgT3PmcBHozo8uv2eAK8GLNVdyCv0DyhHOXGMeo6k7R1O4ABLiMLFzo6XL5yT3r5TBzkTfYLQq1xBRCXxhlHc39urD2JvTs2UGY4YkY6beDoBIOufw+Te8sqttS3J1PmCUjnoQxkxT6PV2HhT+5pSqVStUpb4RcXZIsjR3intRxHyeG9WHrRtfwrKf7XyyoXzn7j0DD9H0ImcyMq2juHfePCkJNoQSj0CD+dYLJmFiXUIO9f7pOmk9XQMTsXng/0mncKewmIm8hrOtAzlsr/0wtUQI1C99kAz5S8MAvvvoPe9+Wjug+WVe1sVq0y0w72BOcE1zrPBunSubF9fSWqicQSvT9IlTXvs9tjw6TPjMUG+Z6NaESSBxaRwQ8uUUnP9PfYbCqm0Bhzvyc4U+7op+qkIQAae1ku6FXKpcd9JCoS7LROZP4WzzmJLBeZGUBLIOoHYA2VJ/wkZO1Jx2ElgA8wO6hWWlJ/e1tnVJ98/Eo8CBILtR3+nQ3B9oBhudfkyyptlpPBPqp7CUvBifhbpU0esv/aOMynFRRGiLFJB2hKkeNaeWqTu7hlQP0BNTlr0IqdVEGhaTcimUdXFh4NL87GI/HKT79WrwBI4x6yFGBsXvKpvlcMiPfYQtUMwAPYmUlwF/CmtubNL43vxzBS9bF1s7Om0paYaqhKHmEmW4XMlK9cDubfJ2pxvaAl5YCv7JFktEsPaqclSp0WqC7l82GTNrFg9cMJ72WKbu5Dcm7BgIFZG+UK0TXUWfs2Eaj88mMpkirneDiE34HeNfUtJa8d96gHvrAlowPFqb8ReBJEk4JwUyVAMcQhUaTemN61yBlir1DuOOvdCLXwUiMVMemSOAZwrUkJSYc0zQ8WfuXbp0drG/cXFC0PF0yMCBX0oQe85/gMuh/sra719/V9++7oAANUQ+7OWdH9jCvrAz4I9+BuEiE0g/9gqNKYmh4iPfdIc7VM2mz7zsbexjZ5y548+9Vn8BC9uvmr+/uHxSTEP5kPWYIZGlF4/PSr9F7/8SyNo3sXvNFM+D8YfwzFqnxlDeC/SHdNJcWScGif4tq0dgtOITK2qUiftWmO6/XD34wqsNBzYJa0xBxi7NlgvmRns18cBHOAHYAZ/po0cz+9TI0EgOMILv/rrUBzVAfou5s5UOjw9kIPTAGPU7P34Nn57ZsN308SRLHhcLsm1mIqoW0XfvEaoFv+g9EWJQHtr0WN0tg3LxdWLR5eygxOaGEWFLhNODHiKkW2tJVa7yBJngsCgFDTXjsIft4wlTeEPH7l3iCk/aYD4SGvLQGf7QILqHch2wbNFG5DX0MD3b98S3GvSvWTNK9NTg4Xinfl5upsEOEFdWfDnJycG9I3o6i7kCvwWzMrhgUGzD63dvcs7ZWEAml1MXVI4VrdKwwW9ZAAqNO4srb6/umnPQwREr0p4tjfXcCWFA5ymnOCUR1zAMtiiUSLQ3fnSzNRqufJkqyzaeW5ILAa+NAO3baiQC1/s0dH4wPCNhcWvfHBX1U9k4IdkptNEYbCNZANH9UDUTwWwnxIhosVc0EmNlj1eULjTCsr4kBP8EyOD2Dq/8GK5xvbi1lAKH3u/0dBVmHKqbx+exL8MU4FKxaB2vD9iuTToyiHHUgSWjMCacvJi9GIVhCVHFMKzXhYL74OnJNOgmMlIOAviOzpa2W3c2NIGIFKJMHfeYkEa6TRCoFBrlBoEwg2wEN7MTCd9mScN4UoGlyFjuohwTMO6Yxm5thYcjacI19s6OGBrTmmWlM9LRrwwPGJgigFJd3TFZGH3kJZSttyULXx7eenW5oa1k8pCb8WVBlRpRceC7Nl++bZtG3VNKQLuoq6CpCMzmMtLZLqztqYiUvO/i33FWW6oDNW4Mbe1yZuEBsyvQYaRm/at57Wj2EzqrcY6O0Yz7XO1+suFnKdYPTxa3Duc6e6c7OqE7QNC+f4u98/hCHLNZrePTxTQjbbrRH/8teUSLv/yYN//82Deo//c7NTNnV1pLYIZ9AAIPs8VIP+APMoQDHjq1WKf5EU5Sw+lyuwdXivmG63t2Pe65jBr6xR5BDPMF9TRJmNVAE9vhNh6ymgiEqMeM3pCeRablqIQ1YUnfDjRHc80+kb60ENdhet1VBvgYPBlrUeyCCn2kQsZzd2C44faFGnK0VjbNKIKnDM2iiP84BopRIQ/BXeJj7CUsFtRFdVQecfh3t54rnsanjnrKXY3OE81YnuXJidsiLmtcmd3z8J2BV6AjpUIj2mBGEIvSiwgLhQsy/38n+7qlsE/0+v4Mo3EnYNlP/0qXjc/b36ZRmZs8f3Twz86K53j8x9+jEfG26cnNs/86Ky4BZb50cXj9Ucnuo4xxdMH44qf5jdGa7h2b1w3dLwYho+o/541KYDUFE4Au1ucRzzNmQ7GwOOx20IGSLCOTs5RORjdfP0wZt0JLL6VjbJPTZvDOAcNYslCqzM7rRd/9Tea3gN835isTwiHeO2dwcSNoloOmcQn6ce103D9b8zYASdG1soFIcTEOI1UsDfclO+FYshQpZsL0kWcjkvruCXyKJrrF8p8TKUVjMvydLu9kRBlyCtuHpFufyNUx9zj+I45CRFAK8Ee2Syur0nWy7MT1IdRLTsmZmRtlyubdO1bc4/4H3gPbB7JoKP9A/CnPDLf8evvX2dOfuGFFwcLfUZAOXEH8KJ4fT6roqW1Ut/ZBMBbrZybnDZrc6vqAKg6J0PFIa5zGWy0Qhd/vL7xT77xbeYDtAtdJsC0wP33ELgz/4yJNuzIMjg++fTsxKtnZkD831xZw1wEA/r7BnyLKd9fWrqzuGAGPRTZgBdrFo5R8nSbIddgsHtaJpfEJPn+MpZurJag1ErSODM8ZBjCfbq5yUQiS0SIKcb8Hkzshc0tcUtJXF+//1jvHUU3jCGh5sjoTexS4jx/jhiJB2chMrm47BVPcfHJWYxJ4f1obVnHbtSd8rDrRaXRM2nX1TUeDeiz8ly5bqQMni5AKu2m9FV36+w5z7VSrz/cgscihoT1HKihHU89TGi+MM5kQVlHz0g64vekS1Ajh3Xk/0RaCIwELDsyUCNfQGwPaCCs40xXrLtqlZZoH0hpACcZmEXhJCEgqfzc1qxSupAkAsKJhBAH5obOt2fEYOWIQrSFRqlqxonUSo1LGZF2gVmRxeWmp8HcUzNRWUurNqjzZQ0ZxF9wTx6iSJTE0I1ZfQMlWt6B7DiQRDbBWTC6ijNBK7YruwsYa2AYVDPi/HxvXvWANM1zue5JyaC47dGxWbVNlvf371Zq9yo73NyvDQ+oflcb8bi8I7EdbKxLETBWXxZ1d3tGjGtC2WB762Dk9SvUgA7S/qAq0fNEFhBhwDOA18uvuNzb843VzQe7e2dz2XkIzweHZ7szENNQMp3pdjm6nYhyq14e6+6yyy2T+L8UO7zGpNnjPF3Kzc0k/3J4fsIrhYkE2gFXqIws4YGygpD9w2EEZR0zHR7WmkY8OXgZRhN82tZG6ok/xIdCGqZGcgdUH1K/6ejvUqGpuKmj4+PTE7dX13m3WckUOCKZZsvvyvFkOcLyDL09qdGJeTT5Og6VOGqw08RFMNP42sdxy/gVautHP4nJYjpxsh9fGJ3fwbKbRz/92MDjk///T3yQPmxeEdNKnDuN4KOD0+M/Pc5n8TZ5jZrD9TamxQ0jUuczYzVl6D8Ec5IEppD6L3hGriIz2ToMX3XayFvGs1zE7uPDrcMDTg4LI08MIBvJU64FaNWnR/ozPVmV3JTQ+/Vd2BWRTNPaikLCmqfAmcTTv/Zbnpd6mQRAcPzgx81Rmoc0upAUMRp8P7F8R/kgvo2xCj0pLFzldrS6ZgHj9owhvRKSQcRwQpejpKNyL5gF4fcKazHJ45QWkhYxflkv35EZ+K/BgnOgI2A6HAX0X5c1ZpE9JaYUeJvQ65F8Vk5joaPtb3/641fOXYrEynX9q/eH+qWP5nZ2tNuNZBAh363q1g9u3zLnXB8XJibPT04+WnyCYREAY0OjnLOoONetvWVIUYuAxIUE6PW4qhQ06aTr21vXHz8hvQgoXAnDMgO01w9WN2SDwuo3J5GUlCYuZiM9SPrdxpo43dvz+UugJPVRy2xVqtefLPCEiv0ir81d+cuxtbhocVL7h3NcpjbREbMu+2X/4NXpyS9cPDcz2A8U8w9v3fnm/NpnZvVrHlSLIMfUSCSVr9cDtFYIvVzfmSutr2+X1eJG0oj20FCUgXBEW4vYBx6CcDJdkgUt6erh4Zl8Diu/rYwwI59n8OrgILwZMEHUyOF8XoohWUROiK4PSrlJCOabFWmosmC4brqKxT6P36jtWB0EIuGbmfUEaH5bG9eHDuNyBGXp5JOlQta4NV0GFZgfuj+hchaMB5WQ21owoFxJ5H5cqvPaBdyjAoLnBRgHgKlq4Rq2yFZ1Wx+Llf3Dceku/f2gkkMD1SCos1NQAeo9Z7G4mYUQLJWeu7Nfp2bgKZgIeU8Rii4kKjGESciotsC9kYOwsVPWBXp+e5vvmwBDZgQgvcT6cpqjDPt1Jpfnx9A7U8zs0U5NwbCIyPl81ipwEAmzi6UK7WLTLB6bS6a12RzSaqCnxyqz2zTGkwRpTvmaMEERIOYU08EVmDgX+npRO/uJkvWgunN7u/L+Ti1cM4L5hT4RtULr8SfGhqI4JrKo25FfRE3axRJ6FkEW9kKobQeHJ+n2Ybm6sH94sTf3wvDwzMTIVMux0C6SEEt/VNmZqzX+ZGkNmg2q2LXDE2Z7NEWBtjY9PtmV+caGVKxDOdlgKnjnJqEKtWcqfEftbdP9A5rW2i0krklSrwU78kFF7a+IygAAIABJREFUZpGaxZrAJdYm1Sm14EvMmp4R2YphP9n+oSMm+7KpJiZtD98IvuB3sBH/Al8Wr4pQkzH7gwPE5gzmEdfENYN1oqH4SV/EtvOJEGZi5cFMY0cGo47jmv81D0rnpavECc3vn/7vq3ROnGasMap4Ece5bLqOLRTf+klXc0Tinj4MOzExumZ+afBdhzgxhpQu8vREkpVuyF+Cbl2teQsP7dGwy3a9GXBOdTBSV84ND1PgeOGksbmFxLjPTw19ambqK/fnbpY2yyFLWgFzMaSkTMzt7P785bNCJa+dm5koZH/9O2//3ocPJChKg7RYtgmapDm1zvzGb6fBh2fHHT0N9uxV87Hi0WKh0qDTA5iG9Ekcag38eaanZyLX9cZGmXIUx8eiUt4j5kzZx1pkB/kbGl3U4oeEJwP8db14ynSO6/Aoo4B4a+bSa+oAxd/eAGAwPdBHH8EdlJ5LfqpLW9zdYz5jlCiPUT3c01XsaPn05QufffFl9OEbLh1hWyaCuN/C6tKGSvpKZb60WW3sTQwUXjx7OiojUvERjd7BMRDjiB+jiEDFRnlLTR0Ot1gqyfg8Pzn9aGPjyYaW2nGYFeWcEUuI/hXCAwfHsFxiAp8aoS5it8YzhrqlSXJ359m+3s+em7HegJo3a7u8LnJdcNKEGtaC3fMX8WAkNQf1dkhe4qrmF5IT8szI0M9duaDJLW6obfS3Hy3oPzyQzby5sHRrvcS+ocAGeQVMk2HV6jqRSdUxmdIuu5UUAUNrA9ojJCjB16pCcSAX8XpgmcWeHhUbACQwxMmRUYneInUsfed+fKiXL+7uZvUYkmh09Tm2e3PdmVK5TBxKS5EVIOtXfA+QPRbPeX1ve5sX/lS3rvQnysXHentNir8btTpcnWFgO/2YOWx5idtHInsoks3Cw+10NcDWa25j/V6pBGzAVGTlVra1XerruzgyZp9IeHdT3qTq4YFLDPf1z4yOE2zUd16lCF1EWbJcKfoB5+JxgmZjO3fMb29Fok42y3m30ah79mI3m0BKdDuEwccbJd2mFiplpgyoO9FFnjRtvOgfIGkZZJZzsrcQaU6pq8nCzg4+K0RPvi7XLd0hHdZDqs8AqSZDVC91QSb5rDDupRXh9TOpAiusGIk0u7sQZDhwDFuxug1meU7n1b3LOeb2DO8oEYLgZdx6WNNOrlD02EYF1Zrqp7pVcbWP9A/Mjo7SVe4vzH/v4ZPqYQsspjdKZT5IAfGTrsxIT8/5YgFfemN943KvbtiZz48PfWJs9E5F/5Za98mxRhGyIIzDrlGNsbJ3oA4UcMubm9srjb2p7sxqfVeTdaNisodXMLgP5S7ivHnFBxh1Ljfe10cYKA6AlmFbaYxAtX9UAnOkhUjdzONc6uZQjhi1KhwbVlNJ29P0YhEeH68ItS/+2h+tVAosM7hCOPR4UJG2fITQt3yYtJdgWbHdEFbaj03B4Lu0fRMHC27riCC+RIHxJvZt+jC9SQwnSZS4VvMnruDEdEp8+v8dH69DEsT18Kl0eLpR3DM4PmLG4iTpKZLQioekZ4g7H32EeRk3j+ODi/pJF3Y1jDSeufk2XiTRlY4M0EFcJF1Tdpo4ftRoODJ5lTXuAVBeA0YQHzpOxYx8to7nhvrxpUelbe4UvXDFcjoLBfnZ5qOsW1kkmwWzDYV99p/97yF6YlDxYOlFc8rSJ0bZnETfNv+kYcdQ4/hYNrqEKjL+kEi8oSHGOMJDGCo825cFwHMdVa/+Rs9uKJLO4pAlCZqqsoOdbnQeVsoBbilZpWArK/IE11PslUc8MzahGMckLq+vmEwDvTU3d3eZYQ6xO4J/mF13y/EvvvryxZkZpbYqp5nadl61Dg5td5UeDLzhUGPSozMjxc8+88ygFqwK1jUUCx8bfoH3ihIDxA/UQHmPa/qvrqwtloncBuuG93w1EPm5pfX1DmUYtr4JzGZsTpVWXExS1GMGE114GJQc5OWCl4cGrowMvDG/LLIM1+iFidGLIyO7skKv3ya9kIsLGkSkf1vXDt1mTRJ3nJSSDGV/qliErqPKSVb43dIGRskWBomj89rWbgMw0XBfoV+yvZ5Qe/tz1cpoDz97bpkIOD6Gavfi+CmIUUcHjZXGgT3MmV6Ed6NcMLL3uNvbaP3QH7MdGWm/LkJbmdsVhDvAwqRLDon/tGfWNBGLHiYiAQrF9+xnijkAman+fuM1M483S5D9F3eqPP4ywqXyoAoxhqAg+cGsKI8A38aauqy8Q0XUuigLUeqRYPUFhELRbjtfKOhhq3YEWKk2mVIVuZiae9u241yiOwvAimUC58D98z28QbGCgrHhHogsHRxGHRl6bJVEazPzL0kApfuIUth/gp9Q0gCivTu/eGN+bq5aFjAoyX06aYMrNzQ4yHSQlgomgarheCf1ZLovDgyAq8tl8wjg3ip8vzANeaUjDxJOBjEfwMjRR0HVsfpC8eTXZqbO9+f/3YcPYJygUXAOEuPqtd2/dfn0q7NTv/rN7zA4lP5JYdA8R1DUpsH4Ii4d6VuRw6fmwj7DIjFuFfvlg30ClUroeLV7BN5PXT3/wvkL4QxtOX64uPREcs3hMQh+qW+31tf+/eNlC8qkulmpDwBlOTgQXgYJ94WJ4Y+Pj/z2e7f1/Drs7v6J0zM/emqYlj3d28NUIsnIjsfV2h8uRYcWpMXRFEyHkGsN1EKkDj6Et8GsAg2VMK1i0UZU8E1i7Ya9mDVdk/3RiA8uE2sMMTiRtquaF/MqV3eYqk+q1c3GbnDF0O5FGnCk0D2RGZCM5OUNRZ+6GliybG7wiKKPbGIbDQ9I3JjO1Qx3eoEMUED8hDKaOK3/gluld8Hd0k/874DgxfHFRx+nd940P06MPs71tnmJ9EVi4cEom1czYh8n3dcV+XIuyuSSLJt8T6Ipq0I+YeeGBmyRROZdlzRNTh7/pau4ZjxXSDmc3JXxhPSYic3GLfwQJHGAV+meSdfES9MDhABKYiPZTwoDOaTblcHj8hFDTW0kzvT2glydHRho71HR23oCqp8ZMPvb/0fczNib0xGDSJeMO6aHTLd/OgEx0JiX5sz57XP3sMlMkt+q2/kXonBcfRZaiOdq5ie0cI92ixlKlYuwcJtaJ75qpop72PgemmGJDk6fmjKdG9tb4m7TwyN4MWxq+fKmb2pstFhggjdV9ZbV9YWvvfXOk60az0mopUEaJ89MjJ87derek4d0c74UQuTq6bOo0AG0J7HfuyslpVUEoGTwmeFBgP5cx6wIriS/44npjPv7ixtbZAi3g9JzDhn0Z3vTJzk7/A4N/fhkolgwaY+3ASEHnlSYBUHJQZZmw919gEFgOn99doob4QdwktvhRCql68SktiWJe3B2ESxMxr7k9K5sb6E4YaCK2iSkqEg6gWyzxxexqrFBgvFBamzhCNRGmLQt8IyU74OVb+yvgFtobdWEfby3T80wXV5gwCLY0iDd3QtzARyopzH+z1pfq7As9xleq0BaaqZRLoqki+N8y8nlU6f0NNbwBGtjNvG2w1z8weLSA/ns+3twofFQBaXRmiqSWUWhj1Ebkq0dH5JdREYNUbV3TCbne7hupQCFubaPq9HRRzszF/X1LPR6kMrR0e3Sug63s3KuCn18QBJpVBGOFgoeTQKNACYFn8cwJDQIiPB6RwkVWmfNoHUkyUllicnyRKEUjk7Cj1IWEYJOTTA0oN7hu+GJ+nBr69Hq8nppXV3SUmsb35FnFI8Fv8G1SuvXLUv7aBkXZowSh6EofpRMybmxXKkgkLTjA4jUifaNdfGClxb3wpcp0Zy2mk8cNurvz89bd21kIDSqI9a2sELSNvY/dWroF5+9/D99+/skPmf9+cG+4wDGaVU9IKlIMutqbW9p71hukggwszeC221aX1GfnvIgEQLhbnUA54rdP//qp2gnHry2U/53b7xd7Or58WsXX18p3Vpcnc5lmRcE9sNKBbj3xYHi3/v66/fre8pnQLwx2vAhLi/Q/EJP5q5rd8/u+tKVs6+ND4OWfFDfu7W5fSnXtbh38G6lbiQKF87leribJJuR2dyDOp+MyrTKdO8SNB0d2xwTKq5Fzmo7Ov+JWDZXROQmmLbSh26c/ZiDSKZIS1e0gxUEcgyHQTBSZBMqc6AHJt7SAulML0k0FnBme/uBMChVWrKfsp7giBEKtutcIqspRnsbtcAkBb+KzRi6tZfBWx0bOzT9skuDZQfUrhcIJ7asIx0QP0+nOWnIPmt+6Nv0ZZPjxyH+xUWan7qdI+x5H1F2qfzBfEaGoA16oqWdutbBi+uayKqKD1qVW0yXZ69zCdKBlGJsH+zZGs51YTPnV7pH3DUuHb8Se0+BWp96i+RAieG3BhEu9Yi3xkE0O8MwsWHCp1lyrcja2D/okVZD7+N9DXX9pHXmt34nAX6lgQfjip/ExJrTke7bnAkXisvFzX74N1h8HBheB97DyOeV+XN4eKovx/9HkYgEzUQrfJEZjWW6O8GwYf08EprYahmG74ohg1UAzGkhRB1vP37wYGnh2TNnz5yarNa2xVkhWbpDf19UERkQFiGD5PrdW3Nra1gzt4B8HudafyIvcIS6MpK1qcaXpvlCISt3UBrfvfehi48ODl9/cP979x9wavfyWyOZSFiKB0dDpt5+NoPnJ06p81rZ2oZz8GCTFFCeE6yfDGcEIBbe2Bcnhhe3q2+trNNylUhBPHFrcxupS6jJhtQ4my4bYA81k6OqlseFC4vyS0wO5XvPj4396Fk9C/prGrak7uSwqe8tPNElTKkEVXphp3pva0tKokAj6AJqEMUWTu9UdEUeghEv3wnvflJad/BAd/e50XH7PBSNSMYNmA0RbxAIejhIrC5tb+OGgyraDg+WtzZhD3C7U83sEtU6cl6RLOWZUcBL7lxxFFUCc9LA19d2q5V79fpaY+8U9C6mE8CogB9gsRgSWa4adl/aQXvgJXRKf2S6IRJileCkwo9oLdAL0o87pw0iCKo1zrlyZW6zBCD2hWLx7PBw4Eyz+shDJmOKbdL0ECvzKLAWIhjIqwyvP/KdaJ1omlGotyh9HxVIDA3rREa/PQhfpLFLi8dyzMAH66t3NjZ0uaMOSzI801tgkYhzaoyllnhRyRunByKr1x5U9YmsiODzgI3nFCy3SsQ07VE6y2/ZpP3YebHPQ99MSqvdCfWWR16zDHsb6NtPn5v+yWuXmQk355/cX9vAHM32vfVNoQLUTsC/ODZ4b5MAPhzPdr06OTY9PMiPL2fn3MQs8vjB/Xs3F1dkvCzV93D/xUY0zCAMMH2MT8TORmcDyW37wjTU6H5vZWvx+C9sVIG47Xe0v1uujfVkR7ozQvGXezr+0U987t52+fdv3H+vVB7KZ7+7sr5dbwiegf2RsKuhI23Dv9muTiD7Zwf61wR8qAtdXbsnrTBL3l4rYWEMOJMgrI1Zr0KlzmTANWLHc9Epfl/bV6mKUTGQgV+UJZxsba1gtOhgiNtfaKUPglN3p9IzLjJTAZkjxGdAg3RKKaN/hd1sNiPJwwyjILpH+MGGVQXm84oJxauzEFwODsgDTAepOAqpOzQx8lijtDjBMZ2e2LT/goOG3vqUe8VN8GXc30HBIePA+Mg7LxLv+6unp2/iMumbGONH38b5IV5izwdrTrp07P44hIidKBY1jQApC8f+3IC4ezjByFo5IDRfjkTweCYB/Qz1dM+rnouEi7hW3BJ7dfF4TV7FNZ9eP16He8HD823a+7YGYUZfEXtASOAGRBARv0dManjkZDq9v6NzqDOyPFRounxcZeLXftPjxG1iHmPu/PEkPuJ7YpU1S8c8n3vHOcHyEWCYIkbmXQwvxE/ruFKjXPdstuc/eO4qn8D/8PW/ONcLOjZSdNRnqsJ9aXZqpJC33qP9RStNtc/2aMiFm0ejYKwTUt13b7y7Xdv5kY+9omYLKieJiMEDB3WPeJIk4jDhre3SVoVNjAIMHNz8EczOs6cmpscm7TS+6XKtMjs2IfB778mDMxPTb3146/r8EsRlCAauQmboeMxzggJU7Q725vXqgi7nQWq7+6eH+j92ZsbmBO6vSvbDdVAmB5uNfZYBS5/M4Ic1Qzza6gkujI0ocn7lzNTdje3/+/pt+zAYYk/gyICOAeYVDetFpAn69rYHlR2+4L9+7vRAoagG4pOnhiBT3t8OSInvLsyvlrd5dcDwFzLd0ShRB8Te3kntz9ralvVGO245XSy6rCWtN3aljmDBo4Xec9o6ZWHqdtpmgYjZ3jq3XVaCROtf362D5WWCTBf7zk3NDPUWdVt9vLyovXG4/1WB5go2G44T4oeLJgNhrdcwJMZI3n+0svL1J49l6A93A6/JcE7JXueAxnOlmNvYKKTY0TGXkhEvFApXx8dnBoeNlnKN81Pulhj4VuvgAD8VRSSGmZt0auk0pa0Nzpy+3sJssdhf6IcgiqBIblnnLJsw51RK7+9zzihI1k7EFsjrNaRGr7GL77wwMVWQ78R3lDodLunUWqncKK1F+nzqca1wQfSQ3MUvrEhvb+8zo6Nn871U19vw2ho07naOCUmlXJQYuipIQpeaSpghNrdnN6CrJkpMk95RGmrHaNL/4YMWYQYBzWm+tF05U+x1K/AkwC1ePTX80y++IMV5S8Xabp3epx3FP//Bu99f2TLz0ari6BAr5Lwa7829cnrqhbPnRvqHXF/kAuEpxuLS+6M33/w3tx7uKsFjCOIL4lVtkJPr3dFy+eC5ifEvnplUasKPpMp3O5pr1n/2+WdqLSf/zVe/zZtnD5suOBNnWk6++9/+nXfmFv7uv/rD7R7Zoi0f68tDIQRQqgpMufUYo1RrsMOj2Vw3D+y9nV2aPmYjcXxflk7/0JnxyV5ld8rk9LB7Mv/W3NxCtTpXVT1zwsjQe1OUiKT0OCc0Hsw7uhwTxwz9TrsAcZrGzrYTHkuhI+2eZ/PZS/39Y8PDgigP1ktgSPB0y20G2Md4itp72qtITJjkqvuoiTQzngM4dLpRRWFHVFQEl3WOLZ3YkKOfsnELJQZrYT/yssT3/jWZaPOs4NnNeuD4Ipju05MTl3e8M7Cn5nnBbNM9gt88/eivvghZEiNBODGIOEskAwKP6JG3kWKnWCTDEX96AB/u5HxT5jKb7by1Xb2ze3BJnObkRMbX7gG5Hzfz495YePglYqDxioWBDgIuKkVENAWa7iakheU6qwd7c9E4OhqNcDTRkhWIhPcnHIk0p0i3kO4Beo+/AWNHnHKyW2f/4T+JaUwy1PEh/9JcpEEExnKkfXmu+BVzEWIhvW3KDJ9wGlzu773c36f03GvcJN/eCaNYjt1krvunXrj27MzM92+9TxP8hU99JsIAmR6pdjF3H/1obre8vnR//km1sa+P7oXpWfRJB1TBDKosrTCQKLnMld7cQBrFCawzVbvkRzGvpjd+pG+uV3dcnATzsFC/dNp7vLaqaIuri0CaGChqBU7DhSz0ufOnnztzVt6Hp8UJ339474/fex/GGffCtVPjjnrh9OnebE9/Lv+d2x++s7x+2JbhPZDiI6HQ1pV8+dzIwJXxUeredx89eaTfC//ayMBkX+/c1vZ7yxsgLkq16lg2p0AUZnV/vk/yB+2bgfD506dmB4rvrFXxLLbO+0vLnKE8PNRV0sWQdNTr68mfH+i/0lfYODyWeBcRjoSwTzRyTBuVbjas9/ubm7IYzTr7Ws8yD85tZKVlcFt7Si6bPQ+ISXeRwQFEE7hv2ZysJyq+s+qynhx9fMRNgnxDY4PnIwySIAfuVsoPNjYAd13u7xcu9ux2sjxLTcZZ2YDVcdXpYhHmMP+JQH1AK0NtB7whWhEO/QDvJK5oDAHSn3IHmzoaWuJFBv6j6pX45iPwuZRQ0j0iNxog7+1p8je3sXY3wOlaLgwMjGfVMBMicBMUEx1Ql0AxmLO1+u6cwRC6Sp0720lfmqkq60lAPVJFIV8WtZkKdD90PV/ejmikirxQMIOYbSee+mxHFFgERfIyY/3GQV93TckL4VZMpmFskdgJmJFtaEXCg8Hn060ePn+qN/fs2PCfPngsgMwIhgzx8ujAVG92dnx8oNBf2V7zgF/74PaaktdsbqG8Y5JzKray3Rcnp+0XOp5ulx5wV4uj45PZUY3L8guljT+5efdEg8+jozeW12WLSo70vIosfnzm1JsLi9emp4UcXr97l1FIbwM/8iufelmCze986ztvVxvaZmBGYv0sThm4v3Dl9IMtNu3B49oufebbv/ILN0rlL7/xDtzNwe7wwyTm1qre7U4dnETHtVxGFin3GDVlsCB5u0u7cIDC0jgaB8dbSixUfR4c3F1aem9xQT4iycdXY+o0dBRkEn6RqZISDQPgCA2M9Kgo69Ta7Nr4+FJF46UdEWbaAWuAxqockvS4ubLK4sRGKMhCYwoAGRswNnB5V8YBY58TS2iAcwMHDDAPdrk58wHy51DURz5cKFRZ6xtnkNLBvPxJp6c3cZ2PfuLTtLIfHR0sMLF9h5qVJtf3Ov1gyqH+Y8P0yP+3qTsPjjtN78OOBhrdQKMb6EbjPgieIDnDmeFw7p3ZS1pJK61kaS3J1pYrcdmRHScpx06VE5crf9mVOKm4UopddpXKThSV4lRZkexdSdbuaker3Z1r5+KQM7xBECRxXw00Go376HyeHzdVwXC5JNj4He/7vM/xfb7P87jLE+Xkn/y7K8WNkz8koFPo8HCX43sJYZLkkJ84BYm8yehqlcPx5dt6JUcR35kkU/oRXwKWYyU0cCT4unOm4X5m7zjjy1vb87phWudkWhQbTAMcNbfK5Rk4qpEMAeOrydjDLXRM0tpQNnGks7OYbpW8UR4Jhzdxl9OQOvM//RavHoQdGj9QxVDiMd8yYW2KWCp+nnFjNJJGEd7aCkRsDoOOrldRhUuR6O6CCt0GEZCm295V6Xq+t2R9TFt01KeXl585eSKdivDk1UtPx+lJt+vTgJKYay/U6lXna3Ft/dajh2P9fScHR4kMk6+HLQQecQ59IA5nujmX67KW9R3H/xDioj2Dk+jBFqtrj1arRMcorkKmZX5l6fbcAoACx6+lPb+qIwsSXiZ1fXYeOfF8T/HXXn1FSjDyLVqSVdffunuHTjSl3YSKse6uzZ1dShPseHdm+q656vUdJJtGOisnIcVnfpM7nsi3DeWynBf5vUF5lajWywrqGXaYO/ppd7Goz3OzLkStrc+UCyZcLm3vm2GQOdqdXFk1SITet24LOHNK0mRHMlmDl6XOTBtHM3QLPTZgR2IjxhT3q7M9f6ZkFJnZ30eTtfWYc5JpGykUgsPT1HCc5OtmtreWalUglY+Jx890Ricdl+J2Q8NlQ9EpFX5tNjWXM6096kVzmrukoTea81OYBBGh/nh/Dyj/SXVjqL1tpL2Dj/xoM7pSgn2oY03QTnBmmpvHkjrtbdOA+wf7C4W59fU6ak0qBoZYPSIrkUs8rFy0gIDXmwonE6HLjRyM7AiMa2tzfmMdO94JhlP3tud0UZ6uY+AYCJE916VDTDpqa6WzpC6UlaaNKNgfNOylNaN1Kw/AaIGl/X2tZk7kOxiwCf26+eychuh3BjAISgbnkdKsHZq4u02/876dwYh24yskmRBTwZ5VRVvItvPgOyDHXJtEsT9oveKB+U2gJ3QAHoPoSX7b+XSk5YpEUa+PDj6Q2Txq0pnZOLPBdGOskP3G668zuj+8fvXBytpZeKYnaTWQbl3rpy9fHD/dPwCQ/NNr13Xf+tWXXqJVKYp37tx13L/+6mvf+uDD79x7cH5wgGn5P6/eVK8HQdPwfKyQ+4ULp6/OLnwys/jyYM9WI6VVHyBTqD1ksE+j+fbqWgYCw7A/QaiivWuLkmaVDa8N9sHMH6r65p0J+vI5TVeKgf4b8BCdpswb4KPwdXTIcDQsnbf27hBp31G2R+32FrpeOXdutLfPzBydRKUxfjwzd2d1lVXkD+HshDcpScOk0n6hRfV55INJbNLjDk6hv9Bp7lNve3va+ZXx3oohyVr1hct8fPzZ/Pwnj6eDyhUjTYwaA6nT/8nFQvUGqcQb2VtCSB0JrO0YKwMtgNmiokWpsQyBn6DVfqKZE+1PxYV2D4cj0fEoev45DL8HeKK4/Ut8I4FPQ0ZcmDLyLaYgwIYn/yV/91t8Q+oiUf3+GjbANxOUOH4oSW54uuQyli6eyP8lUsZEuZdlCR8oMuxUrL+6MzZwc5/lkPJpaZVfYTghKEYk6RQyqodN4uNz4aPFT4L8GG5BeMihhfVTwDeUFleSam1qxVRuRnPOAbxbozBbjOsKGjs9Wln6eLWSevqf/hYGjvvzARQWnu0uDeXb35tdAGeL7ic3Np0K68GVMBtAKwlgEhOEsaB+72h3h1KRvpADtPr4NJrhfPmp82+Mn5LhdM3v37iB80UfKTb2hvhJGsCZpW4udX+xMD5qvnqvGkyQdGfBBKUD7Xfgp2ygRKm11UJie2+baz+9vMJJ0VFHudmlU6e6CiUCvLC6rHTf8DlPL5E7u6FNwLFhvw8XFm7PLzaaWzkjQesGUKjnEU4CHLKtmoNSmiOdjvRRb2cXeHepVjdB7JWzp9gAciHniXRhY5QLsRxIh/P1HbPOk77Rzn8Mp1ZKKkihwqg5PjXuDNWJ9cdk29VTw6OG16tnhj6jZwj5zUpkSz5YmHts7sDOlq2PyCZkq4WRONU/MNRVNHlcET9/UJ/I/d1ttUgbYXSDy3iys4v1VqtZ161+Z5vbgPzn4f3r0vr6/PraWl0+d38mssqHBu/Al/pg+u3tPGLiKfCUfABEOEaDpR7gu3iffIjGCJ1pvdtbW8u1jXcWFlZURUVBbDR+Ikkk27J7JQ+pRIDum4lm1McjuRgD0NeWe2l0lAM3tbz0cK2iQfRQNqLLPgT5GLQiRMigPxqMvNlI8eNsE8dcahpdhzJRZGcTj3RajUKV4PUsbNQkhK90ly4OnyD9mNL4AAAgAElEQVQhC+sbYOLxnt6x3oHa/u7k0lJtZ4vDfr2ypgWpbsVcP5UvYZYgRfrrRMuUKIRxwhUOKGTz7nqKOWBOlJ0FKcQZDo0WJ9a/O/70BHPhqPDcuTK6xZo0pyhMQuLOwuJff/nKie7SmzduUI6Pq7VHW/umLTqc0dgyVtvs5SBsyPrAVbFlPIC7CLyk5U8Wcr/+0gsfTNy9q6/gUcPMJoHL5eF+M5/PigzyBQ2I/vTDH7e15x+srl8ZGynm2hzL0wMDEwuL37t5R225HOW9Sq0vlxks5KerdY2Xo42z00RLSIY3N13pK9Mad1erojFHkgl0ytgPXh5B9Zr0o0NhJmt3Oq1H9OdPDDxbLv7J5ONP1upBqtNRJypF9Zg7YoYjwZCU0Tl6ftyRoSlEQCAFl7Jm1KQ/k2lsHz8YicSDg9Pl7teHBh/v7r81MyvXr7zDnS0ssDvWPIml/IF+TbR3E/DKI6l6Io2UoZAXa9YGSZNrWTOaL0j+cY0nZx4fItrubN+q1mVowt2MjmTMC9l086jrpGr9ua1ZeblWNFnzkwV87nN1ft5cySpORKJWQ83G7RNtH9+y8RE9OIaJ+g/Nyy74jn/i29hNInGisxNuY0mFpODW0M5xU1eyDKHS44pPNH783dXDDoXjHhUtMcDBZsQ7Ru8doRVRCYoay4Qr6IJOF9Vq3pO1QobWIxNKpqaKN8Tl0ZTpSqmoF1nN5TB5guSSjs4Fu9vB7NDmpHEoQxUFKZ4o3aqZ8aD2B41ws6y2u0e5TxQ1GgF7wELMry7fXlmZXF+DiwTBVuvAbNvJfEfqZ//ZPzdViEMdpd5eTg1kR073Eg9nP6yLiMA/M2YCajjDJs2ujlRnkr7ya2dPOsTSRXCFe8vgguavXX4Osn9t8p5nvTQ8+NTJse99cv2DhzM6N71+/vT3b024YCnb8nPPXNQnWQDBSdEfDUWlt7sHSwVL0isB0M8MSijkbHZgwY0GvoqRYXfm5gR8A3DrXA45ZHljk36XBBaiBC0yOkzqGrAvWaplCAUqtUJ/sU8ZgzKe+BBB0854L+BXdx4djfMTIpXY4YhsrJ2LuJpr+qdUW/twT79nIxCqB6ara5aD5u0pdIV3wVZjpja11He3ZtfXTYlJH+53dErZ5gC1/gqHo76Fu8vVjYcbG44Fx+dij9bUeXcBsyK6DPb2B09C9aNJ3BCeHbR/6NVu1EwFuUZGSG7QMBBtHGJsurbDBPhob/vW2voj2d144OigwEEGxZztLp/uM//cVKmGIiw5FZiMfnBS4tB1QU9Ns7CNdc3CeNx4nCEukQc77uoo6JNhhuC8FvYip1QK/Xw3Gv8GCN7Tng0wXbO/rhKit3Qfs4eiIz6brZiEVgeakGy6D1FUqrpa39A2Eye1K5MWtGJZcehymjNTq9qVHB5TPoIG1MZQLQdxU635eQkO3PTy4nsLSxChQX59d5ldpN0Waht4LIbbiPicQ4tvyfT/8eX5SRHFS/n6B+IfHm1yMmn7xMdzto/C+3K+46DHjyVnlbMfw+gB8rlUujuTHu/seK6nq9jWCogZLPf+2d37I8WisSdfujiu3u0PP7xmaBI3RTLW3BLDRz9dWl3e3tOGIRpnho4Lg+op4iSjOWhSf7CL0GzZT/V0W0N1mwubW+eHhqR2oOtnh0+8e/uWHsjz9V3zZzpaGv/gl36RXvvWhx9/ulSBnv2dL3/+37734WfzS//o53/q3MDgv3jzB8+MDoGG/vnHNy0msdEsYesw6ktoxHAkvW4A95RQ6CbbwW+juXmKL/QWTxe7/mx6YWq5YjSf4oACZjawTCN+g6Ja0uv7R2hg9Lt49KW+Um8m/cP5ihZh0DD4mDWP9QtYnp5EIY+TkgTQ1rIZSfS5UtdId+nHs0v31ytiCE17+CIxUiDRkbFPyc+Hs8wIx2PGhYi338lY7IwO7SyowtJsFgDb2ZGLKptoknpgONXDdR23IlBQIejzdpn1iOQNnCQ4sCyDQ4tz0Xa+WOwuFf3rzPq6Ghr7a2UcnJhzqdkMnRizYqI5vlfwK2Qi8f99HzYqTAF10puwe4ithKIQHEhY4kIFthzGXgbLufaD/oPRMHLgy1gWyaYWTcUbXc2ms8XAcz2s+IvYXGoy6JluzWbSzeyxWN+IOp1aEDcYyYRtHGgY6kTsV7PsF9q21jgN2RG3kMfyFt6UYSbqdsUWp61BukVXGBV/XUwCV8Zf9/fEl/O1aPq7u2OS6Fa2UBhUX+KERlFklgcGJxOUA+IM7Unn93dO6c6YyxqaObm8yn34s4lH6AFaqq1rLHd8WAATaEWr7r+Qv7O4+uvPXzrX06VBZr658dTps4Pd5dsPJq5Pz09vbLjBgx+8EywgZMe29KVR5VYFiVZnw7S/33nvYx5HMOdSmT/+9E57y91fuPzMdGXt3vwitH174pEioytnz0J9YJUmnNAWYmwGQFGr2itztGc3avKcK1vbPZ1ddHTFdEfgCDeQlo/pLtq4yxQc0kRM7XFzq/YC+VxG+aWYSNaUns+aM5bJmFNWb+wvyFkl7KE4tH44zHUQivk10qEa5SKgSwJKh1bn56w/MKecy2MWOXuEcnat8gCMs1lVKWaTolj0WP7Q806z/B6JkgrSD+ZGQ2f/1pfHxjRbtuVPuuXhBomHCB7OD3om7H+qUlmt10yklZcD3tImtCNDdbKkSZESUdIWIwDVxOtIM7VWkV67gM3S3j5Y7B7pNRM3G1hfMo2EHmcVOMC4hziUzIM5lKa3zlY1FzgQuvWDQfaaHkXXwE27Ez19NjcdB2/JcTOmhu8EC+Lf2U2qPVD1zqDxaBhABZfUl/OPjhvrh/t9xZLtCNsb/U1D9SyurQ0UOvvKfUIQOx41EzzTRkNm5dH6uqJS+O7swpzEY64lPR2FQlujBY199PtpMhiu1Nn1m2OnDYSSDRbOf/v2zY+WlpTpiVNVq2GVhI/UlMLLRHivxniJoCFpx8bhCQTdMbBXFIwmHOxEJMRk5/w9zrn/wjtNvqjKxIgHuswj1PqjtzVFwypqg2WZDs1WffLo8eTc7EgxDxuUptbE1UCT7mxLX759KOjezTcXVptrO7MC1UMc+QhHwN/SVlTmXuqounOgaeCvXLl8/dH0zYVlpQMlSfve4pvXbwAGr009VK1TPYgOQtpUUzmKnwfL3V5B8QimDFN0ZWRgY2v7f33zRz35jr/zpdf10P/za9fZPJ5NoLXRgDoyohw0P2QCcNg8liAWQZhuELTO5QdDxU7ckm9OTpPO/+zlyw9WVh5W65b3i6eGXxgb+/Hk1PcfPFZx7gogUfje5FrtxtHR6c4OcNMPFyr8brkQO5s4v1HmaTd9mrcVy9xoTO0cLdXrX0q3vjg2pnViEI6bUjdXV0XPgcjHR1w7HG9/DIsQfw0zHDiPquAk607gDUDA9lve2bn94fsctoCHEFqybZfK3U91FYyfc3YibRWVHAeaHtPpVGe4bxYAK1fK7fDwemX1aL3iTV3fPWh8Okf1SchAxNyGT2RtUKTcdFT1A5xrQFbCNXIqQZ0arsFXlVnE61HvQaVPTQvtEikiR/wL3/dCkfKlMqDTDr75s86SZrQy1cQ1k4XCPVTXHfFliktO+5/uyPVnstpxy36BMSGiGNi8B70HXG22Utmcn3fGwXQxarshJG1R34pkq4BINaKTy3VXC6J0fE3P160tx5OD6zgA3ik/59eTYGf1mBBH1xeLL46OeO15JDf2Dx9ht5pYvabTeb2t9u5u1lNvXv0YvVKzsKm52bdu3cFH/OTxgq60uHp/87Xn3zh38tuf3fmd9z/5zddf0Lj4Tz/87NzokHGJUgrzqxWIwa+9dBku9ObE1D49e6gIIBJoOjeYC9ce3hbF3FrdP1rm2+s/FS0AwRHRqTGZ8dLkvnptBkFANBBp3l1EFGiXQ6vlNPdW1G/40f6RIpoW6W8oIqPNzbdxVirIeQTRgoV02hCdYbKqTWynY04LR/jgtKjUjaSlnAnNoM7rgOS5o2yVxWKihQIiJt0rKWV8O0LmWi4NGCFqkGiG140iyDJxbKO6U990R0BVf77AILP/yAmnRKDlMq5Oan9HKTyvFnAvCuGz93V1qd8hSxSZW5M9+RLsI1PJNDNwo6h3bUkpUFAa4bG1kYHYYO/A/h5sVG/Nz2OUKndAvAG7EZbBUtFQ71Kh2FcqquoimswergUp92bWwYvQg1qrgqodBefBItDpICZdYaWOeVW+b9T2cEcBwY7kasgK1IbFeMQxZVO5DtT43py4UuOZtCGg82trD1aWSfZIqSwd/RePpzDbRN+eZKBYHCpEvoEelqNW4wqtEjNxlEiqOlvZRXy13gSc1dgA9K8KTHkaCk1PobMXFuEAEMdU01BEBm3OmKhNeZFMo+Z18Lf3FheXd7bN0aUF0HQYXZku80R78p3KOm6srfvXTOLKOYZeLXRNUu5Iqdmj5PfQO9ATGfLApEMPQZeh/5y+lqFM+guDxb/04osMgM1VN/DRxARdMLm0fOXEyPOnTt+cnX2wtHyiq+PK6ZPDPb0x2uXw4NrE3T/49E710JycZm3EWelyNs1UyDTQy/aa+G3xQ9OtCLvc/JdPDb92/rxM3ePlFQ0GJYG/ff3Tb356i5slIhzMtRoH/cik7K36uZ7S55+6iIn7A8OCNndvVdaBNuhn7yxVVVtxpM1IudDVaZ7E9bWalCjc0n/xpknjLIuzUav/lWfGTxTa/s2PPx3EgNjaNTLv165c+os79+9UNkz4em104MtPXWhuHP7rdz4SeU+uVBh+HpIjUIuJQw1FakI5Cj0wE0dMnOP3iHMSLZ7odX+kC+FCWzu7VK0BFlwBexceSeQOw+X/iff/ZPmd1ICm/MUl7YD/kdhQroTQG7h8hKVP+gyaTipBdWCCE1pT65DaFIwjSIc91dM30worjgqMpsayIkQOIl3hhFPHtpnqF5W6bJiMBgfQPb0LqYxnlsZJdieJDxCPIg8aVi0Go6ZOdHTQNrSz7/gYcFnkoRG3M+Wwe5JOz5PQ/PwZ+q1Bk4DMnx0AxRz9OYBcjCkEEiJZB9aUtE6KOnzt/2obs1vRstsD+YxZcnV9sfb3pBZ5kZ5YnIyOgRpJku2sdiO+UABYcTAgwJM6os3UesWK4c5FVQQsyIx4KGgrA6MYCGbAl/XjPDZj3kUtcB28I4P8nH3vhUFkNmrqT959+6OHsxcGynfmFieq20+PDtnsb92eoIJf6O9W2HJzaXVyo7aXakHz+sblp0Wsf35/etiArUKHJoKiWjqR1ga5eFvWkvxZRJdW4UIcfcWEFU2JWiU2ATU8QnWYgrIMHEAEZK+xy8OpgU5FlfkRx1hUhdOK9mHNmTU0O+1sAupBI4yYq9mf0Rv4DnSTs0xChR1Rft3WHj6CG0mU16o8UybRFYJZINzzszxcE0akjDSnM6haMHfcUG3Li+Q2b0q3hjsfk9PdSVAi/nUeGC0dlNgD9x0GAeU7mB+i7fvlbJtpDDqMcnBuzU7fWFwAZGseLac63t9/rqwoUnWPrgChnenurg7TT7Xz1M5IsBj2GJYNtBFu7uwgMm0/rKxJ2UnheqqV3W31U9pFCI0xRHW8xBHidA91YrEHxscQYhxp80n7Ww1qjniwi7p40v40FNXP+F2dn722uOhcwe5GFFPIKeEdd+Q9Ew3FQ6ZYkeLl9/LZtPOBnm1CI6RCgInK5sR7fcIaNqOlhU7/6N4d0aXJuqjNWnSgvpIBoY/lstpOr0cK0xsTY44kdbFdgb9IWZ8tzPNf0OJkJl7s6xst94L1OH0Io2PF0sWe3mG9UdvzfGHJg4VN+fg9xFN1eDQL9aICuOZQ7RPifT12hvOFxf2D2WoVdFaB+UZcT7cEtutA2kQegKQukpJ0JeNPmLQPUcBNYJxqJ5ABcPglyvrbWk/mDUGLqQnlrq4zQ/JE+aXa5r/74KNaffNnLp7ldOhv0IvoPDjS291r8fd2at+5+sk704vEglp0WLpbUxi+zDPeJxCMtBE2Z3VpvXpzbuHiQN/nLoyXy/2R3D4+ujf96OrDx7eXKnpJmchGTD2h7TMc4lQx//RQH1BexLlSq386u3BtuWoCNubjOlfu4LDc2vJfvnJprznzWz/6QJ8MfAbqU5QY7V7jTVNwzsr62t/43Mu/+uLl//mbf0SYDfeWD7uxtHKsQ3s01GuF5Uql/63Pv/Ls2Kgatdn63q351R9MPiLwhNu8mj1jHWhvuShyykuKgsefGAC3s87WOixt2B5aioUIjeZPvm1xGfUwFT5osULZh9YPRU/jJ/o+vuEPfj2Jz8LGhBGz0aE2fTdaIETWN2Ajsh3tGs0sif5xlEwl5kE2lVoMQZMbMjuvTSdXaoHYrGjkYhxNc0pO3jw4n1ezxhMPBzDM1fF0tGc/GtRMn3pJ+nmIre1K1JsI49TBJFUsvA0ufLvZ3a3oedH02xmMwW/aYAhhqafG8VZTcyWI+EfOoOZTYguGCLQMCWRUiCSKiMqSSDtwdIxmi2BRaTeHPlofcpLaFZroFahYlQGFkUTCIBgHJlWoe2JyeBYYvShPjJAXxwyMUdeOQ9Kig8fI1lrwQLw5f7htCSQp+tP6ajPOYyia2BUOIkVpNcEJath5uv/6zTeXN2p0xLuP5uSOON1hphhPDBb84tZW9VlIHVRSPXxD6jSUBv9ICjRW6ifTlMyh1XD9GDpvJBBCmBQKwyDvYaU8KfKlXiusHCcaegCjJxyomfWtGhqGnYDlQBHoUNZMcEqJhA8XJ9SJfrJMgcv7drxoQscc7enVSnNte+twb8dLWS/q2BmY29gQGJa1sbRSGkYGkSB1qa9fjoiKZHhWtuo2gLOgn0F3V5GzrEWCuFij/9X9gBFPFkuiU+6MOVDg+HtrFeTLkP8gKWfE1DBE4cLZ7h4kSIsYmu7oQAWp4O2Muo9it/77rIyOaRHuRHzXEMcwwEo2Ktuby3WMZ+2MNEZLqZIHblLWWsoQAiJ1slSkeqhaMZpLD7a1F3XyyeWgELGDydJITLFzvHsn2fNbo+2DvTUoebVqWIoe1wy+Q0t7g4A8vHW7PKAfsLKvQ+A7Oab4pXPCvYruHVpstnORCD9Pcqij0J3vEm3IZADiNV6OSegqin8yD0tF4YEZn+KnydW1txcXoDCIjwwDjpGNZk4URqCTa7tvrmFVRdH2LjRZkbB4XNnbl06ePtM/sCwbYVX5PDqmdRUvj43hIiwq35ANrlYfowNELqsVpgn/wrYcLZXRciYX5n84O4uT+izioIyUfk1CDZ8JuwNdpEzClwzHLdKGidJPNUCoEXLh2JnpWO65XaneMIAzcNtwJ5gKSIUKZGmAookuvBPhVCarUoTHhKTw/FDfqxfOP1qYL3UWtSTxHY4niwaE+LPP7n7/wfRaQkU8nc/85ecvYfRzfm2Bp6hGIUu6t1tB++6jxcXrkxPPnTppSJzw+uHC3O3ZpVvLq6d6esd7i//2+oT+haBq2q0n3fylUwO/9OprOvd4m9mluT987/2ry5u6OUYuMprvxkjUPbOgDzU9jXwjFzdx/JOCYAUT6WaUwQFpPk3RR/qkl+FvXIyV+vbKztF0bWs5+uGqaElp9fxsf+kbr7+KMPH06VOf3rv3b97+6MHmnvjMuZPNfuIaQzO4n0QlkoGJ4k+0eujo/99XWInwKPxbfP3kX0PV6zvwxBdOEILIVYTVUAnLEgRGRD79IW5J3be28gvdTikfmIlGCnUFvdFwm+vU1AQjtUF9cUaauAIE6b5uL5ziBHtRdci5ce4cEMiJAoUORFg6BCJq9FiuQ9Cpb25Mo5YlTNH+RgpFXno/FQ26iYFHN4mPNHrT4EsEUdjcOl0Cd5boxqhR0NiRC5lh4lAoAOtpMLmSFNndHf1X4hw5ql6S/qQo7JczsgWs5m7ykptTKuD4o2Jr51Hi3dAK1S/u6yTyVxy0sZ6ePl0BMm3MjOCDkgRzra+vyVHrduzFwy5GviEKTSSogpPmhZqapjdruv2gveE98ro8NiaoPG2J7mvP7aei86MyGtW4IUWGI538R/+Yo83gW3cRBCUeiDM9n07TgEbICvllV8yrY9ksE89LekL8wo7JZnDnHTwry2CE2YzpM9rXtEeOW0Y75AfGpx9Ia77NBJUy5Pre7KPp9SooX7UCde86HgDsrjSO8OAb8lN0sqRnYx2DSNAiwAIL2Nf+YlHWlIOA44H9YnSJtWBjVJMuYdE1UjxfFgIJXJESQZHxkG+hQMmkNCxyCLZ4sS2nHXCNm1Bbx6h1OK0vp09jGbAM9rG9weIPD7Rm1vsh35bXD22g5QQzhJz11gSRH02hY/vYGw6IKSV4WiRVfaZ5I2v1LeJsABSaqdo/ifiZmnqrZki6CVwDMiviL1JC9cT4wFYJjDIcoKODFVS6AAQwocw8GULGwGAogUpcWVUUU2KzVf2ABzVjlt211DwQiLDWmEp89XYmTBq6OQwMhscOBQeOR8Ssb15fW1vUPzLIA1FY5O04FToBtLW3DeEddACm9mdWKxKkMURXjjTmfetdWpuubTgtoWiiDUMjQ3qY5FL3mWJJe6KA9TKt+AwOEJdZt2fzYAkPnytn6LkSGOSrUnlQtr+5Wa1ZUAkQ27e24TwOvdDv8fLipwuL0RxGTKILdAwPcMa0spA2MGuIlj0SExaEOsH1jjlxnASWMlhbMc4oAu2gWCcMZvGR7vYl3XQlkw72n+ov/+ILL1reherGf7w18f3p+Vr0Xo3XCQyXMkoklkjzpv1VtcFoR9vLI/0/9fwVmNj92cckZGxglA/F8i6sLurp/aFesps7K3uS/U2OXEdz4+svPI/uvFRdCy3S1DSztESMTw6ILlqnltc3tqMPWksDUbVlvro5Y9CEUTDp1unNmELHnknmCth1xr9Y6rg01PvM2Que/Nrkg6szC/dq20v8FF55hIyqeKLMM0xdkualczlNnJv9nd1841DiAXbc35Z+aXTw5XMnhcfdncoJW+ZWl3908/bdtbpqAJEv1dvbnhlpb/mbX/5CX3dZD747jx9+89qtW/hJx42ebKY7uvOHb2nSgJrkVcSESHVFYUTizUcEEN55YnE5HDblie4H7tDbSWQQ6Dt8SXsMMu9aGkdL5YUjLtCU5g2LZlHZC99yMVhTmBCNuri6KkISEocDCqYXgcQ9aK6wCdrKZrPntbrq7Sl1FVVITK0u6/PBnecf+JimMXQTjPdsqUTPuAP3bkYQv1lvHEKstU1u4vRQfPyhi8Xic6b+UZEJe0KMy2YwoEAzZ83d5bM4YYSltoMj64DvWi7JrQ2mBX0GqAJkNkRBBwikCW0O2rKml2u7zTHhYbB11rMn26ZRBxqciN93SAUuGdhV3GZnkxe3DA3/yp0GAvtB8Qeej5hsu0lVDeQjMvDK9hwBUhRmiYgcHiJBkVIZTp+HgbMswA8O3CPOK+I3FUqNJjaZH8/3N5LzJOjV1JMT/+R/EXS4a6SwPauhGag1AlT20f/wc+lTOFQC3/vcPrZcNttN2XUHEzE6/NEQwdURuzWLRVBPuK7sjWYPEno2IJC41PFqbePxagX/3arR7LALYfIyzz+mugdKyOxEHZAC9CQEGyqZma4yglnu6Cp0KZDzI/q7a+qro70wxb0cd86p76/Ua7ZcOzheB/1BH0Hl+sq93cVuVq5a1ddkT5KIoEHcBGg4MCTA8BbZGP0JQBmQvM0tOHm006kf7tOJWtl0S/m2tVsHgQ4Yh99teheN5o2jAzLbwMJrq5bJiPhUsfHsFms1BEfoP1CCh8t2lppSfR2FE8MjwHE+ssWZqm0gvdCJnelodk/ve//1vZ3ljSq9I27lDyB7RWkV7kE00Wsyv+XuWoX8nWlvR1QZ6ukjJfhFcPZI/JmcbvtivubO1o5Cp4YdBIKJLqPYzd6HDLNG+sftiBWGcx0X+gZMOTYxzcOz0vKrXufx0uLk8uLj3R0pDe9fCQsUVAGyLu4zuR37sFMHfPPie/t02zfjQW+vQQM1G42F6ro4RoQ0s1a5Xa2qIdBewomWJjnXK4OQk19mp7W/Zn4QNCBCNze3wPG4QNYKLO41DQ5ztCMh0Bz8WjgGJuj5fH4wk5nc3NByXEbAeRATONB+JYobZzFhkkTFTbB+qAdpa0BkTyZ9trMdNflMXy+dtrS5RQ++8/BxBfbW0sI/UuAtaIthR5HYi6Gh9DXVpW0JYyYr+7kTA7/5M1+x1FzQREs1HPy7D+9OLq/fWVpT2LEeU/COBnJZExeNMGg/3m9PHT19YniwWGK/p1Y2eGpocs5tRSZLXBnnUISR+sLFC0bCfefTm58sVjYP8QUCxt1UbBHFrmkc+eEOY7OC+WdaNNUvzwwpEuYCczjCdCtHhHJmDDCmny7lp7b3Z9ZqXx8/8fe+9Or/+N0fzqxv6hX8q6+8eGZkJBAV6RDDPjdqf/z++/fX6w8UagbY3TIiWXi4++uvvTRaLv/JBx/OSTasbZq6Q132qdvqyqlXAEP9zo/eM4lzZu/Ay1qfgNhoeosi6yZK0IFjZ0cgEtonwSJC6Xu80NI+4tMRoNhazxBI4pNKuuboeUf9JzYgrhe//GBwW9KGow3C+Jtb9KS7V6vZa58D41JRjI5NYm8ozXjOjtwbfX2vDg/3l2mMptWdrXuVVWrV3Xc09jB5aXeXdtZGBqqpkQlwNVFI7tO8jYip6ZCJFHu79bpzvydMMB09xlgyD1t1SaCszByyYl0sa7RtC2Rb3bW7r0ByDGQXVTNF6HlqlHQt29+Xy12twet0IgGyKMwIeM4pk+qs7W6bIaF9PzI1WlFPFMvwrtp7mptYC30IKJbg9XEr9f7SHoHFxUM9PNo4Ojbc1MJzjSwCK4Vy5LHxTce6ild6+nDzjiSAEzNgQAXijGRAoO7oHcrDDg/nNzY2a9VsDOBqmsKlMTwATYgK/S++9YqGCgcAACAASURBVD2u4w42y8EhwMRsJy0YYWqaCjCl5qTb0WK+U6Ur552vSeHTTTS40EhQFnY8whbCkOw5WQN/K+kMtu8RpT8xOz23vvagsoZDRZujWq8nAVESPbBFx/zogfZszPLWrDGwDjSltBJfKgPeJ+pwBuxzhFzNLfpyGt+oZ8ueKhZVivBonccbR5pWjnYLnZu08HTewmJbDNPqGeTopsv5jcm6EVUklAPeJcjCMoa8Mq1ULRZ5KwywIfbxKN7DCwnoKhvK5XZ40XQZBSri8frCHVYTSZOuZxJWZNeTK8eQ2SDnEvwmcKS1ysQ5gIoa43coXDiS8zfRu11X3qwYVJhG+YZvC2lvbtlT2+IUHB26JTcU29JLORSuydmPBxNLBYU/oA5DKw1d4WdLSoPjFVDAfGk0tCg4DonUz5pRZBU0J+DyVFjclpbzxdKFoeFTPX12x2BEqCgShUck7ihffoDjLAiwERh6lc1NDxabq71PW666GaMFuF3wIt2c4hilMw85lY8fguolzeSa94ktufQe6Rb5W9QFjsWACI4NOjqE51gPQRLVw7vB++TacezDeHNnAu9LtEE48vElc0NexTeKbtldj8kO0QVogEJMJazoqhpNkEsBmXD4hnRoMJmphsO+TPpnR3q/euUKemtvsbi5vfvB3Vv//tpnCwcxV+SXz43+wcT0gix8aPbAnrlI4nMzil/u7fr+7Ap1zG8fyrb8t1/7SrnYS5FpTLKwsrCE0buyPlffXdzeXeeSW7Twk6KiGHx0sb/n0lD/+QFaIvfb3//BdE1RSyAbfnlvA1olfobzbenjo1+58ty5/p53Ju5/795j0I6EA7v+4ewS8SV8nCF5aX4TwY/Jl8nwW2EdDes1Q0uGj8fQx3IJgFydiBqp+Y2nTwv57szNo1eoV/jpi2NXxscF/9Sn8/Vo9vEPbt65sVydAYA3jiVR8k1Hf+nF588M9H/36tWF2hZr9MxgL7fv+w9mX+jp/K9+6o1zo2OfPXr4u2+9pzPRDAgj4XrZlPDBAzpLKeZGQTlXwvxouak9S2AcGluFcfJYHjVkK4Db8JQ9d4A+fjI5I04Z4+tT1scH4A4OY5zKCPh8L9qW+QJwkVGnUigfH4jYMbj29OP0zh4WuRPR29bGL9GEXJA3XiyR0nM81IIGFvsmvmnoy8ruNonO96XYHVgVFYxWLtXobQ+4ljJ13UYmKp/VuCRu36HgnqPm2FJWsCCnH1wA2qOvcCTUWju/Uve6vNCZXkvoTNt4IdoRTkKIueqIXgSEp0ERQ0Q1GeSMmtSjM7mol4IzosefLRa72N3SIkpG7dOST2TPeEqts50mcCADRZ8UERIwzVHa25Pn5yLrEKxruqwABU7sm8zLQ8PhdotCknAZQaNXtR3mTPTE7dCsxbL4QY9EH6b+yZvvOqJSdnYpFDzUtb7BRxPLMF2RFQEQ4Z9sbxMzemG43KMz/ybfMxKzR7KpoGQ+tR21k941aDNNKX3AZ3TZBcdv1amJNt0CpMg9VlLSCdJV1qR4oZjLKx5RN2TJCDc3lq/tSfg4bsfpdmyEYDbY78wJpYBhQhvyjt2LTuCDWxQHgT4FvLBJGEogPPEU2+EXjcmS2nXqT22qNxUph/xRJTQjEQ0wjlAR2KABWCmCFT9uz1CQorI00C1iGoFbUzO4zU4bE0ZGIRxeYbi7PNBVcFb9rOfk1vhxu+58ysfawk0tEnVcSDIrYsngBDMY8j1MlmIirB8S1qYlUUGVrOiSVUskP6UqTRAcSyfqync6gPPrVa9NWxE+/cvk2dWFRRtPK+ax060gJmieOMmYPQZymxplwqNWvM2jIbShEEjFg7D9OP9aWws9IZZ296wjsO6l/gETEDkCZtxHDiPbRtREdbI+K9VK6BGmSCAF6ULai9cKvkjtEF921zATUoVMGtqBm2hNzX0K1la41XqP8PGtu81kEfWME+oxlSwBUNgBoNbsKS1skSO6DdvMc6Tw6ZBjnTB1zWeHL3V3jXXmYN/egUtxuq9XGzdNnxa29+5YOLNW3OD4eLwzj0n61GDfF8dPEyvcZ8XDv/2Dt6+ubFw+MfKL46f+u+/+0EAxQu757Ts3jTyfLuTw92fqYiCGs3kkl3lttF9WttQRMLrOs7Mb+nbsmmcpVOUBxOEO5SZP2Pjq+KmvXLrozzOV1e9+entivaaYi2iF7xpT8AhFU38uK1x4bqT//OhJ8eInUw/YwlfGzzvb//hb3+ZobB0HQymkk3CH2xuoil+RUXUWoPCulkyzkXwK7Zn4XZQlTEDUUqtW/+7nX37h3OnrUw8yx4c//dzl3hKtEoN3FN8JSv7vH7396XJ1DTsv8k+t/ZnU3//az+Ol/G9/+uZBtn22vivLvQhqPDy80NUxmG/jFEOXt5vSQpBVBykJPmyjNwqF0Ny8WKn83S9+7quXLv43/+4P1o5SGnVGL5tAcTx1WKbk98jK2EY7w5Ei98Q7eb/YdtLi/BNyzK5kMQPa4g+5LxPI23WQACaBSkeFmmlxGSRgF+wMTnnqvnYgEWoDYYKEwiqqgiE0cOkT5R6VrSGMDeFgm4MROYCuIl3MrsChIL2qYYyHArxG8+qWNJdO2i+dbVcCMtJdVpDlOb2Mr8SShY9CRKWjHXVLKvCm9D22vCNGOMUil6eSiZ5B+oLg+xhDwmCIZ4DY/ko30jBQHAXl1W29Q5oHuszuM6V8f3p1ScVDtalFlQaY/rmevks9JW3b5jc1D95e57KJleMBDg2GwaiuiwePDtX/M5AGuvFa8mJ0hMO0CbvIRTvBlT+Ihsoalozn2niozOyTgU4gd7ox9a+uTVBwtAlMHOeSbqI10AzW9nYRTroLXYILewMMdKiiUdERrEMIsc8kLW1umGJo5ip6go1Bm6X2NPqhntyGn3uyp3yyt8/xBgcxRGHMQ89GPEiSOEfEHBbJrjgAjhKPz7doBwBWRPM0u9NFQScVzx6DEIVaoO4jGopI1GdICyHzPzeizpM7hLgRMuojiCt0rUIJQEGYhFDuPkNVeQvnRyjDvAgJGAGCImnsUkTdmYGhEztKHFCOxgf0ExNEtpMzjkiTlCvbdf2tVCrF3HlZDQ2Sg+B/ZBCrudreSzWZuYY6WHkzWx5HSIoyHZ1kYA7xaM5nVC+FVEXgls3Q1k4YEg83je2hbb2ew8PyM9pWw4t7VovJScee2t2xzWCBFGRMa9/13R0+mrpwuH8QJxjFpoauErMEBUUbnUVTO6k2BwkQCcPjWWTahqNAuhP7kyVI2g1BjZt4SWSOWHtlT4Lc+cHkxDtzWp9tdEUviswubLer+FSxqGzS4HtvN6taVTeRCPAPMf3BNWhCWPYquSKFoEd3kqyjdiFLTBphgHvYd5sS3lOLcQvRccHWAn19Me/e3iFGm3imVNBgCq1tuKtDUP/GM8/h5c8szd6emV0zT7GyfktRfBQWNa9v770xOqDKDEfgVKmgs/fK3rFVmqvVXZlUIdVwwdzISpIhSyoaExz4g9b5wg6Nc1UAsVJ9uZgv5kE4b3GujH4jq7R/UMVIXpN4SDpyrNBu9NJL4+PPDQ/8qx+8+9Zj5QtZr0nv2T74fgxFyKan1jY1wfVTJgJZ1d5sy69euXSyr//33//wpuqDKK0nxQmUFT8bxoPCDa+fTHu15GlVcceKYTcih4Ryiseg/s7mc6+N9E2s18vtmcG8hs+Z0XJB3oXqlHa+u6B5VD0mCTsEbHBziim9PFj+bHYJrxnKXzVyee8Ai5JW8ojIUfI0lsvsHSRvrkPyMFYo9sUHaD29af/a5QtvP5w1cGn14GhTjwJkofDpIwfgaQnPE/w5PFl+adRkQTR39QqOKNs59z8K+zj0GtZN3CO23nWapO6ZQzl2NOiQ5CiACp5IAMWINKZUHjY2klmBiJvDCtSpkVSzRjHVvd3PDCnY20uLrX2xAZEgpwEOjXz4XE+v2PrZkyd0rPFdj7d1mEKrJ5BOnJUhBMQeFGksRnVnSzyR1z0bpBkWyDhVveel0P17RI3ATHk5TqEjbFPsqfUxOIIfLLqlJwmYY5ssXbxrYA+JibQ8IFxrCAf2C8IRQ02sNnbGzg5trLjqwdaWWK/j+HhMz8SknQOvxfo4FJqr83QjM8lzOjzSuUDqAga125oxgIlrAvnpET1TR7pko5Du7bdgVIN9Elfb1ogBUn/j9/+IEwkC1sLFXjnkAYFFnWda06hHc48RS7Z1FouWXkcm8Unr0S/2o7pZn1lZqRztI/ygonsZJKQQ6DbtRFChCXzwx0Fs2pUx1J4YsURJlEwjfMZIJnGDDEHEOzFhSrM0/nSWz06zq8fSskbq3FOKA4zY5VWHQk9y9BF/Uu1xjMPZ8xl4sAm3nsEH7EFynBthpgQ+gaNBu/Z4cBausz063y5WqxR8NLTwxIkdikw1oW/N6pX2ZKKvNY3zF3o2BhjRfVt7cYUEhQR9tnhU2IuNdymYEvPFlshwCsbBZbQYV4UjCV+i92PHecIUmcBZCkEDvPqmLlr4oNyhSOkYBBjZjk5yT7NjvgqENVL1chwWOCDITcW8MxLVwlGLH2xH7xjUZQwE1hxzua5+5QCf8mQZdpRaiYvIyuznZXQD3IQqR+pJhxAb3eN2TG+AYZaAMxUAustCvW4sLU6p7ItEaxx2WXr5K1mphXpdY2eZg0FFYR15IM+d2ZmP52ZXYxzvobI1PUT7g5rtbcLQafZCkCZWVz9bWbYaI3q7h3lDodkVmggaTBGwzIu1zWgGoCyjNX2+u0sg/+OFlRhwiGUR5GjnKtgRRhiOtcuktXxu/JxKMUwk2+8NzXnuyuV1Dp+YfvR7n9x7UNvClgnp10bKmwkTQ3GEg89TgszS/H1trUv7R4aZUFBMQKirZOi0QxjWKEhETewNZ8hGA4mdafvii4Npvf1YADLhjEQEA976ytmxs+XOp0+YXFe8+fChAOuPbj+8vrru/NvCYmvLQFumlInKo4NGy8L2rtYdUpR+FprRJkbSTKUtt6F8mnVOoJLY+CBVU5sezfGJ7J8vhgjaElM3Uk0QVAk9ohy5dz4vV7pFt+3tL40NvH76xP/14Y3fuHzuuZOjAO7PpqZR/WZrO4tymPg8kTM3tyNowSIJygcjDXskVjveNNwNx8iaJ4tBl4eJsS7JUQsV7LAJ9851F/7+G8/Djv745n1dXr47OSPf6PjFwYxxUPH1JI55cib5PWwqV8+rAYqfnNVQ6zaSmmTSIlIMk+YXt9pqRzTogPDDjo9FqxQLqiWSzzPFTgPa9HtQEi+zVYdSGHWn6UXQK9nFQ3bOAfEKiR1SapPsVpgbKaKAmRh4wDoSHQ0jQgzVr9+Bcnfd/XLtPEVqxsJvHeyBz+c2NzXFQqIEA/CmNe9zKAzewJFDqOVGcB3gQ5x9c9akGJE49HsRQD9VLuvOQrN5taWNoOvAq2H0FjaeJBIezfhFkm2PV1aWNI5N+spwQwv6d7Wmq/T4AXLmsb70Hq+zrR2xJZQJkqveiEnRErVFrPiL4nGeut9lSbnIyG/4IBaNs+teUUfV3OLVRE7kWW6VKkj9s/c+feIIo5M5Kmh4sQxSwbtmuxrDojmGBjzGzgen384tVVZAMTvRbOtASBWuYnfPULmHU2ML4bhIHdJr1lTClpYJkkkcKH32JSqDFMWNtbXiJr9o7/D7gi0bZ5FTzr31YSEH6eC225lE+AMW9HgUlIDYy5NpS+kFeOK+T03Tzn5SLOIs+bs0eqBxlhgPE3J6aJZ6lpGjQOMHTQ8jpjRLdBg+lnJhh905Ss89iCcPUxN+KPSAA1sqqP7dj6pTTWaM4iA3ykBiBFWoBjshUeaRQcpOCKPi4oLoLYUN+3urW9tGcUbot7uLgrJsJHhMIY+gWNynGAr8jcfP8Xe+eEYmeSEVuKZJivINXpe4646CqUV6I6JpapakUfQAmqMerBWP2y3ZAOAXHxMZQMtGd8Tzi7f0WrAhQB+gKDnbcT4TxJ9u9YPcYTlt5a/KIHXdQfSqblRnV5fNKC8Zb1IoqCw37+l8X38izVroZ62zjb4Hj9o3Pl7xekupnXlphTst7u3iXJPUU8WSJxfCP15eIBJCcgr9fLlH6bL+HQidk8srSo3UN/XkxMIxVl5Dq63QA3Eq+BNeOWp4rCZVmOQn1OK+Otz71155qaujrVzqm5ybvvXg3itPP+vVTGx+MPvwd9+9OlmHCx8s1be+euZEf779X358e7AzH4FkaABeM5+hgQuhZM+OezySLSHPSETAYbVIBU8iOjoEiQ0KERwUWeIwEnpi6hVzHNkgwXJQnuCWKeHkP/zKG2cGeidmpj+dWbi9uKLh+YNqAM1UBpU9mG29MtD97ImhEz3ludXKu1Ozb80uKvxxSX40wjgoSqrHGQY5hgzFvWQ7gGaAQlosRgiEDlVg0dLcq2u/gFTVYKrls8XVyc0dPpbPmzxKOGTOWS8sOjnw//w1PRrKH0w9vD274PpUiUOSZFDFW0eoCnEMTcugD0NNmnYX59EGWPGAxR0chTvJSNE4kGQs+suqwTqksz3tV8dP/PyzTyuQ/z9++JY+EEBkSxwy5qfjGnEZwhaXjmPstwi2KBhrK7iGilh8x98tCLO9eKL3lRzxZMNXEig3p8u5ttdOnHh65ASdA+aFgt6u8A8AkHvydjHKIquqdu92vY5C5uGAsEYkcnI8sjVO1jOomcy5/YrHiwcJecDdEpjPBoLd8oxad8SnbLtKLglEXq+4W49+eA4PwVrN7+yKmSg9mmplZ3d6rTK9IafbaOvIjXNB2tpAB8RePyKhXkeo9nR19+CD+xOS9+ODo8oqKX1IgKtj7izWa8sbGzKdo6Vu67lUrRhHen1l9X5llX3y5LKdwGYBjUrOQ+nibJsEEggj8G4rmLAVAC0kx8FnKqQhZUVahfhspzsFTQj8wKNtR9ygjfVttPbiS16jVYlgw778759NBRiSqFfx+xP9qFZGVap/JqEcWNwJG0Yoo9NufITThskQ3qGH4UCKkvyBCrPKSSQa3pJ1D60drn8iDEmLYM8A0EiOtJDoSShDQnwkPuQ/voVH5z56Slpe9fLiWoWnibLSWyrDoyNsUQAV3KdDCtQ8d505XCDCPf5S6A6MHTWhQoc2DyQKo5dBdevG5JoWRKaRixIjF3hXNG/IeBevR/Qde1pAGpPPbsgRDk+zAKyRiqalgd0fI6tQzcyYBKwiuHU8mWTan6MiFCBVcAk5FXexmMsoT0qWgKhRTNtsflYEColPSla2981pjwSANBGhdA5dxGJSmtw+Rhjf3kJx6zw8V4LUek0OIwFC3gx1T7Nb6DhCoSXZbNA/v8zJcX3bx3KrOJenGsp3aAuBIxSUsjggxqo0CQ91VrhaUfFziB3MHIppjZN0e9Af/LQ52wYenassWyOhiaqIfEuTZpY60O0c7vsxYYGgtdguV9C+ZB79ppHC+cHOwvza6l9MzzmNEUQLcVLNyLh8tx5A58EeNoh+SV85Pfq1C2f+xZvf54Ge7y9/8fx5r3Ht4aOr86bP7PNtcV35whL6IS1Oq/pS58eMdenNTNOJYuH1C+dPDw4urK1x3/rLfRbhresfv31/+lZ1y/jck8U8jhkishIWCpF+IZS0HuFhv8FfhI0XXDZrIhPgaTQiBjw2KUje5JETeOefLLpp5DPCASdb+MqReySc4f4HDqMoKVp9cEgYD/Fupb7zt77w4tmh4f/69/9EwMz62qlSc+Ovv/DUz7/wgjP//t2795bXf2D6g8jYe0XQFfBOojf9PyHyW+CZdD0PjDDZaKZIE0a8Dhs3nM9+/UXgfs97d+78wbXb68dNEonQrfFiQb3breqmciGnF8AY/Qj3d6WpbBCXwHEGCD6JXcCpw1355fo2hhzRIqJmzokj6OXkeRTQNVW3dv72i0+dKxb+6Y8+NslOWcB4sQPBtKvQgQb2aHn1s+npl86clnB+d2p6sR5DJiwpJe7su1eiHuL3sJyJ7+YPuIyXe7sUx3AE/WfwX/goDYCnGat7tgDINtDVNWQozf7xo0qlsrO95qwHOppWeoXUe6ajXTeXua1dudkYUxxlOYeyUMzYaCZzoqvY11k0x3hRu1ieL70k7dfcPLuzq1xL0p5QsaxAKt5ADDuh3ZPjQzKUs6CEUTsS7465Yisal9kY7ejg+oCAyE4cbVVyyHT7u9aZCuYwcRy9X/TziPRnzRCn9UaT3hVXzMruyJmCwCWXG4fVODXTtfralgqbmJTJk5hNxgKe7Sy80NtzmhinW+YqFU84VCqt726JPFa3dxU5R/Pgo4O2VLMeuaJ2bcQW9UCE/h7ElDTjHkG4p7q7NYK08OgGogpJL4CQJSXPkrXQRTQfVpa3B9LgCQVF5R9+752wkGESnQ/eMD1wRIloKOqVosKULaeYEtiUjOLGOm/H+xrA5TEgdWuRjrCC6jdVgdloB+zJFyOW/JlAh4JyeUret0Jl2QA95pR1JrEJqFRuO0Zn7EmZbrLJQZcOTCYOq6Y2FCLswl0CkQz73dSf79TLmElEofYmLu4Y4U81YBEHO5YlSFSUiGAjmL6tmpsb4euc0EQ6LpkeDHmXG6FYtdrpxcJLGGygN2NejtPZDjXnuqce7He5Iddp/1BfM8ysqfXNpXWTnAxKimrkJBN+iL0XSjVwzwToExbAtakZw5v4raxSkur0fy72ZH3E/jL1Qm8/JqbBFCBGHpjiJl5w8I5wHJvkKGgQuSZxQIRCvNRjNvwQLcQ6hNMU/j2bG537hDgS5HlyHNTmyOeXi0UJHybblqMfaMVj9RlFC6UDzxwYKm7vIMRDce60Jel0RBnFKLXLjejjxHwaI+dRbCcwymE5PhRXrta3xeUj5jbwwQy/3tvhDdk1HqXHiZRGONfRUdKlXZ4SIGbuRaWKGHD7xzrzv/Hc0+N9PWzxtQcT58z7zLWvaNlfqd1bXkOvRn9kqyw+Lba5vTXQxV2TnTpCcHJ4ur1e4/Av69bZOzi7PAe74Brcmp67X92aIPj8stZWecsI5pqOcLdD/BIJJOE8uACcQDdBkwhPZ3lbFZ+eH5pMBH3FKhFeZ91LP4nVhFIRCgDKxJchwk2X+szdPny4USPt8cnkPe0FMRDW8KJX4u4pJZ2MB5rmJZuRazOteqm+J8m8uH+ItgHu8EnK1zolwhPovy/3pb4FTHAeWkdKUM+fvlxWq3OPQUI4DabRHTS16MGggkwyEBUm2uEhiUrUA5dYL3VwuHNR/RZPlaxAaH/3otfyqeb/4Zd/9t9fu/HtiYcjhfz87h64ElFawwBrHosT+blD2t+CyCgwH8VM+uWRXtNMx4cGpXWGu7tXtnau3p+0O8VC50fT85KcTrSw2sZFXwAQ5V6w78AmIabk+fDoTDH/0kC3aRwDpdJnkxPkTxsMx4ciw029u7Syut9oy+UZAMHW/s7OJwtLj7ckl3AUfQU4rAcq/vHVtXUzEs53catTiPlyA94SBQDWzgG3EvbUS/VnM5d6ekneJytLpF2oxAAkEmmpOH0W44kHFcvOdIU5SMyVTBWja7UIP50ILWQPkAVdGcKp0N9OEGm45UBrVvnR7M52TvEBGQXGCvsoJRkj5DpqCZpy1FAAEQ5fU7OmI0OZjDLmhYCvZeXCm/BIzMkrJ0+9oKG9tjq6qBwbFmT6HkQ9oyxDxSuqJK8XgKBon39ASdNsHl8BhB7alLY4VlwIi1DL2d9VJKebZrV5yuZmeRccPC0m1W9i0Gm+oA+YmDv1s7/9u0rjZDmSSQABznEyMEAifPAVib5W5AHrLlpJ8hu0TXB13MzRJ/aOtFUgYqHK4qw/+fIXKwDsiXcjBJ41VFWoyCavIdXuJJISi1WQNA84PmZfKGdVG+A0+hHpARjIWFeBPkKfdGAU03JyjWxV7mugQfBBt7bV+OFD4dKfKXaNFuRqsueGhp49ecoTQd0hhrTAf7h+c35ze6zYqVWerqU6lY4PDKA23VlcnlqrfuXcyUujo7EZxoasVn7/4+u311AeTSZRKBu6jHAB7cBGYR+lcdRzh6cTEQzX3nFlIC2G9w+NGoMMgUjao0vOhK8doRV6DDAnLGqaxySQjP2LJW7mlMmgsjciI/idraLWLZzrEFZ/cH3L6jEsX6KS4qd8yR/o7mZFSQG33UZg6Y53dQExEZwFxQzYiqrj6GwRpYOA0QXgNI3u+rET8UVNeDv/6hawCLsYyJJyGwUTOH+hCGj+qKp1JJwQQZ5GoV8Z7u/OtHz74YxeEcpEdCbgmHOgnHOtzciZkFCw6VLOU6j+5GYCAk9Cr2le2OW6B7uvjPT/J1/4fNIvIXpxL6+ptdxJZ/L/z/sfTGApHSvx2/1PX76sPuAPPrwKBhUIS3vyXs357My0kAzl3aTXXouz6ofHGKlVdS6Rr2soPVeCR7l411CyoQLjMNg3TiMEwBLYmoj8NE6PnfRPML0IqfyyODxE20rKY4UTN8gfQowD6OOM08XHnNDwsRL5p8jpHkrQXiMXJnBkxG0n+Uf2a39fFpR3Ztc2oslucN1sa/Ig/Cz/BePHrS2UiFACA+bQl82cLnXoQdTbkR0q4762Ltfqc9W6UO36zOJVLBxdUsil/Q2ZCUH1+IlIIIN4+OAkWHbfthA+w9JzZX7t8iVG5V++/YE8TaD/+/v/4PXL85Xq7919KLdvc3zHu4o5IoDLtqIEnO9q/6uvvHhqZFSjjoXKip6vP/385ffvPXjv3qSqaYlRrgMi4f2V9UXUt4ST8/yw/kbNV6fnsbDIhue0qoNGxvcYKnC2qAm83Yysqff2Bk2zK0tv3ZuYNCLMtpFb/kRLeqcpoPAZM57UPR0eqUIlh0leJ1HZ8bMR72pufDrXMbOzfXODS+oDti5EV7Uq14r+EefbIKvDKFO9ttWRSW4dwhF5CU/g+ZK8DvFA//dswlx5YyK9EInr6PfnsDgXI6YS2dL3kAAADYpJREFU7e/frNdpmTjjQMJDMXH02ICQi29O5Tsv9A/wkFY2Ngwj0mpi5UAHs32Bi/HXSDtgGXDiso82NXTTo44puCY129nMcF8vJy8sa3vuxZETXzx/QSMDuoVvwnnX/v3ao4dydRBatExR6Wq9rlEN+gk0WfUohESDfRgUG+fV9OJs3t/VvURd0QqUIps1ZOl0n3ZQmdR//523Yk+iSkioHp2HqXcnill2MByZMGeh5C0UPWgFnqyTxQqcJ3QeIQ7cku0kRnSOSUBCiTA8zhbeVbGrS87zwcI8eJ06cy3V9gnNuaXckcN2ggVPr1UJnQfqL3XNb26ROZ1KjMZWfDSaz87U6qv1XRUAxv6xjc4fGTXLtCfX/upA+b3Z5fFi51974dL95dVnRkaeHTuBy1Wt1eo7dcOMtLadMnDx7k1F8CrXTvWWsU51NpXJeHtqJrTj4dGLJ/qNQbepAqNbs3M/vHtfYzL+uTsZKoDaksukOUcjBLe3PFDq+vGj2TuVdXkziG2iHUI5WqGI4g1NFalm0loqcoSBJ8TolZG+C0NDMu33lpbMBtHC2tl7bnRIFsFb35ydv7a4oj0GrWF9WFa5bILCL6CdI/aKJQ+RtZfOCaYC5MfyUt0gMzZB7gvAunOMoBIOjngZe5Tt4a4GKdOL+Wx4OrF9rulgOAZ2jsD7/YkDSwzovjgPcQBMSvkJJpt8MOyNP0B+3ddbv9ZT/JmhvrvV2mTVALjmsVzWbFsxjX+nphXHTpmis3+oMwmZiSAyzEBE356Zfypvg22p0qeYTp3vLvzUc8+M9AaNLU5gU9PKeuXbH398Y0WvCI3gmy4PdP/V1142h+ftuxP/cXIWBTNkMkqQZPMYrQAVvRsVLG0AOOLoqVpXAR+nnU4nLpbNGsaveBTv7H++4mbxW5z55OD73WKhhIcN8M3YXKuRFDFh3DqxMTYgekVYngCsqW/YBdcEqhpzYZJT4VC4KDURlw+bzfOjoTj1oY/iXEScR8MkSsf9kid88lxx0wZGQwoqxfcsZ5Ru5C8NFC8OD6r+6+nu8+pS6PceTfJU/sPtR9eX13EkVFPGBb1FnEfq3r1UVkpfp0B7qnA1h3BP9cb145izKEPwxpkx3Lw/vn0PcTPio+NDyUIXURq4vX9kmMXXhvteGz/xe59NejVjEvicP3du9JdfeqGrq+w+D6anvnPt+khP+cJgH/JMnOt0+qlT46gW3/rg/ZnqlobCgmZkLZgBYyA7yhv0P4H4eH/PG+Nno5eYesPGsTm48GgLJeD3uwM+MTcTDBFTrjSOXd+4s1K5t7E1t69hTqhvefKtWG/LmIqKaK58bKr2kXH8FJ7wme1efE+5E/MfHJGm9qRoToRthTjpEunCFF5z3Jf3FvT0dIeEMJ5IoA5hhkdyef/K6nBigN9gWWZgVuZBWCxBHR536zk1A0QnKTmGs/XivLWm+deTum5sbhzUN9FRc52dOIicnpF8R3+hC5l9Olo6NuHnQn7GevTNzfH5TD/F72BmZjfWcFeGshnKcy4BBhgVSKOJyv2lkiEBYQFpg6Ddgx92VUQRIdUkYmglumggWr7fW6+oMKcFOBM605zqLOkt5jnVQHB899TSQqUihRkBGuerqZTv5MqGMytf6P8jhxH/T3bFVdIGwkY3DYfJyrLOcscH0abRx5xSHGRi7jpmnXd2ti9uZ4ZL0KrU46XlW/duLaxVNOeBbivEQgTMNaV+840Xqfu/uHnzo8l7j9eqRmUKb7qaMi8Ue748evq33/6QCkbFw2360d5BFGqFmQkYBH1VyCFAJtGwl0+mF851F+Wgv/nJZ88N9ba1NN69cR13Ud5CNx5J4j//5OPbs/N/5Y03pisVuId3u7tU0fjadIGJ5QqKd39H29WJ+x/fvQMOYl2pe3yGkVLno5VV6FJHoaN+ILS01MefO3XiV164fGtu4drjGQxwanq0PYtcgV3K5NLpymJpHfUBOhv9xjPjvYWOW7Pzqgorta3xckna9u70jNRc3Si0xn6ptVkaXiPrX3/xuc9vbl9/PNtbtGItHzx8pGs8pQP+AF/aJF8OmCsDVaQ71HfwGUlhoORqTPQXZHp9IDn+lAvVFSEaACq0AUH5iSpyHRrN6ZWoCwQ01FR88X2eIAN4BmQ5XPPkR4Dggd+RhHBUnbdoHuCnGG8W66PphXIh15du5to8P375G6++xOargZyvrL03ObW0c4CfxwDBpkycUfIOJZBxceoSEUphN6/togllaweNqZnH6ncYgCdauLa5ppxLn1UnjHwbkEAhG7I2t6YY09toHkXbgr/CwfNG9ID1sSae0NH1EV8ItT7qbt6a2YnvJXo5UZLJ61kJCsf7/X+/haZIliq5QKRevW68fzCMg35DjRoDZxhAT4fzGDzOxe29abqK/lXud5zyVJFsSG4UixtXS8DmeDEVL8mF2YNIjcbH/P3JQ/lcfNfv/pd8WedEhTVwkM7091869xT3OuxrdeXB3NzHk4/e5y3vH6rUSGqmfDo2Pq4jImQ8zMnRpqYlpdysmFGP3S5bxq0G1Dza3KEBv3dnEsJT7OokV4lZR1fGQ2iGnFzs6f7bX/3ieL71t967JoAyQwk12KazH+Ya9WxURZuPlxb0vIOPw6POjp4MxDHdur6xfndujjE+26/pixp+k4t6h3r7TPuZW1nMt+elcGfXqkhfVycnXz0/TsgJsOnQWr94DC+sg3F7pp3YYJCP9Q/F2gdC+GD+o2utew1tHsRbMK6fbKKBiOrvEohfapq7NgUoj2J4aTCea4AKxIPrcj7f8VxvP08Z+MCiT2xuTnGuZZVUgjWn0Hu8pp1gHohR7Sg2lPv1yBg1lW7p1oKhm02s5r4HqIfRZ3gCGeY7guYJhm1r291rrm99ENaiMZBte76Qf7nX0FnZ6bATqShKbdSlB5aXCGx8vqOg7OOD9eq7i4s8CexEI/BMeTyfz31heGC3KaRXS00rDA55tLqCXMT63V5aWHuoK4SkWu60SreOvGIdytg5beTaId4mDLp6a1v758bGfi6jI0VOgPJ4bdU4oEU8/71dGPjp7nKj3LMUnNG9dKnLKK5oaWClSDn3EGYCRJXlkNU074WSiSLp0D9RcO9t8dp6FDqEajkeK5dhR7CLETTDFvtdRJWZX1/ZrW5MLhnMG3OajIj6+pfe0BJ5YnbWvv7JzbtTS8vf/OCjd6Ye311eY3uf7S1dKOV/5XOva3WkI9v3b9zcqtcGM9nzfaW5jc37XOk0VPFQ4WjIaiQGDwBIhIA9WNrefXqg92tPnRVDzKyufefDj06WCy9ceOrciTM+MDU3PTk/j/PjGDs/txce/cXE+/QUWpZg/EJPSWzLuGt/3ZkvPF6t7h7vldtbT5VLP3P5Mqz/x3fv3l1e15vixsoanOs7tya+feOWoFWBD59boFPzAwdHI4X2L54aNRjWZPmF6hbbvrK9e3dhRWELZ/9ib+FSuYRFqtrl6y8/j0SkRximqVLejfpOdWPt6ua6wzNe7uzV5Lm/92h363vVjbN93apb59Y25tYjNmpBvAnFZ5CvqcsxasaWBXgQKxLjDJlqBsOiEP1QLr7C7wxd4jff99QccFiN2BqcSrDYHs6/UNrlE8URrjTDH8JELT3hHSYq2Z9pLAnkJ1ejmMi00A0R6JcunLnS1z2zXv3eJ598/pnnTg/pvFB487ObOFSsDM9cbvUbT529fPrUH169/qPZpZaAaxIOpbbMspfwIlDp9l42RsyF8hM9cnVfOtc4vHt/cXqeQ7J92PTN9z+6v7yyUA94WpQWbNr4dAT+ocN/8r7hSocjHOo4qlKsjNPoab1d/AqnPrpL+kXTeUWsl/A54wo6JQT1i+rxeDAx/3lbn6I/YlGikaSOoZmnerrOlDv7S1EitwmzXljeP6rAJeBdVGEo0+QHk/+PW/pywSdanaR56jCjsaDx2P7dgyVa3x8i7xrfiV8RJewdNvSJ+4Vnzlw+fxGBYXfXRIGa6Xiz65uL2weaerLdLF+CIyUXjKvbxOY6Z3l/++994cXXzp5mdVeraydiznCBy7j5nTcVsq03jpTaK5jSSQa/OPy8hM+G7G+WynMd2en5hd+fXby5VtNUY3XjsL1YONGZt1Zya535Lob2wqmzthj/q7esf7AHb9qord15PDVf24JwFtvSA8WusaExwOb8ykJnrnDh5DiNTFV2dy4vXr82t7H143sTYCMUuIRWl6nvbw2UJJg73p+4/fK5i3LX79y+WS50rdQ2bywuG2M3km4biOqNnZkdgw+4WaH1dPfa3D4QtVkJvLttZ7m1bbSrINrWTQgiL0VAeX1U2/juwylJyqF2bbdNK2n7teFhoQkzCVhf2ty88fjhm3Ozeo3sM54qp8P5TekUpJpHeQCXhfJFfqUkcZ0/V+wCzd1S9pIU8Sp8IWayx7a+uy3zfKl0orOYYzBwMbe2NANaXa2t2GYlPmaEMGD7WIgZ05/2Gvqfp0DNuPkHenxVlmf3DuYEBzpdU8hJqZB70hug/NWqPjHHB23txjiLRDUbfmvqQfsBHlRWEVN3NsvBF2ccH5gccLDYaIzmO41KxZ2Jfpetrafb2xo93ThDxNMMxJsz0+/OzVQPDv5ffvNQFu4bdD0AAAAASUVORK5CYII=");
                return m_HeaderImg;
            } 
        }
        private Texture m_ReviewIcon;
        private Texture ReviewIcon 
        { 
            get
            {
                if (m_ReviewIcon == null)
                    m_ReviewIcon = UI.CreateIcon("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAD0ElEQVRYCb1XaWxMURT+ph2GqUpF2libNtQyQlClIQSR1JYgokSkPyxVS/1C/KA/CJFUxBaRWEIJIVEk+FGSirYSZUgItYWOEkuVGqP7tL7zdKZ3xps3ozPpSb73zj33nu/cd+57595naruDiIhpemg0ajzxMamG0Ci8o9KolRE1hOgfiP8W8397dDgcoNqN6Ff0AGUZWzCBenVHt76mPnA4GZhO+mI1xLRN2Mf2FtWm6ncPAW1tTLlJtQKdzUCuLw2QlYHMNfnYT/sn/z5P2z94ZzMwnoR2D6nnXtcAd2o2trItk/CRioK/zUhlYJMPe3vD2gPRGxYhJ/cgjtP0S29MJDKQSGKHHrnYqmvRmJiJHKqnpS1SX/T3Lle9DER1dIekrTMaFR8Hy+al2NjQhCgCAlXCzcAgkr0ieqqk/nrlZ9QlL8MS2m+qn5yM08uAXiGSAMnEYGIoMbZdl++8LxFUPtXA2b8vznDgS+I1UUW8IxoIr3i+Agst6cQ2Io6wEb2JiEprK5xcgmdEJYm/ETJBu2RgJ5W1RALRZeKqhzN2DtLkJaz+7kRLl0VuD1T1FSaqe6LLK1BeWALXwqnI6G5GdFdM5MsPNE7MwSMWL4fpQp435KQFU1Da09Lp8uwlMlJqnGgauwoPP1Rru+cq7/fKb/b+xWLMbGqG24ggnL5aF5pT12jBZb+Q984VZbUACkqulmJ2ixut4QTS8/1Vh+bJG2B3fMFX9mcTtTJOzYBWuZiJ2+dvY4EUjUhJfSPc6ethr3Bo5wWppt893FEWHil0cP1SMRZ7BoVz55K2Ts2F/XklfpBH0v5Z5TNPkrKjL4Usq8eS+mmbi/6IEKyHC+Gwv9LSLZuUrL2PmBnASGTWYUlTi/Y+yYngvR6RFCIjCZwfIy+lz5aEGDYDFrpgExiicHVKTUxADzomBXI2OhPK4owK5BiqPbk/Ynhakp1VV4wyMJIeUq/Dkrhe6JZug41l10zAH0YTSAkW+elbOF+81z//qb5DBmg7bbJq8+hGExjtGeR///kbzavz8WzMSjwamYWCvFO4wycLWMJHJMJKjgH+PNI2msA/6y8levdZvImbh7KTN/CG/rKVbdxVgBkxszH3yBXtyCa8PjIuBbE06L4HRhOIV1nO3cLHPvNRuv0kKmk/SiwkSgjsyJIrig5dxvBhK5Bzrcy32sVatW1+uDbK/yIHxwDYRntb6RF8GzoQ9+gnv2LyR2QhgknMrFTkPz6BWuHgX9MTOvzzwxKMRPqXExJ4L+GTEba90p4BpPDcLFBEWvwr1DgmK3av+gf86jED3McCCAAAAABJRU5ErkJggg==");
                return m_ReviewIcon; 
            } 
        }
		private Texture m_DocIcon;
        private Texture DocIcon 
        { 
            get
            {
                if (m_DocIcon == null)
                    m_DocIcon = UI.CreateIcon("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAEzklEQVRYCbVXTUhjVxR+L8nExtHEn6KtadMZ7DCLQVtShC5MRYrVYkFEQUUQxYWLrkRdjGhVxFIcLUPp1k0XrlwquKiVUFroDJV2OiJjFVRSicaf4kz+TGLmO7fvPl7eezeNZXrhcs89f/fknPPd+yLv7+9LZiOdTjO2LMtmYgOP63N/DocjQ2dtbS1jzzcyN+AMZf0eDutB4/ycIngOfRv0/ZifKj5yWsjIbLyJcy3RaDR5dXVFUZjpMB4OTttstpt5eXkWMJr29vYCWN8SGugEogBeJz2v1/tzJBJ5AfIKUxjF9PT0uz09PXeXlpYCDQ0NZeFw+AT6zAfWrEMUQJSshoeHPV1dXQ/z8/MzC6pxiezsIUudYN1dX18/Gxoa+gpl/fbo6Ohv8Io0qqakDRk0E7ygtPf399+C8KGZAueRPS+R2+3OA/8R9nfKy8v/PDk5ucDeyXXNVlEGmNHc3Nx2MBi8dLlcIj1pZ2cn2tvb666vry+zWCxUpjKPx/N4YGDg/dHR0d9CoVDWIGw8el109EukhYWFIHrga5CnmIXEMxsTExPN4H+u+GK9gv75fXV1tbqpqenJ8fHxc8hN7WV0rZnPp3B2z0xwTV4QJXLAl2txcfEJbN/T24syQJCSUMNYKpWSkFq9nbqntBNcz8/PE8XFxXZcQFY0JWusRCLhQgPLRUVFUjKZdI6Pj/fA8DvVGISotqWk5PP5HqEEQZApTHEU//ghuJ5jlmDexExiSlNTU3fQI15kgkpjJZ52iAKIkNLY2Nittra21dcwtEZEwyEdgOzKeqfUdHz8BT3qJ6/CMNwlIhiG4Vjq7u72wPBL7k27kjyXwfUQCJXFgHlRBtgFMjg4+Mfh4eElaqvqAVaJyspKx+zs7D30SBwIeFZYWGi12+2GEm1vb0f6+vrcjY2NbyAQitgYAI9Q92tu0B4wOkcP3AdJV6sKI9wLdDVvxGKx5PLy8jPQDzDzMZkdVnXMzMx8gs2IwjCkTVSCEAIr29ra+giGP6nedMTFxQX1AfXLY52IbRWIf6CRGQMQZICl8/T0NK5/DamUVqtVLikpsSs3H3U/G4I7hYtNV7W2OimDYW1t7S8oQQiyDBiWlpZaNzY2WgsKClQEZDmcyiUcogAYDNFgt1tbW39Ag9m5B2QsDJqwLjmdTvv8/Lyvvb39ATKjBqPRPQZNZRQOUQ8wGHZ2dr4Ny0mRNQK4gcOrIa8WlFI1/U8wxPfA00AgEEfKDd3Nr1ulD9SDtMTm5mZ4ZGTknebm5goEeH0YrqysEAxH4TQDhppDOK4N3c11ampqPgP9hbI36IlKwGHog+GP3FkO6yVSza5oRZcakD3tSgmMAQhqxxrq4OAgjBctDdhlPRvOJbya6YqKCgdeP7qQRMOACBEKWJfjCv0VJdiFN/pV2aPANYtbsaGqqsojOt2MLwqAwXBycvJ2S0uLH4+hCkMzJ8gA/TJ6ij80k2fjiXqAwbCjo4NgOJ7NAckEZfw3MyY3vGA5Wb1CJdEnWYzOiMfjqVdwlkz/mvBJxiGb4dIGQQZD2XxcV1f3Db71KnHRxM0UrsPDXzf57OyMmtjwgwy41Dim7zpqUgN0NDrXISkAekcSWiN5d3dX3eNXS36/n+3x1aPy/0/iJfpMtnfwrhsgAAAAAElFTkSuQmCC");
				return m_DocIcon; 
            } 
        }
		private Texture m_FaqIcon;
        private Texture FaqIcon 
        { 
            get
            {
                if (m_FaqIcon == null)
                    m_FaqIcon = UI.CreateIcon("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAFRklEQVRYCa1XXyykVxT/vjEo1qKxSUuxiSUxTUtS3T5oMgm7NmM3EtKkL0v6sjw0G2lVmj548lCLCtG0kRINb4ZGttXSbiTYJbPjT2xaFqNhsBjs6CzKjD/9nc93ufPNmD/Sk5w5/++937nnnntHnJ+fFwgSEhIkyn7MZrMgiqIkHh0dCUFBQcLBwYFAvBKY3/HxsaBSqYSdnR0hNDRUcqOYmZkZp5CRkZFTWaSJ4uLihIWFBWkRPCUvGlCtVhN7BYPdwQK04DXAGGAI8BAL2ASdwwIM8O8Bb6TFrK+vC1FRURAFobe3V6Lsp6KiQmJFcmSwuLgoLYZk4mW4hkm/AH8XE4UxpSe6u7s7igw8gI+ez0BnZ6dTWHl5uSDSFlD6+S9nMry/BH6NiU/2wincu4AMGJGBj+BpPs/bKQOcUwC25mfIOk53IRYZcKAG7iK43d0AUgbcGAz46Otu9BdWdXd3f4bgOuUAIqVeAX9AvqHQOYlra2t7JpPp1crKyj7gKCIiQp2cnHwJeNnJUSGg8D6GyikTygV8DodvFHGSaLPZHIODg+sdHR2Wvr6+LShXgCbgGtAODNfpdBmlpaU3k5KSwiG7AGpiPz09PQmG0wrnt+BNpP2FS5SsmJub287MzHwE8TGQsvSMCpjVJ9dHNKOjo0PR0dERcqgTaW1t/ROKd5iSX0ALBvuEGdxRfN1v0OfU1taSWYvjmYoYahIDQKm7yMf6NvS/kJMSDg8PjzMyMoqh/4Fs4tTUFNHwkJCQLQSpSDgPkIVX2PMfU1JSdPC9xvsNDAzcg9yUlZVF6isOh8PC23m+oaHBVFNT8y50/6qoZQLzvE1OAyQmJoZrNJr7ysnJNjk5+VVBQcH1mJgYAVk4625kVEBOTs4bUN0hNftij1WviHcRNzY29pqbm6koE8mINL/t4sQp4uPjQyFmk4otQMPZ/WKnp6dt+KJRi8WyjMBuCkZtVHsaBHbK0FXyUVG2gJQSvwEXzGp2dvYY+sIEgouANpyMh9ii9z0NhgyRWfqhCiagW80vMBqNm0VFRc8RRCejamlpKRkD94NP9XGgkwVgteR/4GOQ5EYpLCkpmYZgAFahm76HyR+Df01y8OOH1YDVjxi65/eXl5f3EVMlx9WD+j05xbIFUEv1GQICAkStVmvHmyEJ+AEC43wOhmNgYCCbV1DLR5Zuv9u+DoI2G4yWegtbccvXGN5ve3vbATmIdOwU6HkHX3m8/fyqHTbu8PDwJngbySp6MgGf2+32SebgjW5tbdkLCwvH0tLSnmArHo2Pj/tVQ01NTdQzhmkeFb12ZaQHg0/Q1dX1or+/fwGL/hTnXre6uip1NV+CabG4Lbfh+xP5q/E1LO53ME8jIyO9voRQL3R2HwL/kh+v/6Ae2DgeaVlZGR3fMeDf5KjGLUiUwU1shxXP8NMqZQae5ufnx+BFJN2GKOLXgd/x9vP4+vp60+zs7B7sNcxHHBoaYjyjN2JjY+nB4RVwNZuDg4Mj4XjZmzO1bblzfgvfTuYvNjY2Mp6nxejx3+NPhtQmecNF+J6enpXi4mJKfQ/wAT+GWn6Q8DriG6G35ubmNuMNcElp9EeWHx9LiPkV6HJLevvCq5WVlXosJDUsLCzQn4lx1jeqq6vn5YqnGnHba0R8odDW1uY0dnt7uyTX1dXRK4j4ory8vPs4829RRuRT4BRDgtVq3TcYDC/1er0FtUWNhi6oFqBU8aAucJoBZS1gz5TOYVBQ6/0QSCcgAOgOXkL5FEiFTNe1RxAnJugtcQItLbTYM+AyIOBBemb4H7n/AIKWOUx2b5M6AAAAAElFTkSuQmCC");
				return m_FaqIcon; 
            } 
        }
        private Texture m_DiscordIcon;
        private Texture DiscordIcon 
        { 
            get
            {
                if (m_DiscordIcon == null)
                    m_DiscordIcon = UI.CreateIcon("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAElElEQVRYCb1WbUxbZRS+t7TMQfnoYMIYA8rKwkLWaSTMqH80+7FFs8wM94FfhGXZD0O2337Fjx9qdFmMxsQtatTMMZdoZh1uEswWzOLClH1RGbD2Im3pQCylXytbW59z6VvfW+6FgoyTPL3ve85z3ve85557+oq7900K85XBPz8XLOtb5nSLx6cEnS47xTv97UOpMRuICwmAOc/n6Ru/KtPPfveYwk2nmGU4oQxkIpQBJqYiq0CgA5uKNwp5BTUyljwDLCDKBAWhZ4r5PBdSA3R6vgZo84B/QMg0A/kIcBPwBNAB/AJoyXswuIBOwM5IWjUwVwA7sMAeYAuQS4vdvRPySYMnDuIr+AfTAoDq6DZwC6gDPgFkQQ0M4iv4BpPjQN+0Vvk7WwAXQW1Q0hc+QwZegncqOLaSVg28AsKibU6bFa6o+7j9ZIMDwzM0Z6KWgWIYxxhhMZ8u6UfPrx1Na7EmvTJZ1DLwLjMu9rO86qky9IBXsS5BlvRGZIJ2b9J2Tx7rrQeafX9fKQIEQnoA93RzOtEa8/ayZfcVtQACYckDEHV6sXJt4wvR2+PLAEUAFgRYq5V3x42vhn87t/9af++nkhZncqI/0N3V2tvddaA34L8Z1OKtqthyP2zyvxJfhJu1HPqufui4fPG1v2BvkwaOXzMYjHvN655t4fnhkCvSfrL+d+j6gWM3+77QPf2804Y0G3kejVcUP0AN7FGgkw+gnozpEo9FY8nNj8JGXU2otDxzAV2uEV2OWrQszhvH3Bi4gP2kSP7Nv40htWaFZGcXGKCwkpIPwKxgJSeJRJxGUUDenLs/hKFLBRCLRYl4CuCF/GZIPH4nASVlQVEDZTOYUGTpl2fVWltLAXYqotF/QykNmFRZdq7CWL4AcEHO9lXJXD4D1ANUZWPDW1bP8M+HYTwCPJh8Krj5ptq8zds6DheXbJJgEIE3gA3ArMIH8N/lLc1FFLPE1RVbD0JN0BRsXg3jFU2CioHvA/Rellz4AOT/+/QIgpOOkHuo3Zuun2se8A8G3UOn4TfzXFRXzJ9/Bc2xu5GvYeR1eJs6wevqvD4+2n02x7jm8ZWlDxfmGity9AajkickEpGwN0rNaNTTNTE+dilgrmnCRmIJNqOaSMktz3n6t80hhbiy9JGUAYPtqPaPVlc+Wc4raRwKDNlsbRvewbBOb8itN+aZLWgyhaIuS8QtKR4OuqbCITd9dl7YnY3NIy+THy+R8EjE3nPIOWA/Mgp9F/C6IrIkOb+8atubVTW7dqHwStC75dd0/syOnpHhDuoFHyR51Ezo7sBfy+iaNrF7n58oP+F89LkKwYAUcvR96bZfPuTBdAT4DKA74/SltO1ovsAyMea9QHqSanxaL1ave65pasoft/e8T86twHUyagnXA0wuydYuDZxY7pJ+8IHvBL4HbLyv4kZEgZAgAwKcGK8Qg60Adb5TTJnhMw+8PcAl4A81n/QaELgMCMiAMOmbeZnFO6bbsdp6so5lgR1Ik0gGRuZJlIH/I8kayGgJucAoCKoBVgcs/ZSB+cr0gUTVg6mtlUkNqPktmu5fRzyST440lD0AAAAASUVORK5CYII=");
                return m_DiscordIcon; 
            } 
        }
        private Texture m_ForumIcon;
        private Texture ForumIcon 
        { 
            get
            {
                if (m_ForumIcon == null)
                    m_ForumIcon = UI.CreateIcon("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");
                return m_ForumIcon; 
            } 
        }
        private Texture m_TwitterIcon;
        private Texture TwitterIcon 
        { 
            get
            {
                if (m_TwitterIcon == null)
                    m_TwitterIcon = UI.CreateIcon("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAE5ElEQVRYCZ3VW2iWdRzA8b1LnTs4U9FVzra5bB6W6WblYaQkREE3ItGBJLyNCC8CFSHspi666EZCKUiHnYSioReeJhqIWrrNLRPncu82dWpzraZTc4e+35f3Eba9h2f+4MOz932e9////f7/3/NfJCMjIxM5mIAIgvDvobj7XG9jEA8TjuX42XC+u7gDx88owCeI4kZcD9d/4PUKfsJCPIKxhpNPwXrUowUfYSYyHfA/WN1KlCAXfj8OZj0Jfj8HDehCLHOuYcLVfRkf4yk4nyv5N9qdyA9WqmfhMrWiCZfQh2lwpVy+39GLsGHyn6EMzuHvj2IvuoMlNSsrm4F5uIUvsB2/oDTuca4XYWL2RbpwFefidYzHOdRiF65jVDjRfrg8ZlgOV2QV6mDznMBSOHiqsLhF2IMo/N2nKEIEsQhWIPhsZ3biBbhfrkoTzNYKHLAQ7utZuKSJwgmexgaswnlYgJW7vW57LEYm0M+3dn8WSjAZZn4ZUdi5JmYV3eiADTwEw4lt2mV4B69hIqL4ChYzbOtGJsD9WNM1ch3ASviDP3ENJuOWVMJ7VuK9XphEPt7ENlShDTbdbziMfxE6yniyAZ4D78PBDRv1c7j8nhubMBVWuhYu9z24OjVYB1clYSRageBBB3QbKmAyF9AOq7XquXE+Y6VPYDPmweT+wD78iKSVp0qgjx9ehWeDkxXAxrMxfU1vYjGKUQ4b16tvSjMO4nv4XNJIlYB76kSeEQ4+Gw7eCCsykSwsgfeehA3Zglp8A5vXcZJGqgT8kZPbA4/CiXy1/OwkJuGB5JsxB07k94fwHdymAaSMdAn4Y6t27518PmbB/e2EFXt9Bo/BV/NbnIbJp40wCViZE9l8z6MU41AHT0wbzsm8Z5/4nPe8po0wCThIP64hggqUwS1wJbx2IA9uk/c8zM7D1UsZYRNwEAfzrbDzfSuKYA+4PTarveE2mYAN2QwTM/mkMZYEPAGr8CpsvCmwavfbV80lt3LfmGJ4zxPQbUoaYRPIYoTl+BCVsLvHw8Yz6uFWuE2DWApfTatvRB8SRpgEbLiF8JR7EW5FK9wOE3Ar/kILTKITNqNvhvfcBp/3f8qoSJdAJr+wki14BVZ0EUdQg0KUYjpcheuwH7rgSvmfcyrOwO+GMCzSJWAlG7EGJuPhcgq7cBL+M6qCSebiV/haOpnXFbAh7Y1zGLUVqRKwqg+wDjaUy+j7/TUacAcu/QRYrUm4PU5kQ5rcRLgV9kwtTGRYJEsgn6es+l24pDaXe7wTrkBwyvlv1y1xYA+iJTDxswj6wt8vg8l4bpj4g0iUQA53V+M93EcHfkA1rNwqg3BPrdatuQ23w4Z1O+x+G9UJ34AJ9uMCHiQxMgGX0we3YgHacBA1cBIHSBR9fOlZUIr5mIXLuAR/4/ZUoBBBYgP8PSxMxuwPwAwdcA8cMBPpwtfVsyLoj2P8vRieIc/hONyqn2GisQhWIMKnYmzFSzA7l8pX6yhc5nQxyAOeej1wFa26AM24AYvy+xI4n81qf8Q++HA1HMC9tIrtKIeVhQ0HLsIOdMNJbeCO+NXmtTiTegt5rsA0vI21yEY7mvAlzNJGHEu4Wm1wO2ciPy6Hq03rSvlaz0CTCdyDzeL+eL63YjdOwwrGGk7ShRPwbxvUVbgCVyIavzp29v/0qmv/9is4ZAAAAABJRU5ErkJggg==");
                return m_TwitterIcon; 
            } 
        }

        private static Color defaultColor;
        
        private delegate void OnGUIDelegate();
        private class Section
        {
            public GUIContent content;
            public OnGUIDelegate drawFunction;

            public Section(string name, OnGUIDelegate drawFunction)
            {
                this.content = new GUIContent(name);
                this.drawFunction = drawFunction;
            }
        }
        
        private List<Section> sections;
        private int _selectedSectionIndex;
        private int selectedSectionIndex
        {
            get => _selectedSectionIndex;
            set
            {
                _selectedSectionIndex = value;
                if (_selectedSectionIndex >= sections.Count)
                    _selectedSectionIndex = 0;
                else if (_selectedSectionIndex < 0)
                    _selectedSectionIndex = sections.Count - 1;
            }
        }
        private Section selectedSection => sections[_selectedSectionIndex];

        private Vector2 menuScrollPos;
        private static Vector2 sectionScrollPos = Vector2.zero;
        
        private void OnGUI()
        {
            EditorGUIUtility.labelWidth = width / 4f;
            defaultColor = GUI.contentColor;

            if (constants == null) constants = new Constants();
            if (changelog == null) changelog = new Changelog();
            
            if (EditorApplication.isCompiling)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField(new GUIContent(" Compiling scripts...", EditorGUIUtility.FindTexture("cs Script Icon")), UI.Styles.H1);
                EditorGUILayout.Space();
                return;
            }
            
            Rect rect = EditorGUILayout.GetControlRect();
            rect.x -= 3f;
            rect.y -= 3f;
            rect.height = 85f;
            rect.width = width;
            GUI.DrawTexture(rect, HeaderImg);
            float backgroundTint = EditorGUIUtility.isProSkin ? 0f : 1f;
            EditorGUI.DrawRect(rect, new Color(backgroundTint, backgroundTint, backgroundTint, 0.66f));
            
            GUILayout.Space(-7f);
            EditorGUILayout.LabelField("<size=26><color=#fff><b>" + AssetInfo.ASSET_NAME + "</b></color></size>", UI.Styles.H1);
            GUILayout.Space(-5f);
            EditorGUILayout.LabelField("<size=12><color=#fff><i>by Staggart Creations</i></color></size>", UI.Styles.H1);
            GUILayout.Space(16f);

            GUILayout.Space(4f);

            if(sections == null) OnEnable();
            
            #if !URP
            UI.DrawNotification("The Universal Render Pipeline package isn't installed", MessageType.Error);
            #endif
            
            GUILayout.BeginHorizontal(); 
            {
                menuScrollPos = GUILayout.BeginScrollView(menuScrollPos, constants.sectionScrollView, GUILayout.Width(120f)); 
                {
                    //GUILayout.Space(40f);
                    for (int i = 0; i < sections.Count; i++)
                    {
                        var section = sections[i];

                        Rect elementRect = GUILayoutUtility.GetRect(section.content, constants.sectionElement, GUILayout.ExpandWidth(true));

                        if (section == selectedSection && Event.current.type == EventType.Repaint)
                        {
                            constants.selected.Draw(elementRect, false, false, section == selectedSection, false);
                        }

                        if (GUI.Toggle(elementRect, selectedSectionIndex == i, section.content, constants.sectionElement))
                        {
                            selectedSectionIndex = i;
                        }
                    }
                } 
                GUILayout.EndScrollView();

                GUILayout.Space(5f);

                GUILayout.BeginVertical(); 
                {
                    //GUILayout.Label(selectedSection.content, constants.sectionHeader);
                    GUILayout.Space(5f);
                    
                    sectionScrollPos = EditorGUILayout.BeginScrollView(sectionScrollPos);
                    selectedSection.drawFunction();
                    EditorGUILayout.EndScrollView();
                } 
                GUILayout.EndVertical();
            } 
            GUILayout.EndHorizontal();
        }
        
        void DrawHome()
        {
            UI.DrawNotification(Installer.HasError, "One or more errors were detected in your project", "View", () => { selectedSectionIndex = 1; }, MessageType.Error);
            
            using (new EditorGUILayout.VerticalScope())
            {
                UI.DrawH2("Thank you for licensing Stylized Water 3!");

                EditorGUILayout.LabelField("This window houses common functionality and installation information.", EditorStyles.label);

                EditorGUILayout.Space();

                using (new EditorGUILayout.HorizontalScope())
                {
                    GUILayout.FlexibleSpace();
                    if (GUILayout.Button(new GUIContent("<b><size=12>  Write a review</size></b>\n<i>  Support further development</i>", ReviewIcon), UI.Styles.Button, GUILayout.Height(45f), GUILayout.Width(240f)))
                    {
                        AssetInfo.OpenReviewsPage();
                    }
                    GUILayout.FlexibleSpace();
                }
                EditorGUILayout.Space();

            }

            EditorGUILayout.Space();
            
            using (new EditorGUILayout.VerticalScope())
            {
                UI.DrawH2("Extension assets");
                
                DrawExtensions();
                
                EditorGUILayout.Space();
            }
            
            EditorGUILayout.Space();

            using (new EditorGUILayout.VerticalScope())
            {
                #if URP
                UI.DrawH2("Quick set up");

                GUILayout.Space(5f);
                EditorGUILayout.LabelField("<i>Options can be found under GameObject->3D Object->Water</i>", UI.Styles.WordWrapLabel);
                #endif
                
                EditorGUILayout.Space();
            }
        }

        void DrawInstallation()
        {
            using (new EditorGUILayout.VerticalScope(EditorStyles.textArea))
            {
                foreach (Installer.SetupItem context in Installer.SetupItems)
                {
                    if (UI.DrawSetupItem(context))
                    {
                        return;
                    }
                }
            }
        }

        void DrawChangelog()
        {
            using (new EditorGUILayout.VerticalScope())
            {
                EditorGUILayout.Separator();
                
                changelog.Draw();
            }
        }
        
        void DrawIntegrations()
        {   
            UI.DrawH2("Fog integrations");

            foreach (FogIntegration.Integration integration in FogIntegration.Integrations)
            {
                if(integration.asset == FogIntegration.Assets.None || integration.asset == FogIntegration.Assets.UnityFog) continue;
                
                UI.DrawIntegration(integration);
                EditorGUILayout.Space();
                
                if (underwaterExtensionInstalled && (integration.underwaterCompatible == false))
                {
                    UI.DrawNotification("Editing of shader files for this asset is required to incorporate support for the Underwater Rendering extension. (See documentation section called \"Transparent Materials\")", MessageType.Warning);
                }
            }
            
            EditorGUILayout.Space();

        }

        void DrawExtensions()
        {
            using (new EditorGUILayout.VerticalScope())
            {
                EditorGUILayout.LabelField("Installed", EditorStyles.boldLabel);
                for (int i = 0; i < Extension.installed.Length; i++)
                {
                    Extension extension = Extension.installed[i];
                    
                    //EditorGUILayout.LabelField(extension.name);
                    UI.DrawExtension(extension.name, extension.description, dynamicEffectsInstalled, extension.assetStoreID, extension.icon);
                }

                if (Extension.available.Length > 0)
                {
                    EditorGUILayout.Space();
                    
                    EditorGUILayout.LabelField("Available for purchase", EditorStyles.boldLabel);
                    
                    for (int i = 0; i < Extension.available.Length; i++)
                    {
                        Extension extension = Extension.available[i];

                        //EditorGUILayout.LabelField(extension.name);
                        UI.DrawExtension(extension.name, extension.description, false, extension.assetStoreID, extension.icon);
                        
                        EditorGUILayout.Separator();
                    }
                }
            }
        }
        
        void DrawSupport()
        {
            UI.DrawNotification(AssetInfo.VersionChecking.unityVersionType != AssetInfo.VersionChecking.UnityVersionType.Release, $"You are using a {AssetInfo.VersionChecking.unityVersionType} version of Unity. You may run into issues at own risk.", MessageType.Warning);

            using (new EditorGUILayout.VerticalScope())
            {
                EditorGUILayout.BeginHorizontal();

                if (GUILayout.Button(new GUIContent("<b><size=12>  Documentation</size></b>\n<i><size=11>  Usage instructions</size></i>", DocIcon), UI.Styles.Button))
                {
                    Application.OpenURL(AssetInfo.DOC_URL);
                }
                if (GUILayout.Button(new GUIContent("<b><size=12>  FAQ/Troubleshooting</size></b>\n<i><size=11>  Common issues and solutions</size></i>", FaqIcon), UI.Styles.Button))
                {
                    Application.OpenURL(AssetInfo.DOC_URL + "?section=troubleshooting-faq-3");
                }
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.Space();

            using (new EditorGUILayout.VerticalScope())
            {
                UI.DrawNotification("Be sure to consult the documentation, it already covers many common topics", MessageType.Info);
                
                EditorGUILayout.Space();

                //Buttons box
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button(new GUIContent("<b><size=12>  Discord</size></b>\n<i><size=11>  Access support</size></i>", DiscordIcon), UI.Styles.Button))
                {
                    Application.OpenURL(AssetInfo.DISCORD_INVITE_URL);
                }
                if (GUILayout.Button(new GUIContent("<b><size=12>  Forum</size></b>\n<i><size=11>  Join the discussion</size></i>", ForumIcon), UI.Styles.Button))
                {
                    Application.OpenURL(AssetInfo.FORUM_URL);
                }                
                if (GUILayout.Button(new GUIContent("<b><size=12>  X</size></b>\n<i><size=11>  Follow developments</size></i>", TwitterIcon), UI.Styles.Button))
                {
                    Application.OpenURL("https://twitter.com/search?q=staggart%20creations&f=user");
                }
                
                EditorGUILayout.EndHorizontal(); //Buttons box

            }

            EditorGUILayout.Space();
            
            if (projectDetails == null) projectDetails = GetProjectDetails();

            using (new EditorGUILayout.HorizontalScope())
            {
                EditorGUILayout.LabelField("Project details", EditorStyles.boldLabel);
                if (GUILayout.Button("Refresh", EditorStyles.miniButton))
                {
                    projectDetails = GetProjectDetails();
                }
            }
            projectDetailsScrollPos = EditorGUILayout.BeginScrollView(projectDetailsScrollPos, EditorStyles.helpBox, GUILayout.Height(280f));
            EditorGUILayout.LabelField(projectDetails, UI.Styles.WordWrapLabel);
            EditorGUILayout.EndScrollView();

            using (new EditorGUILayout.HorizontalScope())
            {
                EditorGUILayout.LabelField("Supplying this information is always useful!", EditorStyles.miniLabel);
                
                if (GUILayout.Button(new GUIContent("  Copy to clipboard", EditorGUIUtility.IconContent("Clipboard").image)))
                {
                    EditorGUIUtility.systemCopyBuffer = projectDetails;
                    EditorApplication.Beep();
                }
            }
        }
        
        private static string GetProjectDetails()
        {
            StringBuilder stringBuilder = new StringBuilder();
            
            stringBuilder.AppendLine($"Version: v{AssetInfo.INSTALLED_VERSION}");
            stringBuilder.AppendLine($"Install location: {AssetInfo.GetRootFolder()}");
            stringBuilder.AppendLine($"Unity version: {AssetInfo.VersionChecking.GetUnityVersion()}");

            UnityEditor.PackageManager.PackageInfo urpPackage = AssetInfo.GetURPPackage();
            if (urpPackage != null)
            {
                stringBuilder.AppendLine($"URP version: {urpPackage.version}");
            }
            else
            {
                stringBuilder.AppendLine($"URP version: NONE");
            }
            
            stringBuilder.AppendLine($"");
            
            BuildTargetGroup buildTargetGroup = BuildPipeline.GetBuildTargetGroup(EditorUserBuildSettings.activeBuildTarget);
            
            stringBuilder.AppendLine($"OS: {SystemInfo.operatingSystem}");
            stringBuilder.AppendLine($"Platform: {EditorUserBuildSettings.activeBuildTarget}");
            
            string scriptingBackend = string.Empty;
            #if UNITY_2023_1_OR_NEWER
            NamedBuildTarget buildTargetName = NamedBuildTarget.FromBuildTargetGroup(buildTargetGroup);
            scriptingBackend = PlayerSettings.GetDefaultScriptingBackend(buildTargetName).ToString();
            #else
            scriptingBackend = PlayerSettings.GetDefaultScriptingBackend(buildTargetGroup).ToString();
            #endif
            stringBuilder.AppendLine($"Scripting backend: {scriptingBackend}");

            stringBuilder.AppendLine($"Color space: {PlayerSettings.colorSpace}");
            stringBuilder.AppendLine($"Graphics API(s): (Auto:{PlayerSettings.GetUseDefaultGraphicsAPIs(EditorUserBuildSettings.activeBuildTarget)}) {String.Join(" -> ", PlayerSettings.GetGraphicsAPIs(EditorUserBuildSettings.activeBuildTarget))}");
            stringBuilder.AppendLine($"Tessellation support: {SystemInfo.supportsTessellationShaders}");

            #if UNITY_6000_0_OR_NEWER && URP
            stringBuilder.AppendLine($"GPU Resident Drawer: {UniversalRenderPipeline.asset.gpuResidentDrawerMode != GPUResidentDrawerMode.Disabled}");
            #endif
            
            string VRProvider = "";
            #if XR && UNITY_2020_3_OR_NEWER
            EditorBuildSettings.TryGetConfigObject(UnityEngine.XR.Management.XRGeneralSettings.k_SettingsKey, out UnityEditor.XR.Management.XRGeneralSettingsPerBuildTarget buildTargetSettings);

            if (buildTargetSettings && buildTargetSettings.SettingsForBuildTarget(buildTargetGroup))
            {
                if (buildTargetSettings.SettingsForBuildTarget(buildTargetGroup).AssignedSettings.activeLoaders.Count > 0)
                {
                    for (int i = 0; i < buildTargetSettings.SettingsForBuildTarget(buildTargetGroup).AssignedSettings.activeLoaders.Count; i++)
                    {
                        VRProvider += $"{buildTargetSettings.SettingsForBuildTarget(buildTargetGroup).AssignedSettings.activeLoaders[i].name} + ";
                    }
                }
            }
            #endif
            stringBuilder.AppendLine($"Active VR plugin(s): {VRProvider}");
            
            stringBuilder.AppendLine($"");
            
            stringBuilder.AppendLine($"First available fog integration: {FogIntegration.GetFirstInstalled().name}");
            stringBuilder.AppendLine($"Underwater Rendering installed: {StylizedWaterEditor.UnderwaterRenderingInstalled()}");
            stringBuilder.AppendLine($"Dynamic Effects installed: {StylizedWaterEditor.DynamicEffectsInstalled()}");
            #if URP
            stringBuilder.AppendLine($"Render feature present: {(StylizedWaterRenderFeature.GetDefault() ? "True" : "False")}");
            #endif

            #if URP
            stringBuilder.AppendLine($"");
            
            stringBuilder.AppendLine($"Depth texture option disabled (anywhere): {PipelineUtilities.IsDepthTextureOptionDisabledAnywhere(out var _)}");
            stringBuilder.AppendLine($"Opaque texture option disabled (anywhere): {PipelineUtilities.IsOpaqueTextureOptionDisabledAnywhere(out var _)}");
            #endif
            
            stringBuilder.AppendLine($"");
            
            stringBuilder.AppendLine($"Async shader compilation: {EditorSettings.asyncShaderCompilation}");
            stringBuilder.AppendLine($"Caching Shader Preprocessor: {EditorSettings.cachingShaderPreprocessor}");
            stringBuilder.AppendLine($"Strict shader variant matching: {PlayerSettings.strictShaderVariantMatching}");

            stringBuilder.AppendLine($"");
            
            Shader defaultShader = ShaderConfigurator.GetDefaultShader();
            
            if(defaultShader == null)
            {
                stringBuilder.AppendLine($"[Failed to find default shader!]");
            }
            else
            {
                ShaderMessage[] shaderMessages = ShaderConfigurator.GetErrorMessages(defaultShader);
                
                stringBuilder.AppendLine($"Shader compile errors ({shaderMessages?.Length ?? 0}):");
                if (shaderMessages != null)
                {
                    foreach (var t in shaderMessages)
                    {
                        stringBuilder.AppendLine($"• {t.message} {t.file}:{t.line} ({t.platform})");
                    }
                }             
            }
            
            ConsoleWindowUtility.GetConsoleLogCounts(out var errors, out var _, out var _);
            stringBuilder.AppendLine($"Scripting compile errors ({errors}):");

            return stringBuilder.ToString();
        }
        
        private class Changelog
        {
            private GUIStyle changelogStyle;
            private string changelogContent;
            private Vector2 scrollPos;

            public float maxHeight = 500f;
            
            public Changelog()
            {
                string changelogPath = AssetDatabase.GUIDToAssetPath("793931a466dcf8743a80dc24cd9f9a56");
                TextAsset textAsset = ((TextAsset)AssetDatabase.LoadAssetAtPath(changelogPath, typeof(TextAsset)));

                if (textAsset == null) return;

                changelogContent = textAsset.text;

                //Format version header
                changelogContent = Regex.Replace(changelogContent, @"^[0-9].*", "<size=18><b>Version $0</b></size>", RegexOptions.Multiline);
                //Format headers
                changelogContent = Regex.Replace(changelogContent, @"(\w*What's New?|Added:|Changed:|Fixed:|Removed:\w*)", "<size=14><b>$0</b></size>", RegexOptions.Multiline);
                //Indent items with dashes
                changelogContent = Regex.Replace(changelogContent, @"^-.*", "  $0", RegexOptions.Multiline);
                
                changelogStyle = new GUIStyle(GUI.skin.label);
                changelogStyle.fontSize = 12;
                changelogStyle.alignment = TextAnchor.UpperLeft;
                changelogStyle.richText = true;
                changelogStyle.wordWrap = true;
            }

            public void Draw()
            {
                if (changelogContent == null)
                {
                    UI.DrawNotification("Changelog text file could not be found. It probably wasn't imported from the asset store...", MessageType.Error);
                    return;
                }
                
                scrollPos = GUILayout.BeginScrollView(scrollPos, EditorStyles.textArea, GUILayout.MaxHeight(maxHeight));
                GUILayout.TextArea(changelogContent, changelogStyle);
                GUILayout.EndScrollView();
            }
        }
    }
}